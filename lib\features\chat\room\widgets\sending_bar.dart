import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/constants/recording_status.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import '../controller.dart';

class SendingBar extends StatelessWidget {
  const SendingBar({super.key});

  @override
  Widget build(BuildContext context) {
    ChatRoomPageController controller = Get.find();
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 2, 16, 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Expanded(
            child: Obx(
              () {
                return Stack(children: [
                  _MessageTextField(controller: controller),
                  switch (controller.recordingStatus) {
                    RecordingStatus.recording =>
                      _RecordingWaves(controller: controller),
                    RecordingStatus.finished =>
                      _RecordedVoicePlayer(controller: controller),
                    _ => const SizedBox(),
                  }
                ]);
              },
            ),
          ),
          const Gap(12),
          Obx(
            () => GestureDetector(
              onTap: () =>
                  controller.recordEnabled ? null : controller.sendMessage(),
              onTapDown: (_) =>
                  controller.recordEnabled ? controller.startRecording() : null,
              onTapUp: (_) => controller.recordController.isRecording
                  ? controller.stopRecording()
                  : null,
              child: Container(
                height: 45,
                width: 45,
                decoration: const BoxDecoration(
                  color: StyleRepo.turquoise,
                  shape: BoxShape.circle,
                ),
                child: AnimatedSwitcher(
                  duration: 200.milliseconds,
                  transitionBuilder: (child, animation) =>
                      ScaleTransition(scale: animation, child: child),
                  child: controller.recordEnabled
                      ? const Icon(
                          Icons.mic_none_rounded,
                          color: StyleRepo.white,
                        )
                      : SvgIcon(
                          Assets.icons.send.path,
                          color: StyleRepo.white,
                        ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _RecordingWaves extends StatelessWidget {
  const _RecordingWaves({required this.controller});

  final ChatRoomPageController controller;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      return Container(
        decoration: BoxDecoration(
          color: StyleRepo.white,
          boxShadow: StyleRepo.elevation_2,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            AudioWaveforms(
              size: Size(constraints.maxWidth, 50.0),
              recorderController: controller.recordController,
              enableGesture: true,
              waveStyle: const WaveStyle(
                waveColor: StyleRepo.turquoise,
                spacing: 8.0,
                showBottom: false,
                extendWaveform: true,
                showMiddleLine: false,
              ),
            ),
          ],
        ),
      );
    });
  }
}

class _RecordedVoicePlayer extends StatelessWidget {
  const _RecordedVoicePlayer({required this.controller});

  final ChatRoomPageController controller;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (!controller.isRecordedPlayerInit) {
        return const LinearProgressIndicator();
      } else {
        return Container(
          decoration: BoxDecoration(
            color: StyleRepo.white,
            boxShadow: StyleRepo.elevation_2,
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              StreamBuilder(
                stream: controller.recordedVoicePlayer.onPlayerStateChanged,
                builder: (context, snapData) {
                  if (!snapData.hasData) {
                    return IconButton(
                      onPressed: () => controller.playeRecordedVoice(),
                      icon: const Icon(Icons.play_arrow_rounded),
                    );
                  } else {
                    if (snapData.data == PlayerState.paused ||
                        snapData.data == PlayerState.initialized) {
                      return IconButton(
                        onPressed: () => controller.playeRecordedVoice(),
                        icon: const Icon(Icons.play_arrow_rounded),
                      );
                    } else if (snapData.data == PlayerState.playing) {
                      return IconButton(
                        onPressed: () => controller.pauseRecordedVoice(),
                        icon: const Icon(Icons.pause_rounded),
                      );
                    }
                  }
                  return const SizedBox();
                },
              ),
              Expanded(
                child: AudioFileWaveforms(
                  padding: const EdgeInsets.only(top: 12, right: 12, left: 12),
                  waveformType: WaveformType.fitWidth,
                  size: Size(MediaQuery.of(context).size.width * .5, 45.0),
                  playerController: controller.recordedVoicePlayer,
                  waveformData: controller.recordWaves,
                  playerWaveStyle: PlayerWaveStyle(
                    fixedWaveColor: StyleRepo.grey.shade300,
                    liveWaveColor: StyleRepo.turquoise,
                    // spacing: 8.0,
                    showBottom: false,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => controller.disposeRecorderPlayer(),
                icon: const Icon(Icons.cancel),
              ),
            ],
          ),
        );
      }
    });
  }
}

class _MessageTextField extends StatelessWidget {
  const _MessageTextField({required this.controller});

  final ChatRoomPageController controller;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: StyleRepo.white,
        boxShadow: StyleRepo.elevation_2,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TextField(
        controller: controller.textController,
        minLines: 1,
        maxLines: 3,
        onChanged: (str) {
          if (str.isEmpty) {
            controller.recordEnabled = true;
            controller.canOpenMessagesTypesPopup = true;
          } else {
            controller.recordEnabled = false;
            controller.canOpenMessagesTypesPopup = false;
          }
        },
        decoration: InputDecoration(
          hintText: LocaleKeys.type_message.tr(),
          suffixIcon: Obx(
            () {
              return AnimatedSwitcher(
                duration: 200.milliseconds,
                transitionBuilder: (child, animation) =>
                    ScaleTransition(scale: animation, child: child),
                child: controller.canOpenMessagesTypesPopup
                    ? InkWell(
                        onTapUp: (details) =>
                            controller.openMessagesTypesPopup(details, context),
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: SvgIcon(Assets.icons.link.path,
                              color: StyleRepo.blueViolet),
                        ),
                      )
                    : const SizedBox(),
              );
            },
          ),
          border: OutlineInputBorder(
            borderSide: BorderSide.none,
            borderRadius: BorderRadius.circular(12),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide.none,
            borderRadius: BorderRadius.circular(12),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide.none,
            borderRadius: BorderRadius.circular(12),
          ),
        ),
      ),
    );
  }
}
