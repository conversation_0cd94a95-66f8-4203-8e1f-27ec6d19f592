import 'received_message.dart';
import 'send_message.dart';
import 'types.dart';

abstract class Message {
  late final MessageType type;
  late final DateTime date;
  bool get isMine;

  Message({
    required this.type,
    required this.date,
  });

  Message.fromJson(Map<String, dynamic> json) {
    type = MessageType.fromString(json['type']);
    date = DateTime.tryParse(json['created_at'] ?? "")?.toLocal() ??
        DateTime.now();
  }

  SendMessage get asSendMessage {
    return this as SendMessage;
  }

  ReceivedMessage get asReceivedMessage {
    return this as ReceivedMessage;
  }
}
