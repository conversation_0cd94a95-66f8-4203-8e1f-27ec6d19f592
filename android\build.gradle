buildscript {
    ext.kotlin_version = '1.9.22'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.4.1' // latest
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.1' // or latest
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'

subprojects {
    afterEvaluate { project ->
        // check only for "com.android.library" to not modify
        // your "app" subproject. All plugins will have "com.android.library" plugin, and only your app "com.android.application"
        // Change your application's namespace in main build.gradle and in main android block.

        if (project.plugins.hasPlugin("com.android.library")) {
            project.android {
                if (namespace == null) {
                    namespace project.group
                }
            }
        }
    }
}

subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
