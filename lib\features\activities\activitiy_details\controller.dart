// ignore_for_file: invalid_use_of_protected_member

import 'package:get/get.dart';
import 'package:gopal/core/config/role_middleware.dart';
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/models/activity/constants/type.dart';
import 'package:gopal/core/models/activity/main_activity.dart';
import 'package:gopal/core/services/location.dart';
import 'package:gopal/core/services/pagination/controller.dart';
import 'package:gopal/features/activities/my_activities/models/favourite_activity.dart';
import 'package:gopal/features/usecases/toast/toast.dart';

import '../../../core/services/rest_api/rest_api.dart';
import '../usecases/booking/booking.dart';
import '../usecases/open_sessions_calendar/controller.dart';
import '../usecases/specific_days_calendar/controller.dart';
import 'models/activity.dart';
import '../../../core/models/activity/set.dart';

class ActivityDetailsPageController extends GetxController with Booking {
  final int id;
  ActivityDetailsPageController(this.id) {
    activityRequest = Request(
      endPoint: EndPoints.activity(id),
      fromJson: ActivityDetails.fromJson,
    );
  }

  late Request<ActivityDetails> activityRequest;
  late Request<MainActivity> relatedActivitiesRequest = Request(
    endPoint: EndPoints.main_activities,
    fromJson: MainActivity.fromJson,
  );

  ActivityDetails? get activity {
    try {
      return (activityRequest.response!.data as ActivityDetails);
    } catch (_) {
      return null;
    }
  }

  loadData() async {
    final location = await LocationUtils.getMyLocation(openSettings: false);
    if (location != null) {
      activityRequest.params = {
        "lat": location.latitude,
        "lng": location.longitude
      };
    }
    await activityRequest.perform();
    if (!activityRequest.response!.success) return;
    favouriteId = activity!.favouriteId;
    isLiked = favouriteId != null;

    if (activity!.type == ActivityType.numbered_session) {
      setsRequest = Request(
        endPoint: EndPoints.numbered_session,
        params: {"sub_activity_id": id},
        fromJson: ActivitySet.fromJson,
      );
      setsRequest!.perform();
    }

    relatedActivitiesRequest.params = {
      "activity_id": activity!.activityId,
      "current_sub_activity_id": id,
    };
    relatedActivitiesRequest.perform();
  }

  refreshData() async {
    activityRequest.reset();
    relatedActivitiesRequest.reset();
    setsRequest?.reset();
    if (Get.isRegistered<OpenSessionsController>(tag: "$id")) {
      Get.find<OpenSessionsController>(tag: "$id").refreshCalendar();
    }
    if (Get.isRegistered<SpecificDayController>(tag: "$id")) {
      Get.find<SpecificDayController>(tag: "$id").refreshCalendar();
    }
    loadData();
  }

  @override
  void onInit() {
    loadData();
    super.onInit();
  }

  @override
  void onClose() {
    activityRequest.stop();
    relatedActivitiesRequest.stop();
    super.onClose();
  }

  //SECTION - Favourite
  int? favouriteId;
  final Rx<bool> _isLiked = false.obs;
  bool get isLiked => _isLiked.value;
  set isLiked(bool value) => _isLiked.value = value;

  bool _isLikeChanging = false;
  changeLikeStatus() async {
    if (RoleMiddleware.guestForbidden) return;

    if (_isLikeChanging) return;
    _isLikeChanging = true;
    bool isChanged = false;
    if (!isLiked) {
      isChanged = await _addToFavourite();
    } else {
      isChanged = await _removeFromFavourite();
    }
    _isLikeChanging = false;
    if (isChanged) {
      if (Get.isRegistered<PaginationController<FavouriteActivity>>(
          tag: ControllersTags.my_favourites_activities)) {
        Get.find<PaginationController<FavouriteActivity>>(
                tag: ControllersTags.my_favourites_activities)
            .refreshData();
      }
    }
  }

  Future<bool> _addToFavourite() async {
    isLiked = true;
    ResponseModel response = await Request(
      endPoint: EndPoints.add_to_favourite,
      method: RequestMethod.Post,
      body: {"favoritable_type": "SUB_ACTIVITY", "favoritable_id": id},
    ).perform();
    if (!response.success) {
      Toast.show(message: response.message);
      isLiked = false;
    } else {
      favouriteId = response.data['id'];
    }
    return response.success;
  }

  Future<bool> _removeFromFavourite() async {
    isLiked = false;
    ResponseModel response = await Request(
      endPoint: EndPoints.remove_from_favourite(favouriteId!),
      method: RequestMethod.Delete,
    ).perform();
    if (!response.success) {
      Toast.show(message: response.message);
      isLiked = true;
    }
    return response.success;
  }
  //!SECTION

  //SECTION - numbered sessions
  Request<ActivitySet>? setsRequest;
  //!SECTION
}
