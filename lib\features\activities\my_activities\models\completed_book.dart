import 'package:get/get.dart';
import 'package:gopal/core/models/book/book.dart';
import 'package:gopal/core/models/rate.dart';

class CompletedBook extends BookedActivity {
  Review? rate;
  String? certificate;

  final Rx<bool> _isRated = false.obs;
  bool get isRated => _isRated.value;
  set isRated(bool value) => _isRated.value = value;

  addRate(Review rate) {
    this.rate = rate;
    isRated = true;
  }

  CompletedBook.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    if (json['review_by_me'] != null) {
      rate = Review.fromJson(json['review_by_me']);
      isRated = true;
    }
    if (json['media'].isNotEmpty) {
      certificate = json['media']['certificate'].first['original_url'];
    }
  }
}
