import 'dart:developer';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';

class DeviceInfo {
  static Future<String> get userAgent async {
    final deviceInfo = DeviceInfoPlugin();
    if (Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      log('Running on ${androidInfo.model}'); // e.g. "Moto G (4)"
      return androidInfo.model;
    } else if (Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      log('Running on ${iosInfo.utsname.machine}');
      return iosInfo.utsname.machine;
    } else {
      throw " Unsupported platform";
    }
  }
}
