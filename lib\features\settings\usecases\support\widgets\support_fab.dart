import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/config/role_middleware.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';

import '../index.dart';

class SupportFab extends StatelessWidget {
  final Pages page;
  final dynamic data;
  const SupportFab({super.key, required this.page, this.data});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () async {
        if (RoleMiddleware.guestForbidden) return;

        Get.bottomSheet(
          SupportBottomSheet(page: page, data: data),
          backgroundColor: Colors.transparent,
          isScrollControlled: true,
        );
      },
      child: Hero(
        tag: "support_sticker",
        child: Assets.icons.supportSticker.svg(height: 60, width: 60),
      ),
    );
  }
}
