import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import '../../profile/child_profile/models/calendar_session.dart';

class CalendarActivitiyCard extends StatelessWidget {
  final CalendarSession session;
  const CalendarActivitiyCard({super.key, required this.session});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(12),
      onTap: () => Nav.to(
        Pages.activity_details,
        arguments: session.subactivityId,
        preventDuplicates: false,
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        decoration: BoxDecoration(
          color: StyleRepo.white,
          boxShadow: StyleRepo.elevation_3,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                AppImage(
                  path: session.centerProfileImage.small,
                  type: ImageType.CachedNetwork,
                  height: 35,
                  width: 35,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: StyleRepo.elevation,
                  ),
                ),
                const Gap(8),
                Expanded(
                  child: Row(
                    children: [
                      Text(
                        session.subactivityName,
                        style: context.textTheme.titleSmall!.copyWith(
                          color: StyleRepo.berkeleyBlue,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const Gap(12),
            Row(
              children: [
                SvgIcon(
                  Assets.icons.time.path,
                  size: 20,
                  color: StyleRepo.grey.shade700,
                ),
                const Gap(6),
                Expanded(
                  child: RichText(
                    text: TextSpan(
                      style: context.textTheme.bodyMedium,
                      children: [
                        TextSpan(
                          text: "${LocaleKeys.time.tr()}: ",
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        TextSpan(
                          text: LocaleKeys.from_to.tr(
                            args: [
                              session.start.format(context),
                              session.end.format(context),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
