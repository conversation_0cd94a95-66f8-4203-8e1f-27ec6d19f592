import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gopal/core/localization/strings.dart';

extension DateTimeExt on DateTime {
  static String dayName(int weekday) => switch (weekday) {
        0 => LocaleKeys.week_day_sunday.tr(),
        1 => LocaleKeys.week_day_monday.tr(),
        2 => LocaleKeys.week_day_tuesday.tr(),
        3 => LocaleKeys.week_day_wednesday.tr(),
        4 => LocaleKeys.week_day_thursday.tr(),
        5 => LocaleKeys.week_day_friday.tr(),
        6 => LocaleKeys.week_day_suterday.tr(),
        _ => LocaleKeys.week_day_sunday.tr(),
      };

  bool get isToday {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final dateToCheck = DateTime(year, month, day);
    if (today == dateToCheck) return true;
    return false;
  }

  String get age {
    Duration age = DateTime.now().difference(this);
    if (age.inDays > 365) {
      return LocaleKeys.years.plural(age.inDays ~/ 365);
    }
    return LocaleKeys.months.plural(age.inDays ~/ 30);
  }
}

extension TimeOfDayExt on TimeOfDay {
  static TimeOfDay parse(String s) {
    try {
      List splitted = s.split(":");
      int hours = int.parse(splitted[0]);
      int minutes = int.parse(splitted[1]);
      return TimeOfDay(hour: hours, minute: minutes);
    } catch (_) {
      throw "Can not parse to TimeOfDay object";
    }
  }

  static TimeOfDay? tryParse(String s) {
    try {
      return parse(s);
    } catch (_) {
      return null;
    }
  }

  bool isBefore(TimeOfDay time) {
    if (hour > time.hour) {
      return false;
    } else if (hour == time.hour) {
      return minute < time.minute;
    }
    return true;
  }

  bool isAfter(TimeOfDay time) => !isBefore(time);

  bool isEqual(TimeOfDay time) => hour == time.hour && minute == time.minute;

  TimeOfDay addDuration(Duration duration) {
    final int totalMinutes = hour * 60 + minute + duration.inMinutes;
    final int newHour =
        totalMinutes ~/ 60 % 24; // Ensure hour is within 0-23 range
    final int newMinute =
        totalMinutes % 60; // Ensure minutes are within 0-59 range

    return TimeOfDay(hour: newHour, minute: newMinute);
  }

  String value() {
    return "${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}";
  }
}
