<svg width="428" height="70" viewBox="0 0 428 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_i_1417_180)">
<path d="M0 0H428V58.6736C152.5 75.4781 82.5 71.9714 0 58.6736V0Z" fill="url(#paint0_linear_1417_180)"/>
</g>
<defs>
<filter id="filter0_i_1417_180" x="0" y="-2" width="428" height="72" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_1417_180"/>
</filter>
<linearGradient id="paint0_linear_1417_180" x1="8.29069e-08" y1="58.4706" x2="427.719" y2="47.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#936CBB"/>
<stop offset="1" stop-color="#753FF4"/>
</linearGradient>
</defs>
</svg>
