import 'package:easy_localization/easy_localization.dart';
import 'package:gopal/core/localization/strings.dart';

enum PriceType {
  any,
  per_hour,
  per_course,
  ;

  String? get value {
    switch (this) {
      case any:
        return null;
      case per_hour:
        return "HOURLY";
      case per_course:
        return "COURSE";
    }
  }

  String get text => switch (this) {
        per_hour => LocaleKeys.per_hour.tr(),
        per_course => LocaleKeys.per_course.tr(),
        any => LocaleKeys.any.tr(),
      };
}
