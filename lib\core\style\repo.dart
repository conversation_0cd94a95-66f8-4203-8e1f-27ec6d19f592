import 'package:flutter/material.dart';

class StyleRepo {
  static const Color blueViolet = Color(0xFF753FF4);
  static const Color amythest = Color(0xFF936CB7);

  static const Color berkeleyBlue = Color(0xFF12325E);

  static const Color turquoise = Color(0xFF00C58A);

  static const MaterialColor grey = Colors.grey;
  static const MaterialColor red = Colors.red;
  static const MaterialColor yellow = Colors.yellow;
  static const Color white = Colors.white;
  static const Color black = Colors.black;

  static const LinearGradient generalAppBarGradient = LinearGradient(
    colors: [
      Color(0xFF936CBB),
      Color(0xFF753FF4),
    ],
    begin: AlignmentDirectional.centerStart,
    end: AlignmentDirectional.centerEnd,
  );

  static const List<BoxShadow> elevation = [
    BoxShadow(
      color: Colors.black26,
      blurRadius: 4,
      offset: Offset(0, 2),
    ),
  ];

  static const List<BoxShadow> authCardShadow = [
    BoxShadow(
      color: Colors.black26,
      blurRadius: 4,
      offset: Offset(0, 2),
    ),
  ];

  static const List<BoxShadow> elevation_2 = [
    BoxShadow(
      color: Colors.black12,
      blurRadius: 4,
      offset: Offset(0, 4),
    ),
  ];

  static const List<BoxShadow> elevation_3 = [
    BoxShadow(
      color: Colors.black12,
      blurRadius: 18,
      offset: Offset(0, 4),
    ),
  ];
}
