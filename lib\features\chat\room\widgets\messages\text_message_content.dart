import 'package:flutter/material.dart';
import 'package:gopal/core/models/chat/messages/messages.dart';

class TextMessageContent extends StatelessWidget {
  final Message message;
  const TextMessageContent({super.key, required this.message});

  String get text {
    if (message is ReceivedMessage) {
      return (message.asReceivedMessage.content as ReceivedTextMessageContent)
          .text;
    } else {
      return (message.asSendMessage.content as SendTextMessageContent).text;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Text(text),
    );
  }
}
