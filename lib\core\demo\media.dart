import 'dart:math';

class DemoMedia {
  static String get getRandomImage => "https://picsum.photos/200/300";

  static List<String> activitiesImages = [
    "https://lh5.googleusercontent.com/p/AF1QipNKVuBj6AK0Fe1sIawZryq-jJj0C4DjhyMpW-cr=w203-h114-k-no",
    "https://lh5.googleusercontent.com/p/AF1QipMODV1HfM4QHaw2mSXOWtsPDtPqwTlJWwu3yjAJ=s387-k-no",
    "https://lh5.googleusercontent.com/p/AF1QipORCZ1XtOEnuuThi2_nDHwPhQf9f4Rkl68c6t5g=s387-k-no",
    "https://lh5.googleusercontent.com/p/AF1QipORCZ1XtOEnuuThi2_nDHwPhQf9f4Rkl68c6t5g=s387-k-no",
    "https://lh5.googleusercontent.com/p/AF1QipO-K7XpJVeiVyt3pDjk1DzZwvrLLuCNiJUfygKi=s454-k-no",
    "https://lh5.googleusercontent.com/p/AF1QipO9MV0jT47TU6QyMvrwKNxCffYN6SuKa-rIrYxe=s431-k-no",
    "https://lh5.googleusercontent.com/p/AF1QipOgeebjjmVj7pinEm0PiGeMr5fFOM_DMBpIqFiS=s508-k-no",
    "https://lh5.googleusercontent.com/p/AF1QipO0SaQEsD1RpsI4aOGQkPCNSvITl8DQR3ZwxYzL=s451-k-no",
    "https://lh5.googleusercontent.com/p/AF1QipMMjLB2pkYrU4H1jHiVW6jUbXP32V7Ti6dl8iE1=s451-k-no",
    // "https://media.istockphoto.com/id/1172681503/photo/creative-kids-creative-arts-and-crafts-classes-in-after-school-activities.jpg?s=612x612&w=0&k=20&c=Pdvbbp7pw-AqunKYoxkjPzUoxr-HWt1C6DXsN-j0xhE=",
    // "https://raisingchildren.net.au/__data/assets/image/0028/48727/activities-for-school-kids-2narrow.jpg",
    // "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS3_YJRPqd2W9OVoZiEypIt5Wr7afyBksWG2w&s",
    // "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQzfoXGX_TRphXy4bRbDZVvu1DFGd_hvw4cng&s",
    // "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTplxQZi73cs4iu0q_WXq95Dijm7jRUzXOQuQ&s",
    // "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcTqYc807nqSvk9Ryae9Vu4wzl1YPmujgRr_wg&s",
    // "https://media.istockphoto.com/id/481495699/photo/pull.jpg?s=612x612&w=0&k=20&c=2CZ8MvYM5D2m5otrdCmnIkTFZYDGMoU8PMBOSKQqNUg=",
    // "https://media.istockphoto.com/id/610864024/photo/couple-kayaking-together.jpg?s=612x612&w=0&k=20&c=zeioetaU5WM6uUnZAGoWBHnkilMeK1pexGX1ZitPU1o=",
    // "https://raisingchildren.net.au/__data/assets/image/0019/122194/belly-breathing.jpg",
  ];

  static String get getRandomVideo => Random().nextBool()
      ? "https://sample-videos.com/video321/mp4/720/big_buck_bunny_720p_5mb.mp4"
      : "https://file-examples.com/storage/fe0e9b723466913cf9611b7/2017/04/file_example_MP4_640_3MG.mp4";

  static List<String> getRandomImagesAndVideos(int length) {
    return List.generate(
      length,
      (index) => Random().nextBool() ? getRandomImage : getRandomVideo,
    );
  }
}
