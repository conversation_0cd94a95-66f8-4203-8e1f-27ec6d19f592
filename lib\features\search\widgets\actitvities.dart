import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/activity/main_activity.dart';
import 'package:gopal/core/services/pagination/options/list_view.dart';
import 'package:gopal/features/activities/widgets/activity_card.dart';

import '../controller.dart';

class ActivitiesSearch extends GetView<SearchPageController> {
  const ActivitiesSearch({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: TextField(
            controller: controller.activitiesSearch,
            onChanged: controller.debouncerActivitiesSearch.add,
            decoration: InputDecoration(
              hintText: tr(LocaleKeys.search_for_activities),
              prefixIcon: const Icon(Icons.search),
            ),
          ),
        ),
        const Divider(height: 0),
        Expanded(
          child: Obx(
            () => AnimatedSwitcher(
              duration: 300.milliseconds,
              transitionBuilder: (child, animation) =>
                  ScaleTransition(scale: animation, child: child),
              child: controller.isSearchingForActivities
                  ? ListViewPagination.separated(
                      tag: ControllersTags.activities_search_pager,
                      fetchApi: controller.fetchActivities,
                      fromJson: MainActivity.fromJson,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      itemBuilder: (context, index, activity) =>
                          ActivityCard(activity: activity),
                      separatorBuilder: (_, __) => const Gap(12),
                    )
                  : Center(child: Text(tr(LocaleKeys.search_for_activities))),
            ),
          ),
        ),
      ],
    );
  }
}
