import 'package:gopal/core/models/image.dart';

enum AdType {
  text,
  image;

  static AdType fromString(String s) => switch (s) {
    "TEXT" => text,
    "IMAGE" => image,
    _ => text,
  };
}

enum AdActionType {
  external,
  internal,
  image;

  static AdActionType fromString(String s) {
    switch (s) {
      case "INTERNAL":
        return internal;
      case "EXTERNAL":
        return external;
      case "ONLY_IMAGE":
        return image;
      default:
        throw '';
    }
  }
}

enum InternalAdType {
  activity,
  provider;

  static InternalAdType fromString(String s) {
    switch (s) {
      case "SUB_ACTIVITY":
        return activity;
      case "PROVIDER":
        return provider;
      default:
        throw '';
    }
  }
}

class SliderAd {
  int id;
  AdType type;
  String? link;
  ImageModel? image;
  String? cover;
  String? text;
  InternalAdType? internalType;
  int? adableId;
  AdActionType actionType;

  SliderAd({
    required this.id,
    required this.type,
    this.link,
    this.image,
    this.cover,
    this.text,
    this.internalType,
    required this.actionType,
    this.adableId,
  });

  factory SliderAd.fromJson(Map<String, dynamic> json) => SliderAd(
    id: json["id"],
    type: AdType.fromString(json['display_type']),
    link: json["link"],
    cover: json['cover'],
    image:
        json['media'] is List
            ? null
            : ImageModel.fromJson(json['media']['cover'].first),
    text: json['text'],
    internalType:
        (json["adable_type"] == null || json["adable_type"].isEmpty)
            ? null
            : InternalAdType.fromString(json["adable_type"]),
    actionType: AdActionType.fromString(json["type"]),
    adableId: json['adable_id'],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "link": link,
    "image": image,
    "adable_type": internalType,
    "type": actionType,
  };
}
