import 'package:easy_localization/easy_localization.dart';
import 'package:gopal/core/localization/strings.dart';

enum Gender {
  male,
  female,
  both,
  ;

  bool get isMale => this == male;
  bool get isFemale => this == female;
  bool get isBoth => this == both;

  String get text => switch (this) {
        male => LocaleKeys.gender_male.tr(),
        female => LocaleKeys.gender_female.tr(),
        both =>
          "${LocaleKeys.gender_male.tr()} & ${LocaleKeys.gender_female.tr()}",
      };

  int get value {
    switch (this) {
      case male:
        return 1;
      case female:
        return 2;
      default:
        return 0;
    }
  }

  static Gender fromValue(int v) {
    switch (v) {
      case 1:
        return male;
      case 2:
        return female;
      default:
        throw "";
    }
  }

  static Gender fromString(String s) {
    switch (s) {
      case "MALE":
        return male;
      case "FEMALE":
        return female;
      case "BOTH":
        return both;
      default:
        throw "Unknown Target age: $s";
    }
  }
}
