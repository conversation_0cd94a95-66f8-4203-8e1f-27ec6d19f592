import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/config/app_builder.dart';
import 'package:gopal/core/localization/localization.dart';
import 'package:gopal/core/style/repo.dart';

class LanguagesSelection extends StatelessWidget {
  const LanguagesSelection({super.key});

  @override
  Widget build(BuildContext context) {
    AppBuilder appBuilder = Get.find();
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: Get.width * .1),
      child: Directionality(
        textDirection: TextDirection.ltr,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: AppLocalization.values
              .map(
                (locale) => InkWell(
                  onTap: () => appBuilder.changeLocale(locale: locale),
                  borderRadius: BorderRadius.circular(45),
                  child: AnimatedContainer(
                    duration: 300.milliseconds,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      color: StyleRepo.white,
                      border: appBuilder.currentLocale == locale
                          ? Border.all(color: StyleRepo.blueViolet)
                          : null,
                      borderRadius: BorderRadius.circular(45),
                    ),
                    child: Row(
                      children: [
                        Text(locale.flagEmoji),
                        const Gap(8),
                        Text(locale.text),
                      ],
                    ),
                  ),
                ),
              )
              .toList(),
        ),
      ),
    );
  }
}
