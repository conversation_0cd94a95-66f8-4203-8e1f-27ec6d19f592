import 'dart:math';

import 'package:gopal/core/models/image.dart';

import '../user/center.dart';

class MainActivity {
  int id;
  String name;
  String description;
  ImageModel cover;
  ImageModel? seasonCover;
  CenterModel center;
  int booksCount;
  int? subActivityId;
  bool isAvailable;

  MainActivity({
    required this.id,
    required this.name,
    required this.description,
    required this.cover,
    required this.center,
    required this.booksCount,
    required this.isAvailable,
    this.seasonCover,
    this.subActivityId,
  });

  factory MainActivity.fromJson(Map<String, dynamic> json) => MainActivity(
        id: json["id"],
        name: json["main_activity_name"] ?? "",
        description: json["main_activity_description"] ?? "",
        cover: ImageModel.tryParse(
            json['media'] is List ? {} : json['media']['main_image'].first),
        seasonCover:
            (json['media'] is List || json['media']['season_cover'] == null)
                ? null
                : ImageModel.tryParse(json['media'] is List
                    ? {}
                    : json['media']['season_cover']?.first),
        center: CenterModel.fromJson(json["provider"]),
        isAvailable: json['is_available'] ?? Random().nextBool(),
        booksCount: json['book_transaction_count'] ?? 0,
        subActivityId: json['sub_activities']?['id'],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "main_activity_name": name,
        "main_activity_description": description,
        "media": cover,
        "center": center,
      };
}
