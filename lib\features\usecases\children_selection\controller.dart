// ignore_for_file: invalid_use_of_protected_member

import 'package:get/get.dart';
import 'package:gopal/core/models/user/main_child.dart';

import '../../../core/services/rest_api/rest_api.dart';

class ChildrenSelectionController extends GetxController {
  final int? activityId;
  final List<int> initialSelected;
  final bool isMultiSelect;

  ChildrenSelectionController({
    this.activityId,
    this.initialSelected = const [],
    required this.isMultiSelect,
  }) {
    childrenRequest = Request(
      endPoint: EndPoints.children,
      params: activityId == null
          ? null
          : {"sub_activity_id": activityId, "has_booking": 0},
      fromJson: MainChild.fromJson,
    );
    selectedChildren.addAll(initialSelected);
  }

  RxSet<int> selectedChildren = RxSet();

  final Rx<bool> _isAllSelected = false.obs;
  bool get isAllSelected => _isAllSelected.value;
  set isAllSelected(bool value) => _isAllSelected.value = value;

  add$RemoveChild(int id) {
    if (selectedChildren.contains(id)) {
      selectedChildren.remove(id);
      if (isAllSelected) {
        isAllSelected = false;
      }
    } else {
      if (!isMultiSelect && selectedChildren.isNotEmpty) {
        add$RemoveChild(selectedChildren.first);
      }
      selectedChildren.add(id);

      bool allSelected = true;
      for (var child in allChildren) {
        if (!selectedChildren.contains(child.id)) {
          allSelected = false;
          break;
        }
      }

      if (allSelected) {
        isAllSelected = true;
      }
    }
  }

  selectAll() {
    if (isAllSelected) {
      isAllSelected = false;
      selectedChildren.clear();
    } else {
      isAllSelected = true;
      selectedChildren.clear();
      selectedChildren.addAll(allChildren.map((e) => e.id!).toSet());
    }
  }

  late Request<MainChild> childrenRequest;
  late Set<MainChild> allChildren;

  loadData() async {
    await childrenRequest.perform();
    if (childrenRequest.response!.success) {
      allChildren = (childrenRequest.response!.data as List<MainChild>).toSet();
    }
  }

  refreshData() {
    childrenRequest.reset();
    loadData();
  }

  @override
  void onInit() {
    loadData();
    super.onInit();
  }

  @override
  void onClose() {
    childrenRequest.stop();
    super.onClose();
  }
}
