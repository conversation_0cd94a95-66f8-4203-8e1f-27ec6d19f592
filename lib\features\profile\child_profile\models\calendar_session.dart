import 'package:flutter/material.dart';
import 'package:gopal/core/models/image.dart';
import 'package:gopal/core/utils/date.dart';

class CalendarSession {
  late DateTime date;
  late TimeOfDay start;
  late TimeOfDay end;
  late ImageModel centerProfileImage;
  late int subactivityId;
  late String subactivityName;

  CalendarSession.fromJson(Map<String, dynamic> json) {
    date = DateTime.parse(json['date']);
    start = TimeOfDayExt.parse(json['from']);
    end = TimeOfDayExt.parse(json['to']);
    subactivityName = json['sub_activity_name'];
    subactivityId = json['sub_activity_id'];
    centerProfileImage =
        ImageModel.fromJson(json['provider_photo']['profile'].first);
  }
}
