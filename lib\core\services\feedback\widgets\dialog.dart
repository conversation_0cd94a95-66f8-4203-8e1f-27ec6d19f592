import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';

import '../controller.dart';

class FeedbackDialog extends GetView<FeedBackController> {
  const FeedbackDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.white,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(15),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            InkWell(
              onTap: () => controller.saveToGallery(),
              child: const Card(
                child: Padding(
                  padding: EdgeInsets.all(12),
                  child: Text("Save To Gallery"),
                ),
              ),
            ),
            const Gap(12),
            InkWell(
              onTap: () => controller.sendToWhatsapp(),
              child: const Card(
                child: Padding(
                  padding: EdgeInsets.all(12),
                  child: Text("Send To Whatsapp"),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
