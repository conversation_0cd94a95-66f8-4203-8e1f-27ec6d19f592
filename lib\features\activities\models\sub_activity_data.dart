import 'package:gopal/core/models/activity/constants/price_type.dart';
import 'package:gopal/core/models/activity/constants/type.dart';
import 'package:gopal/core/models/activity/set.dart';
import 'package:gopal/core/models/activity/subactivity.dart';
import 'package:gopal/core/models/constants/gender.dart';
import 'package:gopal/core/models/activity/periodic/main_periodic.dart';

class SubActivityData extends SubActivity {
  late int activityId;
  late PriceType priceType;
  num? priceBeforeDiscount;
  late bool isDiscountedToday;
  late num price;
  late int minAge;
  late int maxAge;
  late Gender gender;
  late bool isFamily;
  late ActivityType type;
  late bool isSupportCustom;
  String? season;
  MainPeriodicModel? periodic;
  DateTime? minDate;
  DateTime? maxDate;
  int? specificDayId;
  int? specificDaySessionsCount;
  List<ActivitySet>? sets;
  int? minPersonsNumber, maxPersonsNumber;

  SubActivityData({
    required super.id,
    required super.capacity,
    required super.name,
    required super.description,
    required super.center,
    required super.isAvailable,
    super.discountType,
    super.discount,
    required this.activityId,
    required this.priceType,
    required this.price,
    this.priceBeforeDiscount,
    required this.minAge,
    required this.maxAge,
    required this.gender,
    required this.isFamily,
    required this.type,
    required this.isSupportCustom,
    this.season,
    this.periodic,
    this.specificDayId,
    this.isDiscountedToday = false,
    this.sets,
  });

  SubActivityData.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    activityId = json["activity_id"];
    priceType = PriceType.fromString(json["price_type"]);
    price = json["total_price"];
    priceBeforeDiscount = json['price_before_discount'];
    isDiscountedToday = json['is_have_discount'] ?? false;
    minAge = json["min_age"];
    maxAge = json["max_age"];
    gender = Gender.fromString(json["gender"]);
    isFamily = json["is_family"] == 1;
    type = ActivityType.fromString(json["session_type"]);
    isSupportCustom = type == ActivityType.custom_session
        ? true
        : json['support_custom_session'] == 1;
    if (json['seasons'] != null && json['seasons'].isNotEmpty) {
      season = json['seasons'].first['name'];
    }
    periodic = json['periodic_day'] != null
        ? MainPeriodicModel.parseTypes(json['periodic_day'])
        : null;
    minDate = DateTime.tryParse(json['open_sessions_min_from'] ?? "") ??
        DateTime.tryParse(json['specific_day']?['from'] ?? "");
    maxDate = DateTime.tryParse(json['open_sessions_max_to'] ?? "") ??
        DateTime.tryParse(json['specific_day']?['to'] ?? "");
    specificDayId = json['specific_day']?['id'];
    specificDaySessionsCount =
        json['specific_day']?['specific_day_values']?.length;

    if (json['numbered_sessions'] != null) {
      sets = List.from(
          json['numbered_sessions'].map((e) => ActivitySet.fromJson(e)));
    }

    minPersonsNumber = json['min_persons_number'];
    maxPersonsNumber = json['max_persons_number'];
  }

  @override
  Map<String, dynamic> toJson() {
    Map<String, dynamic> json = super.toJson();
    json.addAll({
      "activity_id": activityId,
      "price_type": priceType,
      "price": price,
      "min_age": minAge,
      "max_age": maxAge,
      "discount_type": discountType,
      "discount": discount,
      "gender": gender,
      "is_family": isFamily,
      "session_type": type,
    });
    return json;
  }
}
