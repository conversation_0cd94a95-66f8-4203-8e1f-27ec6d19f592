import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gopal/core/models/constants/gender.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';

class GenderAvatar extends StatelessWidget {
  final Gender gender;
  const GenderAvatar({super.key, required this.gender});

  @override
  Widget build(BuildContext context) {
    if (gender.isBoth) {
      return Container(
        height: 35,
        decoration: BoxDecoration(
          gradient: photoGradient(gender),
          borderRadius: BorderRadius.circular(25),
        ),
        child: Row(
          children: [
            const Gap(4),
            Assets.images.girl.image(),
            const Gap(4),
            Assets.images.boy.image(),
            const Gap(4),
          ],
        ),
      );
    }
    return Container(
      width: 35,
      height: 35,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        image: DecorationImage(
          image: (gender.isMale ? Assets.images.boy : Assets.images.girl)
              .provider(),
          fit: BoxFit.cover,
        ),
        gradient: photoGradient(gender),
        boxShadow: StyleRepo.elevation_2,
      ),
    );
  }

  LinearGradient photoGradient(Gender gender) {
    if (gender.isMale) {
      return const LinearGradient(
        colors: [
          Color(0xFF1EA9E2),
          Color(0xFF63CADF),
        ],
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
      );
    } else if (gender.isFemale) {
      return const LinearGradient(
        colors: [
          Color(0xFFFF7ABA),
          Color(0xFFFFC8C8),
        ],
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
      );
    } else {
      return const LinearGradient(
        colors: [
          Color(0xFFFF7ABA),
          Color(0xFFFFC8C8),
          Color(0xFF63CADF),
          Color(0xFF1EA9E2),
        ],
        begin: AlignmentDirectional.topStart,
        end: AlignmentDirectional.bottomEnd,
      );
    }
  }
}
