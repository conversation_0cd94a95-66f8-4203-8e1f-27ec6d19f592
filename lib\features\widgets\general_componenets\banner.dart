import 'package:flutter/material.dart';
import 'package:gopal/core/style/repo.dart';

class BanneredWidget extends StatelessWidget {
  final Color? color;
  final String message;
  final BannerLocation location;
  final bool isVisible;
  final Widget child;

  const BanneredWidget({
    super.key,
    this.color,
    required this.message,
    this.location = BannerLocation.topEnd,
    this.isVisible = true,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) {
      return child;
    }
    return Banner(
      message: message,
      location: location,
      color: color ?? StyleRepo.red,
      child: child,
    );
  }
}
