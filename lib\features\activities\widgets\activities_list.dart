import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/activity/main_activity.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/activities/widgets/activity_card.dart';

class ActivitiesList extends StatelessWidget {
  final String title;
  final Function()? seeAll;
  final List<MainActivity> activities;
  final bool isOffer;

  const ActivitiesList({
    super.key,
    required this.title,
    this.seeAll,
    required this.activities,
    this.isOffer = false,
  });

  @override
  Widget build(BuildContext context) {
    if (activities.isEmpty) {
      return const SizedBox();
    }
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: context.textTheme.titleLarge!
                    .copyWith(color: StyleRepo.blueViolet),
              ),
              seeAll != null
                  ? InkWell(
                      onTap: seeAll,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 12, vertical: 4),
                        child: Text(
                          LocaleKeys.see_all.tr(),
                          style: context.textTheme.bodyMedium!.copyWith(
                            color: StyleRepo.blueViolet,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    )
                  : const SizedBox(),
            ],
          ),
        ),
        const Gap(8),
        SizedBox(
          width: double.infinity,
          height: 275,
          child: ListView.separated(
            itemCount: activities.length,
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            separatorBuilder: (_, __) => const Gap(16),
            itemBuilder: (context, index) => ActivityCard(
              activity: activities[index],
              isOffer: isOffer,
            ),
          ),
        ),
        // SizedBox(
        //   width: double.infinity,
        //   child: SingleChildScrollView(
        //     scrollDirection: Axis.horizontal,
        //     padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        //     child: Row(
        //       mainAxisAlignment: MainAxisAlignment.start,
        //       children: List.generate(
        //         activities.length,
        //         (index) => Padding(
        //           padding: const EdgeInsetsDirectional.only(end: 16),
        //           child: ActivityCard(
        //             activity: activities[index],
        //             isOffer: isOffer,
        //           ),
        //         ),
        //       ),
        //     ),
        //   ),
        // ),
      ],
    );
  }
}
