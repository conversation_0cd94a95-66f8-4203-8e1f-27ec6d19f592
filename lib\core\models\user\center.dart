import '../image.dart';

class CenterModel {
  late int id;
  late bool isTrusted;
  late String name;
  late String description;
  late int reviewsCount;
  late double rate;
  ImageModel? cover;
  late ImageModel profileImage;
  double? distance;

  CenterModel({
    required this.id,
    required this.isTrusted,
    required this.name,
    required this.description,
    required this.reviewsCount,
    required this.rate,
    required this.cover,
    required this.profileImage,
    this.distance,
  });

  CenterModel.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    isTrusted = json["is_trusted"] == 1;
    name = json["name"];
    description = json["description"];
    reviewsCount = json["reviews_count"];
    rate = (json["rate"] ?? 0) * 1.0;
    try {
      cover = ImageModel.fromJson(json['media']['cover'].first);
    } catch (_) {}
    try {
      profileImage = ImageModel.fromJson(
        json['user']['media']['profile'].first,
      );
    } catch (_) {
      profileImage = ImageModel.empty();
    }
    distance = json['distance'] != null ? json['distance'] * 1000.0 : null;
  }

  Map<String, dynamic> toJson() => {
    "id": id,
    "is_trusted": isTrusted ? 1 : 0,
    "name": name,
    "description": description,
    "reviews_count": reviewsCount,
    "rate": rate,
  };
}
