import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/categories/category/models/nav.dart';

class AllCard extends StatelessWidget {
  const AllCard({super.key});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => Nav.to(
        Pages.category,
        arguments: CategoryPageNav(null),
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
            boxShadow: StyleRepo.elevation_3,
            borderRadius: BorderRadius.circular(12),
            gradient: StyleRepo.generalAppBarGradient),
        child: Center(
          child: Text(
            LocaleKeys.all.tr(),
            style: context.textTheme.titleLarge!
                .copyWith(fontWeight: FontWeight.w700, color: StyleRepo.white),
          ),
        ),
      ),
    );
  }
}
