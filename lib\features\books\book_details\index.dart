import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/outlined_button.dart';
import 'package:gopal/features/widgets/general_componenets/pages/general_page.dart';
import 'package:qr_flutter/qr_flutter.dart';

import 'controller.dart';
import 'widgets/date_time_info.dart';
import 'widgets/header.dart';

class BookDetailsPage extends StatelessWidget {
  final int id;
  const BookDetailsPage({super.key, required this.id});

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    BookDetailsPageController controller = Get.put(
      BookDetailsPageController(id),
    );
    return GeneralPage(
      titleText: LocaleKeys.book_details.tr(),
      withBackground: true,
      child: controller.bookRequest.objectBuilder(
        withRefresh: true,
        onRefresh: () async => await controller.refreshData(),
        builder: (context, book) {
          return ListView(
            padding: const EdgeInsets.only(top: 24),
            children: [
              BookDetailsHeader(book: book),
              const Gap(16),
              Container(
                width: double.infinity,
                margin: const EdgeInsets.symmetric(horizontal: 16),
                padding: const EdgeInsets.fromLTRB(12, 12, 12, 12),
                decoration: BoxDecoration(
                  color: StyleRepo.white,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: StyleRepo.elevation_3,
                ),
                child: Row(
                  children: [
                    AppImage(
                      path: book.child.image.small,
                      type: ImageType.CachedNetwork,
                      height: 50,
                      width: 50,
                      decoration: const BoxDecoration(shape: BoxShape.circle),
                    ),
                    const Gap(12),
                    Expanded(
                      child: Text(
                        "${book.child.firstName} ${book.child.lastName}",
                        style: context.textTheme.titleLarge!.copyWith(
                          fontWeight: FontWeight.w600,
                          color: StyleRepo.berkeleyBlue,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const Gap(16),
              BookDateTimeInfo(book: book),
              const Gap(16),
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: AppOutlinedButton(
                  onTap:
                      () => Nav.to(
                        Pages.activity_details,
                        arguments: book.activity.id,
                        preventDuplicates: false,
                      ),
                  child: Text(LocaleKeys.activity_details.tr()),
                ),
              ),
              const Gap(16),
              if (book.qrContent != null)
                Center(
                  child: Container(
                    decoration: BoxDecoration(
                      color: StyleRepo.white,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow: StyleRepo.elevation_3,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: QrImageView(
                        data: book.qrContent!,
                        version: QrVersions.auto,
                        size: 200,
                        padding: const EdgeInsets.all(14),
                        // backgroundColor: StyleRepo.blueViolet.withValues(
                        //   alpha: .05,
                        // ),
                        eyeStyle: const QrEyeStyle(
                          color: StyleRepo.blueViolet,
                          eyeShape: QrEyeShape.square,
                        ),
                        embeddedImage: Assets.images.logo.provider(),
                        embeddedImageStyle: const QrEmbeddedImageStyle(
                          size: Size.square(30),
                        ),
                      ),
                    ),
                  ),
                ),
              const Gap(16),
            ],
          );
        },
      ),
    );
  }
}
