import 'dart:io';

import 'package:flutter_device_id/flutter_device_id.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:gopal/core/constants/storages_names.dart';
import 'package:gopal/core/services/device_info.dart';

import '../../config/app_builder.dart';
import '../firebase_messaging/firebase_messaging.dart';
import '../rest_api/rest_api.dart';
import 'status.dart';

class FCMRegisterService {
  static final box = GetStorage(StoragesNames.fcm_register);

  static RegisterStatus status = RegisterStatus.not_initialized;

  static init() async {
    await box.initStorage;
    if (box.hasData("status")) {
      status = RegisterStatus.fromValue(box.read("status"));
    } else {
      status = RegisterStatus.not_registerd;
      box.write("status", status.name);
    }

    if (status == RegisterStatus.not_registerd) {
      _registerAPI();
    }
  }

  static _registerAPI() async {
    final token = await FirebaseMessagingService.getToken();
    if (token == null) return;
    ResponseModel response = await Request(
      endPoint: EndPoints.device_info,
      method: RequestMethod.Post,
      body: {
        "token": token,
        "locale": Get.find<AppBuilder>().currentLocale.value,
        "type": Platform.isAndroid ? "ANDROID" : "IOS",
        "user_agent": await DeviceInfo.userAgent,
        "device_id": await FlutterDeviceId().getDeviceId() ?? "",
      },
    ).perform();
    if (response.success) {
      status = RegisterStatus.registerd;
      box.write("status", status.name);
    }
  }

  static Future<void> disopse() async {
    status = RegisterStatus.not_initialized;
    await box.erase();
  }
}
