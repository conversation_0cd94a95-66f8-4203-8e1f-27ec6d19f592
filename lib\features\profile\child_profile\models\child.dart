import 'package:gopal/core/models/constants/gender.dart';
import 'package:gopal/core/models/image.dart';
import 'package:gopal/core/models/selection.dart';
import 'package:gopal/core/models/user/main_child.dart';
import 'package:gopal/features/profile/create_child/models/child_creation.dart';

class ChildDetails extends MainChild {
  late DateTime birthdate;
  late Gender gender;
  String? allergies;
  List<Selection> specialCases = [];
  List<Selection> hobbies = [];
  ImageModel? image;

  bool get hasMedicalInfo => specialCases.isNotEmpty || allergies != null;

  ChildDetails({
    required super.id,
    required super.firstName,
    required super.lastName,
    required super.isMain,
    required this.birthdate,
    required this.gender,
    required this.allergies,
    required this.specialCases,
    required this.hobbies,
    required this.image,
  });

  ChildDetails.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    birthdate = DateTime.parse(json["birthdate"]);
    gender = Gender.fromValue(json['gender']);
    allergies = json["alergies"];
    specialCases = List<Selection>.from(
        json["special_cases"].map((x) => Selection.fromJson(x)));
    hobbies =
        List<Selection>.from(json["hobbies"].map((x) => Selection.fromJson(x)));
    image = json["media"].isNotEmpty
        ? ImageModel.fromJson(json["media"]['profile'].first)
        : null;
  }

  ChildCreation toChildCreation() => ChildCreation(
        id: id,
        firstName: firstName,
        lastName: lastName,
        image: image?.medium ?? "",
        birthDate: birthdate,
        hobbies: hobbies,
        gender: gender,
        allergy: allergies,
        specialCases: specialCases,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "first_name": firstName,
        "last_name": lastName,
        "birthdate":
            "${birthdate.year.toString().padLeft(4, '0')}-${birthdate.month.toString().padLeft(2, '0')}-${birthdate.day.toString().padLeft(2, '0')}",
        "gender": gender,
        "alergies": allergies,
        "special_cases": List<dynamic>.from(specialCases.map((x) => x)),
        "hobbies": List<dynamic>.from(hobbies.map((x) => x)),
        "image": image,
      };
}
