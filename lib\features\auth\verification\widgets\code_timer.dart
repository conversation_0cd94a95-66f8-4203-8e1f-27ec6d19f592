import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/utils/num_utils.dart';

import '../controller.dart';

class CodeTimer extends GetView<VerificationPageController> {
  const CodeTimer({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        return Column(
          children: [
            if (controller.secondsRemaining > 0)
              Text(
                "( ${controller.secondsRemaining.formatSeconds} )",
                style: context.textTheme.bodyLarge!
                    .copyWith(color: StyleRepo.blueViolet),
              ),
            const Gap(4),
            RichText(
              text: TextSpan(
                style: context.textTheme.bodyLarge!
                    .copyWith(fontWeight: FontWeight.w600),
                children: [
                  TextSpan(
                    text: "${LocaleKeys.did_not_receive_a_code.tr()} ",
                  ),
                  TextSpan(
                    recognizer: TapGestureRecognizer()
                      ..onTap = controller.secondsRemaining == 0
                          ? () => controller.resendCode()
                          : null,
                    text: LocaleKeys.resend_code.tr(),
                    style: TextStyle(
                      color: controller.secondsRemaining > 0
                          ? StyleRepo.grey
                          : StyleRepo.blueViolet,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
