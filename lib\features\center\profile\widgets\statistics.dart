import 'package:easy_localization/easy_localization.dart' hide TextDirection;
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/utils/list_utils.dart';
import 'package:gopal/core/utils/num_utils.dart';

import '../../../../core/localization/strings.dart';

class CenterStatistics extends StatelessWidget {
  final int activitiesCount, booksCount;
  const CenterStatistics({
    super.key,
    required this.activitiesCount,
    required this.booksCount,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Expanded(
            child: _StatisticCard(
              isExpanded: activitiesCount <= 0 || booksCount <= 0,
              title: activitiesCount.approximate,
              body: LocaleKeys.activities_provided
                  .plural(activitiesCount, args: [""]),
            ),
          ),
          const Gap(12),
          Visibility(
            visible: booksCount > 0,
            child: Expanded(
              child: _StatisticCard(
                isExpanded: activitiesCount <= 0 || booksCount <= 0,
                title: booksCount.approximate,
                body: LocaleKeys.n_books.plural(booksCount, args: [""]),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _StatisticCard extends StatelessWidget {
  final String title, body;
  final bool isExpanded;
  const _StatisticCard({
    required this.title,
    required this.body,
    required this.isExpanded,
  });

  @override
  Widget build(BuildContext context) {
    List<Widget> children = [
      Directionality(
        textDirection: TextDirection.ltr,
        child: Text(
          title,
          style: context.textTheme.titleLarge!.copyWith(
            color: StyleRepo.blueViolet,
            fontWeight: FontWeight.w700,
          ),
        ),
      ),
      Text(
        body,
        style: context.textTheme.titleMedium!.copyWith(
          color: StyleRepo.blueViolet,
          fontWeight: FontWeight.w600,
        ),
      ),
    ];

    return Container(
      height: 60,
      width: Get.width,
      decoration: BoxDecoration(
        color: StyleRepo.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: StyleRepo.elevation,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(10),
        child: Stack(
          children: [
            Directionality(
              textDirection: TextDirection.ltr,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Assets.icons.statisticsCardBgLeft.svg(),
                  Assets.icons.statisticsSmallCardBgRight.svg(),
                ],
              ),
            ),
            Center(
              child: isExpanded
                  ? Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: children.joinItems(const Gap(12)),
                    )
                  : Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: children,
                    ),
            ),
          ],
        ),
      ),
    );

    // if (isExpanded) {
    //   return Stack(children: [
    //     Assets.images.statisticsCardBg.image(),
    //     Center(child: Row(children: children)),
    //   ]);
    // } else {
    //   return Stack(children: [
    //     Assets.images.smallStatisticsCardBg.image(),
    //     Center(child: Column(children: children)),
    //   ]);
    // }
  }
}
