// ignore_for_file: non_constant_identifier_names

abstract class EndPoints {
  //##########  Base Url  ##########
  //test
  // static const String baseUrl = 'https://104.248.246.37:4443/api';
  //production
  static const String baseUrl = 'https://gopalsa.com/api';

  //SECTION - Auth
  static const send_code = "/auth/send-code";
  static const verify_code = "/auth/verify-code";
  static const create_account = "/guardians";
  static const logout = "/auth/logout";
  static const delete_account = "/auth/de-activate-me";
  static const change_phone = "/update-phone/send-code";
  static const change_phone_verification = "/update-phone";
  //!SECTION

  //SECTION - Profile
  static const my_profile = "/auth/profile";
  static const update_profile = "/guardians";
  static child_profile(int id) => "/children/$id";
  static const add_child = "/children";
  static delete_child(int id) => "/children/$id";
  static const children = "/children";
  static const child_sessions = "/book-transactions/for-child-profile/sessions";
  static const device_info = "/device-tokens";
  //!SECTION

  //SECTION - Settings
  static const hobbies = "/hobbies";
  static const special_cases = "/special-cases";
  static const settings = "/settings";
  static const targeted_question = "/targeted-questions";
  static const points_data = "/user-points";
  static const points_settings = "/point-setting";
  //!SECTION

  //SECTION - Home
  static const ads = "/ads";
  //!SECTION

  //SECTION - Categories
  static const categories = "/categories?only_parents=1";
  static subcategories(int id) =>
      "/categories?only_children=1&parent_ids[]=$id";
  //!SECTION

  //SECTION - Centers
  static const centers = "/providers";
  static center_details(int id) => "/providers/$id";
  static const center_reviews = "/reviews";
  static const centers_on_map = "/provider/location";
  //!SECTION

  //SECTION - Activities
  static const sub_activities = "/sub-activities";
  static const main_activities = "/activities";
  static activity(int id) => "/sub-activities/$id";
  static const numbered_session = "/numbered-sessions";
  static const session_values = "/session-values";
  static const specific_session_values = "/specific-day-values";

  //SECTION - Favourites
  static const add_to_favourite = "/favorites";
  static const favourites = "/favorites";
  static remove_from_favourite(int id) => "/favorites/$id";
  //!SECTION

  //!SECTION

  //SECTION - Booking
  static const book_activity = "/book-transactions";
  static const book_custom_session = "/custom-sessions";
  static const user_books = "/book-transactions";
  static book_details(int id) => "/book-transactions/$id";
  static delete_book(int id) => "/book-transactions/$id";
  static const rate_book = "/reviews";
  static const checkout_payment = "/book-check-out";
  //!SECTION

  //SECTION - Notifications
  static const unreadNotificationsCount = "/notification/unread";
  static const notifications = "/notifications";
  //!SECTION

  //SECTION - Chat
  static const rooms = "/rooms";
  static const messages = "/messages";
  //!SECTION
}
