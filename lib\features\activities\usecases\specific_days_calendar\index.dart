import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/features/activities/usecases/specific_days_calendar/widgets/specific_days_sessions.dart';

import 'controller.dart';

class SpecificDaysDataWidget extends StatelessWidget {
  final DateTime? minDate, maxDate;
  final int id;
  final int specificDayId;
  const SpecificDaysDataWidget({
    super.key,
    this.minDate,
    this.maxDate,
    required this.id,
    required this.specificDayId,
  });

  @override
  Widget build(BuildContext context) {
    Get.put(
      SpecificDayController(
        id: id,
        minDate: minDate,
        maxDate: maxDate,
      ),
      tag: "$id",
    );
    return SpecificDaysSessions(
      id: id,
      specificDayId: specificDayId,
    );
  }
}
