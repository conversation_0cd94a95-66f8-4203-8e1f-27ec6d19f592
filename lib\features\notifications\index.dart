import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/notifications/notification.dart';
import 'package:gopal/core/services/pagination/options/list_view.dart';
import 'package:gopal/features/widgets/general_componenets/pages/general_page.dart';

import 'controller.dart';
import 'widgets/card.dart';

class NotificationsPage extends StatelessWidget {
  const NotificationsPage({super.key});

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    NotificationsPageController controller =
        Get.put(NotificationsPageController());
    return GeneralPage(
      titleText: LocaleKeys.notifications.tr(),
      withBackground: false,
      child: ListViewPagination<NotificationModel>.separated(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        tag: ControllersTags.notifications_pager,
        fetchApi: controller.fetchData,
        fromJson: NotificationModel.fromJson,
        separatorBuilder: (_, __) => const Gap(12),
        itemBuilder: (context, index, notification) =>
            NotificationCard(notification: notification),
      ),
    );
  }
}
