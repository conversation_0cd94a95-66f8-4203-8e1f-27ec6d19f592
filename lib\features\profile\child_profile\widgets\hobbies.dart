import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/repo.dart';

import '../models/child.dart';

class ChildProfileHobbies extends StatelessWidget {
  final ChildDetails child;
  const ChildProfileHobbies(this.child, {super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            LocaleKeys.hobbies.tr(),
            style: context.textTheme.titleMedium!.copyWith(
              color: StyleRepo.berkeleyBlue,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        SingleChildScrollView(
          padding: const EdgeInsetsDirectional.only(start: 16, end: 8),
          scrollDirection: Axis.horizontal,
          child: Row(
            children: List.generate(
              child.hobbies.length,
              (index) => Padding(
                padding: const EdgeInsetsDirectional.only(end: 8),
                child: Chip(label: Text(child.hobbies[index].name)),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
