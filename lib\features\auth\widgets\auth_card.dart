import 'package:flutter/material.dart';
import 'package:gopal/core/style/repo.dart';

class AuthCard extends StatelessWidget {
  final Widget child;
  final EdgeInsets margin;
  const AuthCard(
      {super.key,
      required this.child,
      this.margin = const EdgeInsets.symmetric(horizontal: 32)});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.symmetric(horizontal: 32),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: StyleRepo.grey.shade50,
        borderRadius: BorderRadius.circular(45),
        boxShadow: StyleRepo.authCardShadow,
      ),
      child: child,
    );
  }
}
