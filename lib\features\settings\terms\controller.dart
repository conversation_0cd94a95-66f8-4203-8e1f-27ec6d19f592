import 'package:get/get.dart';

import '../../../core/services/rest_api/rest_api.dart';

class TermsPageController extends GetxController {
  Request<String> request = Request(
    endPoint: EndPoints.settings,
    params: {"type": "TERMS_AND_CONDITIONS"},
    fromJson: (json) => json['text'],
  );

  @override
  void onInit() {
    request.perform();
    super.onInit();
  }

  @override
  void onClose() {
    request.stop();
    super.onClose();
  }
}
