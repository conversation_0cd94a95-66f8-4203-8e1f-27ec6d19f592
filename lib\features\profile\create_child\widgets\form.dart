import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/constants/gender.dart';
import 'package:gopal/core/models/selection.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/utils/validator.dart';
import 'package:gopal/core/widgets/error_widget.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/widgets/general_componenets/form_field_error.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';
import 'package:multi_dropdown/multiselect_dropdown.dart';

import '../../../usecases/gender/widgets/gender_tile.dart';
import '../controller.dart';

class ChildCreationForm extends StatelessWidget {
  const ChildCreationForm({super.key});

  @override
  Widget build(BuildContext context) {
    CreateChildPageController controller = Get.find();
    return Form(
      key: controller.form,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWell(
            onTap: () => controller.pickImage(),
            child: Row(
              children: [
                Stack(
                  children: [
                    Obx(() {
                      if (controller.image.isEmpty) {
                        return Container(
                          height: 75,
                          width: 75,
                          margin: const EdgeInsets.symmetric(horizontal: 3),
                          decoration: BoxDecoration(
                            color: StyleRepo.white,
                            shape: BoxShape.circle,
                            border: Border.all(color: StyleRepo.blueViolet),
                          ),
                          alignment: Alignment.center,
                          child: SvgIcon(Assets.icons.profilePlaceholder.path),
                        );
                      } else {
                        return AppImage(
                          path: controller.image,
                          type: controller.image.startsWith("http")
                              ? ImageType.CachedNetwork
                              : ImageType.File,
                          height: 75,
                          width: 75,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(45),
                          ),
                          margin: const EdgeInsets.symmetric(horizontal: 3),
                        );
                      }
                    }),
                    Positioned(
                      bottom: 10,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: StyleRepo.turquoise,
                          shape: BoxShape.circle,
                          border: Border.all(color: StyleRepo.white),
                        ),
                        child: SvgIcon(
                          Assets.icons.edit.path,
                          color: StyleRepo.white,
                        ),
                      ),
                    ),
                  ],
                ),
                const Gap(12),
                Text(
                  LocaleKeys.select_photo.tr(),
                  style: context.textTheme.titleMedium!.copyWith(
                      color: StyleRepo.grey.shade700,
                      decoration: TextDecoration.underline),
                ),
              ],
            ),
          ),
          const Gap(10),
          Text(
            LocaleKeys.first_name.tr(),
            style: context.textTheme.bodyMedium!
                .copyWith(color: StyleRepo.berkeleyBlue),
          ),
          const Gap(12),
          TextFormField(
            controller: controller.firstName,
            decoration: InputDecoration(
              hintText: LocaleKeys.enter_first_name.tr(),
              suffixIcon: Padding(
                padding: const EdgeInsets.all(14.0),
                child: SvgIcon(Assets.icons.edit.path),
              ),
            ),
            validator: (str) {
              String? error;
              error = Validator.notNullValidation(str);
              if (error != null) return error;
              error = Validator.lengthValidation(str);
              if (error != null) return error;
              return null;
            },
          ),
          const Gap(20),
          Text(
            LocaleKeys.last_name.tr(),
            style: context.textTheme.bodyMedium!
                .copyWith(color: StyleRepo.berkeleyBlue),
          ),
          const Gap(12),
          TextFormField(
            controller: controller.lastName,
            decoration: InputDecoration(
              hintText: LocaleKeys.enter_last_name.tr(),
              suffixIcon: Padding(
                padding: const EdgeInsets.all(14.0),
                child: SvgIcon(Assets.icons.edit.path),
              ),
            ),
            validator: (str) {
              String? error;
              error = Validator.notNullValidation(str);
              if (error != null) return error;
              error = Validator.lengthValidation(str);
              if (error != null) return error;
              return null;
            },
          ),
          const Gap(20),
          Text(
            LocaleKeys.date_of_birth.tr(),
            style: context.textTheme.bodyMedium!
                .copyWith(color: StyleRepo.berkeleyBlue),
          ),
          const Gap(12),
          GestureDetector(
            onTap: () => controller.pickDate(context),
            child: TextFormField(
              controller: controller.birthDateController,
              style: TextStyle(color: StyleRepo.grey.shade800),
              enabled: false,
              decoration: InputDecoration(
                hintText: LocaleKeys.date_of_birth.tr(),
                suffixIcon: Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: SvgIcon(Assets.icons.calenderOutlined.path),
                ),
              ),
              validator: Validator.notNullValidation,
            ),
          ),
          const Gap(20),
          Text(
            LocaleKeys.hobbies.tr(),
            style: context.textTheme.bodyMedium!
                .copyWith(color: StyleRepo.berkeleyBlue),
          ),
          const Gap(12),
          FormField<int>(
            initialValue: controller.initHobbies.length,
            validator: (length) {
              if (length == 0) {
                return LocaleKeys.this_field_is_required.tr();
              }
              return null;
            },
            builder: (state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ChipTheme(
                    data: const ChipThemeData(),
                    child: controller.hobbiesRequest.listBuilder(
                      loader: (_) => const FieldLoadingWidget(),
                      errorBuilder: (context, response) => FieldErrorWidget(
                        error: response.message,
                        onRefresh: () => controller.hobbiesRequest.refresh(),
                      ),
                      builder: (context, data) {
                        return MultiSelectDropDown<Selection>(
                          controller: controller.hobbiesController,
                          onOptionSelected: (selections) {
                            log("Selections: ${selections.map((e) => e.value!.id).toList()}");
                            state.didChange(selections.length);
                          },
                          chipConfig: const ChipConfig(
                            padding: EdgeInsets.symmetric(horizontal: 12),
                          ),
                          inputDecoration: BoxDecoration(
                            border: Border.fromBorderSide(Theme.of(context)
                                .inputDecorationTheme
                                .enabledBorder!
                                .borderSide),
                            borderRadius: (Theme.of(context)
                                    .inputDecorationTheme
                                    .enabledBorder! as OutlineInputBorder)
                                .borderRadius,
                          ),
                          options: const [],
                          suffixIcon: const Icon(Icons.keyboard_arrow_down),
                        );
                      },
                    ),
                  ),
                  if (state.hasError) FormFieldError(error: state.errorText!),
                ],
              );
            },
          ),
          const Gap(20),
          Text(
            LocaleKeys.gender.tr(),
            style: context.textTheme.bodyMedium!
                .copyWith(color: StyleRepo.berkeleyBlue),
          ),
          const Gap(12),
          FormField<Gender>(
            initialValue: controller.gender.value,
            validator: Validator.notNullValidation,
            builder: (state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  GestureDetector(
                    onTap: () async {
                      Gender? result = await controller.pickGender();
                      if (result != null) {
                        state.didChange(result);
                      }
                    },
                    child: Obx(
                      () {
                        if (controller.gender.hasData) {
                          return GenderTile(controller.gender.value!);
                        } else {
                          return Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(
                                vertical: 12, horizontal: 16),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: StyleRepo.blueViolet),
                            ),
                            child: Text(
                              LocaleKeys.select_gender.tr(),
                              style: context.textTheme.bodyMedium!.copyWith(
                                fontWeight: FontWeight.w600,
                                color: StyleRepo.blueViolet,
                              ),
                            ),
                          );
                        }
                      },
                    ),
                  ),
                  if (state.hasError) FormFieldError(error: state.errorText!),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
