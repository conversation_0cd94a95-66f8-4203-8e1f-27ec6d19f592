import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/models/chat/messages/messages.dart';
import 'package:gopal/core/services/player/player.dart';
import 'package:gopal/core/style/repo.dart';

import '../../controller.dart';

class VoiceMessageContent extends StatelessWidget {
  final Message message;
  const VoiceMessageContent({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    ChatRoomPageController controller = Get.find();

    if (message is SendMessage) {
      return Row(
        children: [
          const Gap(8),
          const SizedBox(
            height: 25,
            width: 25,
            child: CircularProgressIndicator(color: StyleRepo.white),
          ),
          Expanded(
            child: AudioFileWaveforms(
              padding: const EdgeInsets.only(top: 12, right: 12, left: 12),
              waveformType: WaveformType.fitWidth,
              size: Size(MediaQuery.of(context).size.width * .5, 45.0),
              playerController: PlayerController(),
              waveformData: message.asSendMessage.content.asVoice.waves,
              playerWaveStyle: PlayerWaveStyle(
                fixedWaveColor: StyleRepo.grey.shade300,
                liveWaveColor: StyleRepo.turquoise,
                // spacing: 8.0,
                showBottom: false,
              ),
            ),
          ),
        ],
      );
    } else if (message is ReceivedMessage) {
      Widget staticWaves = AudioFileWaveforms(
        padding: const EdgeInsets.only(top: 12, right: 12, left: 12),
        waveformType: WaveformType.fitWidth,
        size: Size(MediaQuery.of(context).size.width * .5, 45.0),
        playerController: PlayerController(),
        waveformData: message.asReceivedMessage.content.asVoice.waves,
        playerWaveStyle: PlayerWaveStyle(
          fixedWaveColor: StyleRepo.grey.shade300,
          liveWaveColor: StyleRepo.turquoise,
          // spacing: 8.0,
          showBottom: false,
        ),
      );
      return Obx(
        () {
          if (controller.playingVoiceMessage != message.asReceivedMessage.id) {
            return Row(
              children: [
                IconButton(
                  onPressed: () =>
                      controller.playVoice(message.asReceivedMessage),
                  icon: const Icon(Icons.play_arrow_rounded),
                ),
                Expanded(child: staticWaves),
              ],
            );
          } else {
            if (AudioPlayer().playerStatus.isInitializing) {
              return Row(
                children: [
                  const Gap(12),
                  const SizedBox(
                    height: 25,
                    width: 25,
                    child: CircularProgressIndicator(),
                  ),
                  const Gap(12),
                  Expanded(child: staticWaves),
                ],
              );
            } else if (AudioPlayer().playerStatus.isError) {
              return Row(
                children: [
                  IconButton(
                    onPressed: () =>
                        controller.playVoice(message.asReceivedMessage),
                    icon: const Icon(Icons.refresh),
                  ),
                  Expanded(child: staticWaves),
                ],
              );
            }
            return Row(
              children: [
                StreamBuilder(
                  stream: AudioPlayer().player!.onPlayerStateChanged,
                  builder: (context, snapData) {
                    if (!snapData.hasData) {
                      return IconButton(
                        onPressed: () => AudioPlayer().play(),
                        icon: const Icon(Icons.play_arrow_rounded),
                      );
                    } else {
                      if (snapData.data == PlayerState.paused ||
                          snapData.data == PlayerState.initialized) {
                        return IconButton(
                          onPressed: () => AudioPlayer().play(),
                          icon: const Icon(Icons.play_arrow_rounded),
                        );
                      } else if (snapData.data == PlayerState.playing) {
                        return IconButton(
                          onPressed: () => AudioPlayer().pause(),
                          icon: const Icon(Icons.pause_rounded),
                        );
                      }
                    }
                    return const SizedBox();
                  },
                ),
                Expanded(
                  child: AudioFileWaveforms(
                    padding:
                        const EdgeInsets.only(top: 12, right: 12, left: 12),
                    waveformType: WaveformType.fitWidth,
                    size: Size(MediaQuery.of(context).size.width * .5, 45.0),
                    playerController: AudioPlayer().player!,
                    waveformData:
                        message.asReceivedMessage.content.asVoice.waves,
                    playerWaveStyle: PlayerWaveStyle(
                      fixedWaveColor: StyleRepo.grey.shade300,
                      liveWaveColor: StyleRepo.turquoise,
                      seekLineColor: StyleRepo.turquoise,
                      // spacing: 8.0,
                      showBottom: false,
                    ),
                  ),
                ),
              ],
            );
          }
        },
      );
    }
    return const Placeholder();
  }
}
