import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/pages/rounded_page.dart';

import 'controller.dart';
import 'widgets/activities.dart';
import 'widgets/calendar.dart';
import 'widgets/heading_card.dart';
import 'widgets/hobbies.dart';
import 'widgets/loading.dart';

class ChildProfilePage extends GetView<ChildProfilePageController> {
  const ChildProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return RoundedPage(
      title: Text(LocaleKeys.child_profile.tr()),
      child: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: Assets.images.childProfileBackground.provider(),
            fit: BoxFit.cover,
          ),
        ),
        child: controller.childRequest.objectBuilder(
          withRefresh: true,
          onRefresh: () async => await controller.refreshData(),
          loader: (_) => const ChildProfileLoading(),
          builder: (context, child) {
            return RefreshIndicator(
              onRefresh: () => controller.childRequest.refresh(),
              child: ListView(
                padding: const EdgeInsets.symmetric(vertical: 12),
                children: [
                  ChildProfileHeadingCard(child),
                  const Gap(16),
                  ChildProfileHobbies(child),
                  const Gap(16),
                  if (child.hasMedicalInfo)
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        LocaleKeys.medical_information.tr(),
                        style: context.textTheme.titleMedium!.copyWith(
                          color: StyleRepo.berkeleyBlue,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  if (child.hasMedicalInfo) const Gap(8),
                  if (child.hasMedicalInfo)
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      decoration: BoxDecoration(
                        color: StyleRepo.white,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: StyleRepo.elevation_3,
                      ),
                      child: Column(
                        children: [
                          if (child.allergies != null)
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "${LocaleKeys.allergies.tr()}: ",
                                  style:
                                      context.textTheme.titleMedium!.copyWith(
                                    color: StyleRepo.berkeleyBlue,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                Expanded(
                                  child: Text(child.allergies!),
                                ),
                              ],
                            ),
                          if (child.specialCases.isNotEmpty) const Gap(12),
                          if (child.specialCases.isNotEmpty)
                            Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "${LocaleKeys.special_case.tr()}: ",
                                  style:
                                      context.textTheme.titleMedium!.copyWith(
                                    color: StyleRepo.berkeleyBlue,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                Expanded(
                                  child: Text(
                                    List.generate(
                                            child.specialCases.length,
                                            (index) =>
                                                child.specialCases[index].name)
                                        .join(", "),
                                  ),
                                ),
                              ],
                            ),
                        ],
                      ),
                    ),
                  const Gap(16),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      LocaleKeys.booked_activities.tr(),
                      style: context.textTheme.titleMedium!.copyWith(
                        color: StyleRepo.berkeleyBlue,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const Gap(8),
                  const ChildCalendar(),
                  const Gap(16),
                  const ChildSessions(),
                  const Gap(16),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
