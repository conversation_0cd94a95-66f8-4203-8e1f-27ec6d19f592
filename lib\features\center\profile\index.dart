import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/services/share/deep_link.dart';
import 'package:gopal/core/services/share/share.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/features/activities/widgets/activities_list.dart';
import 'package:gopal/features/center/profile/widgets/about.dart';
import 'package:gopal/features/center/profile/widgets/pictures.dart';
import 'package:gopal/features/settings/usecases/support/widgets/support_fab.dart';
import 'package:gopal/features/widgets/general_componenets/pages/rounded_page.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';
import 'package:gopal/features/widgets/horizontal_list.dart';
import 'package:gopal/features/widgets/location_row.dart';

import 'controller.dart';
import 'models/nav.dart';
import 'widgets/branches.dart';
import 'widgets/categories.dart';
import 'widgets/header.dart';
import 'widgets/loading.dart';
import 'widgets/reviews.dart';
import 'widgets/statistics.dart';

class CenterProfilePage extends StatelessWidget {
  late final CenterProfilePageController controller;
  CenterProfilePage({super.key}) {
    controller = Get.find(tag: "${(Get.arguments as CenterProfilePageNav).id}");
  }

  @override
  Widget build(BuildContext context) {
    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () async {
        DeepLinks.pageBack(context);
        return false;
      },
      child: RoundedPage(
        title: Text(LocaleKeys.center.tr()),
        mustHasBack: true,
        actions: [
          IconButton(
            onPressed: () => ShareHelper.shareCenter(controller.nav.id),
            icon: SvgIcon(Assets.icons.share.path),
          ),
        ],
        floatingActionButton: SupportFab(
          page: Pages.center,
          data: controller.nav.id,
        ),
        child: controller.centerRequest.objectBuilder(
          withRefresh: true,
          onRefresh: controller.refreshData,
          loader: (_) => const CenterProfileLoading(),
          builder: (context, center) {
            return Column(
              children: [
                const Gap(12),
                CenterProfileHeader(center: center),
                const Gap(16),
                const Divider(height: 0),
                Expanded(
                  child: ListView(
                    children: [
                      CenterCategories(categories: center.categories),
                      const Gap(16),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: LocationRow(branch: center.mainBranch),
                      ),
                      const Gap(16),
                      CenterStatistics(
                        activitiesCount: center.activitiesNumber,
                        booksCount: center.booksCount,
                      ),
                      const Gap(16),
                      CenterPictures(center: center),
                      const Gap(16),
                      AboutCenter(description: center.description),
                      if (center.branches.isNotEmpty) const Gap(16),
                      if (center.branches.isNotEmpty)
                        CenterBranches(branches: center.branches),
                      const Gap(16),
                      controller.activitiesRequest.listBuilder(
                        loader:
                            (_) => HorizontalListLoading(
                              title: LocaleKeys.provided_activities.tr(),
                            ),
                        errorBuilder:
                            (_, response) => HorizontalListError(
                              title: LocaleKeys.provided_activities.tr(),
                              error: response.message,
                              onRefresh:
                                  () => controller.activitiesRequest.refresh(),
                            ),
                        builder: (context, activities) {
                          if (activities.isEmpty) return null;
                          return ActivitiesList(
                            title: LocaleKeys.provided_activities.tr(),
                            activities: activities,
                            seeAll: () => controller.seeAllActivities(),
                          );
                        },
                      ),
                      const Gap(16),
                      CenterReviews(centerId: center.id),
                      const Gap(16),
                    ],
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
}
