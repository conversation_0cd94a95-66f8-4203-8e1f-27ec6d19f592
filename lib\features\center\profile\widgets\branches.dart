import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/services/location.dart';
import 'package:gopal/core/style/repo.dart';

import '../models/center_details.dart';

class CenterBranches extends StatelessWidget {
  final List<CenterBranch> branches;
  const CenterBranches({super.key, required this.branches});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            LocaleKeys.branches_location.tr(),
            style: context.textTheme.titleLarge!
                .copyWith(color: StyleRepo.blueViolet),
          ),
        ),
        const Gap(8),
        SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsetsDirectional.only(start: 16, end: 8),
          child: Row(
            children: branches
                .map(
                  (e) => GestureDetector(
                    onTap: () => LocationUtils.openMapApp(e.lat, e.lng),
                    child: Container(
                      margin: const EdgeInsetsDirectional.only(end: 8),
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 4),
                      decoration: BoxDecoration(
                        color: StyleRepo.grey.shade200,
                        borderRadius: BorderRadius.circular(45),
                      ),
                      child: Text(
                        e.cityName + (e.name != null ? " - ${e.name}" : ""),
                        style: const TextStyle(
                          decoration: TextDecoration.underline,
                          decorationColor: StyleRepo.blueViolet,
                        ),
                      ),
                    ),
                  ),
                )
                .toList(),
          ),
        ),
      ],
    );
  }
}
