import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/backgrounds/auth_bg.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';

import '../../usecases/phone_field/phone_field.dart';
import 'controller.dart';

class ChangePhonePage extends StatelessWidget {
  const ChangePhonePage({super.key});

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    ChangePhonePageController controller = Get.put(ChangePhonePageController());
    return Scaffold(
      body: AuthBackground(
        child: Column(
          children: [
            SafeArea(
              child: SizedBox(
                width: double.infinity,
                height: 50,
                child: NavigationToolbar(
                  middle: Text(
                    "Change Phone number",
                    style: context.textTheme.headlineSmall!.copyWith(
                      color: StyleRepo.berkeleyBlue,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  leading: const BackButton(),
                ),
              ),
            ),
            Expanded(
              child: Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Assets.images.logo.image(height: 70),
                    PhoneNumberField(
                      onCountrySelection: (country) =>
                          controller.selectedCountry = country,
                      controller: controller.phone,
                      formKey: controller.formKey,
                      initCountry: controller.selectedCountry.countryCode,
                    ),
                    AppElevatedButton(
                      onTap: () => controller.confirm(),
                      child: Text(LocaleKeys.confirm.tr()),
                    ),
                    Gap(Get.height * .1),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
