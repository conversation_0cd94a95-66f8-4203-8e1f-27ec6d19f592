import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:gopal/core/models/image.dart';

class CenterLocation {
  int id;
  String name;
  ImageModel image;
  List<LatLng> locations;

  CenterLocation({
    required this.id,
    required this.name,
    required this.image,
    required this.locations,
  });

  factory CenterLocation.fromJson(Map<String, dynamic> json) => CenterLocation(
        id: json['id'],
        name: json['name'],
        image: ImageModel.tryParse(
            json['media'] is List ? null : json['media']['profile']),
        locations: List.from(
          json['providerBranchesLocation'].map<LatLng>(
            (location) => LatLng(location['lat'] * 1.0, location['lng'] * 1.0),
          ),
        ),
      );
}
