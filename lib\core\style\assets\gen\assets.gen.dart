/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:lottie/lottie.dart' as _lottie;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsFontsGen {
  const $AssetsFontsGen();

  /// File path: assets/fonts/Quicksand-Bold.ttf
  String get quicksandBold => 'assets/fonts/Quicksand-Bold.ttf';

  /// File path: assets/fonts/Quicksand-Light.ttf
  String get quicksandLight => 'assets/fonts/Quicksand-Light.ttf';

  /// File path: assets/fonts/Quicksand-Medium.ttf
  String get quicksandMedium => 'assets/fonts/Quicksand-Medium.ttf';

  /// File path: assets/fonts/Quicksand-Regular.ttf
  String get quicksandRegular => 'assets/fonts/Quicksand-Regular.ttf';

  /// File path: assets/fonts/Quicksand-SemiBold.ttf
  String get quicksandSemiBold => 'assets/fonts/Quicksand-SemiBold.ttf';

  /// File path: assets/fonts/Shamel-Bold.ttf
  String get shamelBold => 'assets/fonts/Shamel-Bold.ttf';

  /// File path: assets/fonts/Shamel-Book.ttf
  String get shamelBook => 'assets/fonts/Shamel-Book.ttf';

  /// File path: assets/fonts/Shamel-Light.ttf
  String get shamelLight => 'assets/fonts/Shamel-Light.ttf';

  /// File path: assets/fonts/Shamel-Medium.ttf
  String get shamelMedium => 'assets/fonts/Shamel-Medium.ttf';

  /// List of all assets
  List<String> get values => [
    quicksandBold,
    quicksandLight,
    quicksandMedium,
    quicksandRegular,
    quicksandSemiBold,
    shamelBold,
    shamelBook,
    shamelLight,
    shamelMedium,
  ];
}

class $AssetsIconsGen {
  const $AssetsIconsGen();

  /// File path: assets/icons/about.svg
  SvgGenImage get about => const SvgGenImage('assets/icons/about.svg');

  /// File path: assets/icons/activities.svg
  SvgGenImage get activities =>
      const SvgGenImage('assets/icons/activities.svg');

  /// File path: assets/icons/app_bar.svg
  SvgGenImage get appBar => const SvgGenImage('assets/icons/app_bar.svg');

  /// File path: assets/icons/app_bar_notifications.svg
  SvgGenImage get appBarNotifications =>
      const SvgGenImage('assets/icons/app_bar_notifications.svg');

  /// File path: assets/icons/arrow_down.svg
  SvgGenImage get arrowDown => const SvgGenImage('assets/icons/arrow_down.svg');

  /// File path: assets/icons/brain.svg
  SvgGenImage get brain => const SvgGenImage('assets/icons/brain.svg');

  /// File path: assets/icons/calendar.svg
  SvgGenImage get calendar => const SvgGenImage('assets/icons/calendar.svg');

  /// File path: assets/icons/calendar_sticker.svg
  SvgGenImage get calendarSticker =>
      const SvgGenImage('assets/icons/calendar_sticker.svg');

  /// File path: assets/icons/calender_outlined.svg
  SvgGenImage get calenderOutlined =>
      const SvgGenImage('assets/icons/calender_outlined.svg');

  /// File path: assets/icons/camera.svg
  SvgGenImage get camera => const SvgGenImage('assets/icons/camera.svg');

  /// File path: assets/icons/categories.svg
  SvgGenImage get categories =>
      const SvgGenImage('assets/icons/categories.svg');

  /// File path: assets/icons/certificate.svg
  SvgGenImage get certificate =>
      const SvgGenImage('assets/icons/certificate.svg');

  /// File path: assets/icons/chairs.svg
  SvgGenImage get chairs => const SvgGenImage('assets/icons/chairs.svg');

  /// File path: assets/icons/chat.svg
  SvgGenImage get chat => const SvgGenImage('assets/icons/chat.svg');

  /// File path: assets/icons/confirmation_sticker.svg
  SvgGenImage get confirmationSticker =>
      const SvgGenImage('assets/icons/confirmation_sticker.svg');

  /// File path: assets/icons/contact_us.svg
  SvgGenImage get contactUs => const SvgGenImage('assets/icons/contact_us.svg');

  /// File path: assets/icons/delete.svg
  SvgGenImage get delete => const SvgGenImage('assets/icons/delete.svg');

  /// File path: assets/icons/discount.svg
  SvgGenImage get discount => const SvgGenImage('assets/icons/discount.svg');

  /// File path: assets/icons/document.svg
  SvgGenImage get document => const SvgGenImage('assets/icons/document.svg');

  /// File path: assets/icons/edit.svg
  SvgGenImage get edit => const SvgGenImage('assets/icons/edit.svg');

  /// File path: assets/icons/facebook.svg
  SvgGenImage get facebook => const SvgGenImage('assets/icons/facebook.svg');

  /// File path: assets/icons/family.svg
  SvgGenImage get family => const SvgGenImage('assets/icons/family.svg');

  /// File path: assets/icons/filled_heart.svg
  SvgGenImage get filledHeart =>
      const SvgGenImage('assets/icons/filled_heart.svg');

  /// File path: assets/icons/filters.svg
  SvgGenImage get filters => const SvgGenImage('assets/icons/filters.svg');

  /// File path: assets/icons/gallery.svg
  SvgGenImage get gallery => const SvgGenImage('assets/icons/gallery.svg');

  /// File path: assets/icons/gender.svg
  SvgGenImage get gender => const SvgGenImage('assets/icons/gender.svg');

  /// File path: assets/icons/gender_sticker.svg
  SvgGenImage get genderSticker =>
      const SvgGenImage('assets/icons/gender_sticker.svg');

  /// File path: assets/icons/gift.svg
  SvgGenImage get gift => const SvgGenImage('assets/icons/gift.svg');

  /// File path: assets/icons/help.svg
  SvgGenImage get help => const SvgGenImage('assets/icons/help.svg');

  /// File path: assets/icons/holding.svg
  SvgGenImage get holding => const SvgGenImage('assets/icons/holding.svg');

  /// File path: assets/icons/home.svg
  SvgGenImage get home => const SvgGenImage('assets/icons/home.svg');

  /// File path: assets/icons/instagram.svg
  SvgGenImage get instagram => const SvgGenImage('assets/icons/instagram.svg');

  /// File path: assets/icons/languages.svg
  SvgGenImage get languages => const SvgGenImage('assets/icons/languages.svg');

  /// File path: assets/icons/link.svg
  SvgGenImage get link => const SvgGenImage('assets/icons/link.svg');

  /// File path: assets/icons/location.svg
  SvgGenImage get location => const SvgGenImage('assets/icons/location.svg');

  /// File path: assets/icons/location_icon.svg
  SvgGenImage get locationIcon =>
      const SvgGenImage('assets/icons/location_icon.svg');

  /// File path: assets/icons/menu.svg
  SvgGenImage get menu => const SvgGenImage('assets/icons/menu.svg');

  /// File path: assets/icons/notifications.svg
  SvgGenImage get notifications =>
      const SvgGenImage('assets/icons/notifications.svg');

  /// File path: assets/icons/offer_circle.svg
  SvgGenImage get offerCircle =>
      const SvgGenImage('assets/icons/offer_circle.svg');

  /// File path: assets/icons/outlined_heart.svg
  SvgGenImage get outlinedHeart =>
      const SvgGenImage('assets/icons/outlined_heart.svg');

  /// File path: assets/icons/phone.svg
  SvgGenImage get phone => const SvgGenImage('assets/icons/phone.svg');

  /// File path: assets/icons/play.svg
  SvgGenImage get play => const SvgGenImage('assets/icons/play.svg');

  /// File path: assets/icons/price.svg
  SvgGenImage get price => const SvgGenImage('assets/icons/price.svg');

  /// File path: assets/icons/profile.svg
  SvgGenImage get profile => const SvgGenImage('assets/icons/profile.svg');

  /// File path: assets/icons/profile_placeholder.svg
  SvgGenImage get profilePlaceholder =>
      const SvgGenImage('assets/icons/profile_placeholder.svg');

  /// File path: assets/icons/profile_sticker.svg
  SvgGenImage get profileSticker =>
      const SvgGenImage('assets/icons/profile_sticker.svg');

  /// File path: assets/icons/rate_sticker.svg
  SvgGenImage get rateSticker =>
      const SvgGenImage('assets/icons/rate_sticker.svg');

  /// File path: assets/icons/rejection.svg
  SvgGenImage get rejection => const SvgGenImage('assets/icons/rejection.svg');

  /// File path: assets/icons/search.svg
  SvgGenImage get search => const SvgGenImage('assets/icons/search.svg');

  /// File path: assets/icons/season.svg
  SvgGenImage get season => const SvgGenImage('assets/icons/season.svg');

  /// File path: assets/icons/send.svg
  SvgGenImage get send => const SvgGenImage('assets/icons/send.svg');

  /// File path: assets/icons/share.svg
  SvgGenImage get share => const SvgGenImage('assets/icons/share.svg');

  /// File path: assets/icons/side_bar.svg
  SvgGenImage get sideBar => const SvgGenImage('assets/icons/side_bar.svg');

  /// File path: assets/icons/stack.svg
  SvgGenImage get stack => const SvgGenImage('assets/icons/stack.svg');

  /// File path: assets/icons/star.svg
  SvgGenImage get star => const SvgGenImage('assets/icons/star.svg');

  /// File path: assets/icons/star_half.svg
  SvgGenImage get starHalf => const SvgGenImage('assets/icons/star_half.svg');

  /// File path: assets/icons/statistics_card_bg_left.svg
  SvgGenImage get statisticsCardBgLeft =>
      const SvgGenImage('assets/icons/statistics_card_bg_left.svg');

  /// File path: assets/icons/statistics_small_card_bg_right.svg
  SvgGenImage get statisticsSmallCardBgRight =>
      const SvgGenImage('assets/icons/statistics_small_card_bg_right.svg');

  /// File path: assets/icons/support_sticker.svg
  SvgGenImage get supportSticker =>
      const SvgGenImage('assets/icons/support_sticker.svg');

  /// File path: assets/icons/target_age.svg
  SvgGenImage get targetAge => const SvgGenImage('assets/icons/target_age.svg');

  /// File path: assets/icons/targetted_questions.svg
  SvgGenImage get targettedQuestions =>
      const SvgGenImage('assets/icons/targetted_questions.svg');

  /// File path: assets/icons/terms.svg
  SvgGenImage get terms => const SvgGenImage('assets/icons/terms.svg');

  /// File path: assets/icons/time.svg
  SvgGenImage get time => const SvgGenImage('assets/icons/time.svg');

  /// File path: assets/icons/trusted.svg
  SvgGenImage get trusted => const SvgGenImage('assets/icons/trusted.svg');

  /// File path: assets/icons/verification_mark.svg
  SvgGenImage get verificationMark =>
      const SvgGenImage('assets/icons/verification_mark.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
    about,
    activities,
    appBar,
    appBarNotifications,
    arrowDown,
    brain,
    calendar,
    calendarSticker,
    calenderOutlined,
    camera,
    categories,
    certificate,
    chairs,
    chat,
    confirmationSticker,
    contactUs,
    delete,
    discount,
    document,
    edit,
    facebook,
    family,
    filledHeart,
    filters,
    gallery,
    gender,
    genderSticker,
    gift,
    help,
    holding,
    home,
    instagram,
    languages,
    link,
    location,
    locationIcon,
    menu,
    notifications,
    offerCircle,
    outlinedHeart,
    phone,
    play,
    price,
    profile,
    profilePlaceholder,
    profileSticker,
    rateSticker,
    rejection,
    search,
    season,
    send,
    share,
    sideBar,
    stack,
    star,
    starHalf,
    statisticsCardBgLeft,
    statisticsSmallCardBgRight,
    supportSticker,
    targetAge,
    targettedQuestions,
    terms,
    time,
    trusted,
    verificationMark,
  ];
}

class $AssetsImagesGen {
  const $AssetsImagesGen();

  /// File path: assets/images/about.png
  AssetGenImage get about => const AssetGenImage('assets/images/about.png');

  /// File path: assets/images/auth_background_1.png
  AssetGenImage get authBackground1 =>
      const AssetGenImage('assets/images/auth_background_1.png');

  /// File path: assets/images/auth_background_2.png
  AssetGenImage get authBackground2 =>
      const AssetGenImage('assets/images/auth_background_2.png');

  /// File path: assets/images/auth_background_test_1.png
  AssetGenImage get authBackgroundTest1 =>
      const AssetGenImage('assets/images/auth_background_test_1.png');

  /// File path: assets/images/auth_background_test_2.png
  AssetGenImage get authBackgroundTest2 =>
      const AssetGenImage('assets/images/auth_background_test_2.png');

  /// File path: assets/images/background_3.png
  AssetGenImage get background3 =>
      const AssetGenImage('assets/images/background_3.png');

  /// File path: assets/images/boarding_1.png
  AssetGenImage get boarding1 =>
      const AssetGenImage('assets/images/boarding_1.png');

  /// File path: assets/images/boarding_2.png
  AssetGenImage get boarding2 =>
      const AssetGenImage('assets/images/boarding_2.png');

  /// File path: assets/images/boarding_3.png
  AssetGenImage get boarding3 =>
      const AssetGenImage('assets/images/boarding_3.png');

  /// File path: assets/images/boy.png
  AssetGenImage get boy => const AssetGenImage('assets/images/boy.png');

  /// File path: assets/images/chat_background.png
  AssetGenImage get chatBackground =>
      const AssetGenImage('assets/images/chat_background.png');

  /// File path: assets/images/child_profile_background.png
  AssetGenImage get childProfileBackground =>
      const AssetGenImage('assets/images/child_profile_background.png');

  /// File path: assets/images/girl.png
  AssetGenImage get girl => const AssetGenImage('assets/images/girl.png');

  /// File path: assets/images/invite_friends.png
  AssetGenImage get inviteFriends =>
      const AssetGenImage('assets/images/invite_friends.png');

  /// File path: assets/images/logo.png
  AssetGenImage get logo => const AssetGenImage('assets/images/logo.png');

  /// File path: assets/images/panorama.jpg
  AssetGenImage get panorama =>
      const AssetGenImage('assets/images/panorama.jpg');

  /// File path: assets/images/rounded_app_bar_background.png
  AssetGenImage get roundedAppBarBackground =>
      const AssetGenImage('assets/images/rounded_app_bar_background.png');

  /// File path: assets/images/side_bar_background.png
  AssetGenImage get sideBarBackground =>
      const AssetGenImage('assets/images/side_bar_background.png');

  /// List of all assets
  List<AssetGenImage> get values => [
    about,
    authBackground1,
    authBackground2,
    authBackgroundTest1,
    authBackgroundTest2,
    background3,
    boarding1,
    boarding2,
    boarding3,
    boy,
    chatBackground,
    childProfileBackground,
    girl,
    inviteFriends,
    logo,
    panorama,
    roundedAppBarBackground,
    sideBarBackground,
  ];
}

class $AssetsLottiesGen {
  const $AssetsLottiesGen();

  /// File path: assets/lotties/coming_soon.json
  LottieGenImage get comingSoon =>
      const LottieGenImage('assets/lotties/coming_soon.json');

  /// File path: assets/lotties/loading.json
  LottieGenImage get loading =>
      const LottieGenImage('assets/lotties/loading.json');

  /// File path: assets/lotties/no_data.json
  LottieGenImage get noData =>
      const LottieGenImage('assets/lotties/no_data.json');

  /// File path: assets/lotties/splash_1.json
  LottieGenImage get splash1 =>
      const LottieGenImage('assets/lotties/splash_1.json');

  /// File path: assets/lotties/splash_2.json
  LottieGenImage get splash2 =>
      const LottieGenImage('assets/lotties/splash_2.json');

  /// File path: assets/lotties/splash_3.json
  LottieGenImage get splash3 =>
      const LottieGenImage('assets/lotties/splash_3.json');

  /// List of all assets
  List<LottieGenImage> get values => [
    comingSoon,
    loading,
    noData,
    splash1,
    splash2,
    splash3,
  ];
}

class $AssetsTranslationsGen {
  const $AssetsTranslationsGen();

  /// File path: assets/translations/ar.json
  String get ar => 'assets/translations/ar.json';

  /// File path: assets/translations/en.json
  String get en => 'assets/translations/en.json';

  /// List of all assets
  List<String> get values => [ar, en];
}

class Assets {
  const Assets._();

  static const $AssetsFontsGen fonts = $AssetsFontsGen();
  static const $AssetsIconsGen icons = $AssetsIconsGen();
  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsLottiesGen lotties = $AssetsLottiesGen();
  static const $AssetsTranslationsGen translations = $AssetsTranslationsGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName, {this.size, this.flavors = const {}});

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = true,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.medium,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({AssetBundle? bundle, String? package}) {
    return AssetImage(_assetName, bundle: bundle, package: package);
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = false;

  const SvgGenImage.vec(this._assetName, {this.size, this.flavors = const {}})
    : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter:
          colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class LottieGenImage {
  const LottieGenImage(this._assetName, {this.flavors = const {}});

  final String _assetName;
  final Set<String> flavors;

  _lottie.LottieBuilder lottie({
    Animation<double>? controller,
    bool? animate,
    _lottie.FrameRate? frameRate,
    bool? repeat,
    bool? reverse,
    _lottie.LottieDelegates? delegates,
    _lottie.LottieOptions? options,
    void Function(_lottie.LottieComposition)? onLoaded,
    _lottie.LottieImageProviderFactory? imageProviderFactory,
    Key? key,
    AssetBundle? bundle,
    Widget Function(BuildContext, Widget, _lottie.LottieComposition?)?
    frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    double? width,
    double? height,
    BoxFit? fit,
    AlignmentGeometry? alignment,
    String? package,
    bool? addRepaintBoundary,
    FilterQuality? filterQuality,
    void Function(String)? onWarning,
    _lottie.LottieDecoder? decoder,
    _lottie.RenderCache? renderCache,
    bool? backgroundLoading,
  }) {
    return _lottie.Lottie.asset(
      _assetName,
      controller: controller,
      animate: animate,
      frameRate: frameRate,
      repeat: repeat,
      reverse: reverse,
      delegates: delegates,
      options: options,
      onLoaded: onLoaded,
      imageProviderFactory: imageProviderFactory,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      package: package,
      addRepaintBoundary: addRepaintBoundary,
      filterQuality: filterQuality,
      onWarning: onWarning,
      decoder: decoder,
      renderCache: renderCache,
      backgroundLoading: backgroundLoading,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
