import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/services/notifications_counter/counter.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/home/<USER>/about.dart';
import 'package:gopal/features/home/<USER>/seasonal_activities_list.dart';
import 'package:gopal/features/home/<USER>/slider.dart';
import 'package:gopal/features/widgets/general_componenets/app_bars/general_app_bar.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';
import 'package:gopal/features/widgets/version_widget.dart';

import '../../core/config/app_builder.dart';
import '../../core/services/firebase_messaging/widgets/fcm_token.dart';
import '../widgets/horizontal_list.dart';
import 'controller.dart';
import '../activities/widgets/activities_list.dart';
import 'widgets/categories.dart';
import 'widgets/centers_list.dart';
import 'widgets/text_slider.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    AppBuilder appBuilder = Get.find<AppBuilder>();
    HomePageController controller = Get.put(HomePageController());
    return Stack(
      alignment: Alignment.topCenter,
      children: [
        RefreshIndicator(
          onRefresh: () async => await controller.refreshData(),
          child: ListView(
            padding: EdgeInsets.only(
              top: 60 + MediaQuery.viewPaddingOf(context).top,
              bottom: 16,
            ),
            children: [
              const HomeTextSlider(),
              const Gap(8),
              const CategoriesListWidget(),
              const Gap(8),
              const HomeSlider(),
              const Gap(16),
              FcmTokenWidget(),
              const Gap(16),
              controller.offeredActivitiesRequest.listBuilder(
                loader:
                    (_) => HorizontalListLoading(
                      title: LocaleKeys.todays_offers.tr(),
                    ),
                errorBuilder:
                    (_, response) => HorizontalListError(
                      title: LocaleKeys.todays_offers.tr(),
                      error: response.message,
                      onRefresh: () => controller.refreshWhenError(),
                    ),
                builder:
                    (context, activities) => ActivitiesList(
                      title: LocaleKeys.todays_offers.tr(),
                      seeAll: () => controller.seeAllTodaysOffersActivities(),
                      activities: activities,
                      isOffer: true,
                    ),
              ),
              const Gap(16),
              controller.centersRequest.listBuilder(
                errorBuilder:
                    (context, response) => HorizontalListError(
                      title: LocaleKeys.centers_near_you.tr(),
                      error: response.message,
                      onRefresh: () => controller.refreshWhenError(),
                    ),
                loader:
                    (_) => HorizontalListLoading(
                      title: LocaleKeys.centers_near_you.tr(),
                    ),
                builder: (context, data) {
                  return CentersList(
                    title: LocaleKeys.centers_near_you.tr(),
                    seeAll: () => Nav.to(Pages.centers_on_map),
                    centers: data,
                  );
                },
              ),
              const Gap(16),
              controller.recommendedActivitiesRequest.listBuilder(
                loader:
                    (_) => HorizontalListLoading(
                      title: LocaleKeys.recommended_activities.tr(),
                    ),
                errorBuilder:
                    (_, response) => HorizontalListError(
                      title: LocaleKeys.recommended_activities.tr(),
                      error: response.message,
                      onRefresh: () => controller.refreshWhenError(),
                    ),
                builder:
                    (context, activities) => ActivitiesList(
                      title: LocaleKeys.recommended_activities.tr(),
                      activities: activities,
                      seeAll: () => controller.seeAllRecommendedActivities(),
                    ),
              ),
              const Gap(16),
              controller.seasonalActivitiesRequest.listBuilder(
                loader:
                    (_) => HorizontalListLoading(
                      title: LocaleKeys.seasonal_activities.tr(),
                    ),
                errorBuilder:
                    (_, response) => HorizontalListError(
                      title: LocaleKeys.seasonal_activities.tr(),
                      error: response.message,
                      onRefresh: () => controller.refreshWhenError(),
                    ),
                builder:
                    (context, activities) => SeasonalActivitiesList(
                      title: LocaleKeys.seasonal_activities.tr(),
                      activities: activities,
                      seeAll: () => controller.seeAllSeasonalActivities(),
                    ),
              ),
              const Gap(16),
              controller.familyActivitiesRequest.listBuilder(
                loader:
                    (_) => HorizontalListLoading(
                      title: LocaleKeys.family_activities.tr(),
                    ),
                errorBuilder:
                    (_, response) => HorizontalListError(
                      title: LocaleKeys.family_activities.tr(),
                      error: response.message,
                      onRefresh: () => controller.refreshWhenError(),
                    ),
                builder:
                    (context, activities) => ActivitiesList(
                      title: LocaleKeys.family_activities.tr(),
                      seeAll: () => controller.seeAllFamilyActivities(),
                      activities: activities,
                    ),
              ),
              const Gap(16),
              const AboutSection(),
              const Gap(16),
              const Center(child: VersionWidget()),
              const Gap(16),
            ],
          ),
        ),
        GeneralAppBar(
          title: ValueListenableBuilder(
            valueListenable: appBuilder.user,
            builder: (context, user, _) {
              return Text(
                LocaleKeys.hello.tr(args: [user?.name ?? ""]),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              );
            },
          ),
          centerTitle: false,
          actions: [
            IconButton(
              onPressed: () => Nav.to(Pages.search),
              icon: const Icon(Icons.search, size: 30),
            ),
            IconButton(
              onPressed: () => Nav.to(Pages.notifications),
              icon: Obx(
                () => Badge(
                  isLabelVisible: NotificationsCounter.notificationsCount > 0,
                  label: Text(
                    "${NotificationsCounter.notificationsCount >= 10 ? "+9" : NotificationsCounter.notificationsCount}",
                  ),
                  child: Assets.icons.appBarNotifications.svg(),
                ),
              ),
            ),
            IconButton(
              onPressed: () => Scaffold.of(context).openEndDrawer(),
              icon: SvgIcon(Assets.icons.sideBar.path, color: StyleRepo.white),
            ),
          ],
        ),
      ],
    );
  }
}
