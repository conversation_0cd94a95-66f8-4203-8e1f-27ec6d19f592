import 'package:easy_localization/easy_localization.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';

enum MainDestinations {
  home,
  categories,
  my_activities,
  profile,
  ;

  String get text {
    switch (this) {
      case home:
        return LocaleKeys.home.tr();
      case categories:
        return LocaleKeys.categories.tr();
      case my_activities:
        return LocaleKeys.my_activities.tr();
      case profile:
        return LocaleKeys.profile.tr();
    }
  }

  String get iconPath {
    switch (this) {
      case home:
        return Assets.icons.home.path;
      case categories:
        return Assets.icons.categories.path;
      case my_activities:
        return Assets.icons.activities.path;
      case profile:
        return Assets.icons.profile.path;
    }
  }
}
