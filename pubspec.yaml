name: gopal
description: A new Flutter project.

publish_to: "none" # Remove this line if you wish to publish to pub.dev

# IOS
version: 1.1.8+26
# Android
# version: 1.1.8+23

environment:
  sdk: ">=3.7.2 <4.0.0"

dependencies:
  flutter:
    sdk: flutter
  cupertino_icons: ^1.0.2
  #====== Localization ========
  flutter_localizations:
    sdk: flutter
  easy_localization: ^3.0.7
  #==== State Managment =======
  get:
  #====== Storage =======
  path:
  path_provider: ^2.1.3
  get_storage:
  flutter_secure_storage: ^9.0.0
  #===== Notification ========
  firebase_messaging: ^15.2.5
  flutter_local_notifications: ^19.0.0
  firebase_core:
  firebase_analytics:
  #========= HTTP ==========
  dio: ^5.1.2
  #========= Media =======
  cached_network_image:
  flutter_svg:
  flutter_svg_provider: ^1.0.7
  lottie: ^3.3.1
  flutter_image_compress: ^2.1.0
  # panorama_viewer:
  image_picker: ^1.0.8
  wechat_assets_picker: ^9.5.0
  video_player: ^2.8.5
  video_thumbnail: ^0.5.3
  chewie: ^1.7.5
  just_audio:
  audio_waveforms: ^1.0.5
  file_icon: ^1.0.0
  #======= Theme ========
  theme_tailor_annotation:
  animated_theme_switcher:
  #========= UI =========
  shimmer:
  gap:
  blur:
  pinput: ^5.0.0
  country_picker: ^2.0.26
  multi_dropdown: ^2.1.4
  flutter_staggered_grid_view: ^0.7.0
  readmore: ^3.0.0
  flutter_rating_bar: ^4.0.1
  table_calendar: ^3.0.9
  flutter_html:
  carousel_slider: ^5.0.0
  # ======== Utils =======
  uuid: ^4.4.0
  feedback: ^3.1.0
  fluttertoast:
  url_launcher: ^6.3.0
  geolocator:
  map_launcher: ^3.4.0
  timeago: ^3.7.0
  device_info_plus: ^9.1.2
  flutter_device_id: ^1.0.1
  permission_handler: ^11.3.1
  file_picker: ^10.1.9
  google_maps_flutter:
  app_settings: ^5.1.1
  package_info_plus: ^8.3.0
  firebase_crashlytics: ^4.3.6
  # ===== Share ====
  app_links: ^6.4.0
  share_plus: ^11.0.0
  qr_flutter: ^4.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints:
  build_runner:
  theme_tailor:
  flutter_launcher_icons: ^0.13.1

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  remove_alpha_ios: true
  image_path: "assets/images/logo.png"

flutter_gen:
  output: lib/core/style/assets/gen/
  integrations:
    flutter_svg: true
    lottie: true

flutter:
  uses-material-design: true

  assets:
    - assets/
    - assets/images/
    - assets/icons/
    - assets/fonts/
    - assets/translations/
    - assets/lotties/

  fonts:
    - family: Quicksand
      fonts:
        - asset: assets/fonts/Quicksand-Bold.ttf
          weight: 700
        - asset: assets/fonts/Quicksand-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Quicksand-Medium.ttf
          weight: 500
        - asset: assets/fonts/Quicksand-Regular.ttf
          weight: 400
        - asset: assets/fonts/Quicksand-Light.ttf
          weight: 300
    - family: Shamel
      fonts:
        - asset: assets/fonts/Shamel-Book.ttf
          weight: 800
        - asset: assets/fonts/Shamel-Bold.ttf
          weight: 700
        - asset: assets/fonts/Shamel-Medium.ttf
          weight: 500
        - asset: assets/fonts/Shamel-Light.ttf
          weight: 300
