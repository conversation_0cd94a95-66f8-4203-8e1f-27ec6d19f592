import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gopal/core/widgets/shimmer_loading.dart';

class ListCardsLoading extends StatelessWidget {
  const ListCardsLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      itemCount: 4,
      separatorBuilder: (_, __) => const Gap(12),
      itemBuilder: (_, __) => AspectRatio(
        aspectRatio: 16 / 9,
        child: ShimmerWidget.card(borderRadius: BorderRadius.circular(12)),
      ),
    );
  }
}
