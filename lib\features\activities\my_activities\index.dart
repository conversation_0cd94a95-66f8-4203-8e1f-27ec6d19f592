import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/pages/general_page.dart';

import 'controller.dart';
import 'models/tabs.dart';
import 'widgets/completed.dart';
import 'widgets/favourites.dart';
import 'widgets/pending.dart';
import 'widgets/progress.dart';

class MyActivitiesPage extends StatelessWidget {
  const MyActivitiesPage({super.key});

  @override
  Widget build(BuildContext context) {
    MyActivitiesPageController controller =
        Get.put(MyActivitiesPageController());
    return GeneralPage(
      title: Center(
        child: InkWell(
          onTap: () => controller.selectChildren(),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Obx(
                () {
                  if (controller.children.isEmpty) {
                    return Text(LocaleKeys.all_children.tr());
                  } else {
                    return Text(
                      controller.children.first.firstName +
                          (controller.children.length >= 2
                              ? " + ${controller.children.length - 1}"
                              : ""),
                    );
                  }
                },
              ),
              const Icon(Icons.keyboard_arrow_down),
            ],
          ),
        ),
      ),
      withBackground: false,
      child: Column(
        children: [
          const Gap(24),
          TabBar(
            controller: controller.tabController,
            tabs: MyActivitiesTabs.values.map((e) => Text(e.text)).toList(),
            tabAlignment: TabAlignment.fill,
            labelPadding:
                const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
            labelStyle:
                const TextStyle(fontWeight: FontWeight.w600, fontSize: 16),
            unselectedLabelColor: StyleRepo.grey,
          ),
          Expanded(
            child: TabBarView(
              controller: controller.tabController,
              children: const [
                MyPendingActivities(),
                MyProgressActivities(),
                MyCompletedActivities(),
                MyFavouritesActivities(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
