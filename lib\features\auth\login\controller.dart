import 'dart:developer';

import 'package:country_picker/country_picker.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:gopal/core/config/app_builder.dart';
import 'package:gopal/core/config/role.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/features/auth/verification/models/nav.dart';

import '../../../core/services/rest_api/rest_api.dart';
import '../../usecases/toast/toast.dart';

class LoginPageController extends GetxController {
  AppBuilder appBuilder = Get.find();
  final form = GlobalKey<FormState>();

  TextEditingController phone = TextEditingController();

  Country selectedCountry = Country.parse("SA");

  sendPhoneNumber() async {
    if (!form.currentState!.validate()) {
      return;
    }
    String phoneNumber =
        "+${selectedCountry.phoneCode}${phone.text.replaceFirst(RegExp(r'^0+'), '')}";
    // String phoneNumber = phone.text;
    log(phoneNumber);
    FocusManager.instance.primaryFocus?.unfocus();
    ResponseModel response = await Request(
      endPoint: EndPoints.send_code,
      method: RequestMethod.Post,
      body: {
        "phone": phoneNumber,
      },
    ).perform();
    if (response.success) {
      Nav.to(
        Pages.verification,
        arguments: VerificationPageNav(phone: phoneNumber),
      );
    } else {
      Toast.show(message: response.message);
    }
  }

  guestSignin() async {
    appBuilder.setRole(const Guest());
    await appBuilder.role.initialize();
    Nav.offUntil(appBuilder.role.landing, (_) => false);
  }
}
