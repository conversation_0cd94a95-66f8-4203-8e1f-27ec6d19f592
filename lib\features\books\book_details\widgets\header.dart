import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/models/book/book.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/features/center/profile/models/nav.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

class BookDetailsHeader extends StatelessWidget {
  final BookedActivity book;
  const BookDetailsHeader({super.key, required this.book});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: InkWell(
        onTap: () => Nav.to(
          Pages.center,
          arguments: CenterProfilePageNav(book.activity.center.id),
          preventDuplicates: false,
        ),
        child: Row(
          children: [
            AppImage(
              path: book.activity.center.profileImage.small,
              type: ImageType.CachedNetwork,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(25),
                boxShadow: StyleRepo.elevation,
              ),
              height: 50,
              width: 50,
            ),
            const Gap(10),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    book.activity.center.name,
                    textAlign: TextAlign.start,
                    style: context.textTheme.bodyLarge!.copyWith(
                      fontWeight: FontWeight.w600,
                      color: StyleRepo.blueViolet,
                    ),
                  ),
                  RatingBar.builder(
                    itemSize: 20,
                    unratedColor: Colors.transparent,
                    allowHalfRating: true,
                    ignoreGestures: true,
                    initialRating: book.activity.center.rate,
                    itemBuilder: (_, __) {
                      return SvgIcon(
                        Assets.icons.star.path,
                        color: StyleRepo.yellow.shade600,
                      );
                    },
                    onRatingUpdate: (_) {},
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
