import 'package:flutter/material.dart';
import 'package:gopal/core/models/category.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/features/categories/category/models/nav.dart';

class CenterCategories extends StatelessWidget {
  final List<Category> categories;
  const CenterCategories({super.key, required this.categories});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsetsDirectional.only(start: 16, end: 8),
      scrollDirection: Axis.horizontal,
      child: Row(
        children: categories
            .map((category) => Padding(
                  padding: const EdgeInsetsDirectional.only(end: 8),
                  child: GestureDetector(
                    onTap: () => Nav.to(Pages.category,
                        arguments: CategoryPageNav(category)),
                    child: Chip(label: Text(category.name)),
                  ),
                ))
            .toList(),
      ),
    );
  }
}
