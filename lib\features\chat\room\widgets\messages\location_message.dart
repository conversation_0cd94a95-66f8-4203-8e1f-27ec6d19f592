import 'package:flutter/material.dart';
import 'package:gopal/core/services/location.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';

import '../../../../../core/models/chat/messages/messages.dart';

class LocationMessageContent extends StatelessWidget {
  final Message message;
  const LocationMessageContent({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    if (message is SendMessage) {
      return AspectRatio(
        aspectRatio: 2,
        child: Stack(
          children: [
            Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: StyleRepo.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: StyleRepo.white),
                image: DecorationImage(
                  image: MemoryImage(
                    message.asSendMessage.content.asLocation.mapImage,
                  ),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            Center(
              child: Padding(
                padding: const EdgeInsets.only(bottom: 12),
                child: Icon(
                  Icons.location_on,
                  color: StyleRepo.red.shade800,
                  size: 24,
                ),
              ),
            ),
          ],
        ),
      );
    } else {
      return InkWell(
        onTap: () => LocationUtils.openMapApp(
          message.asReceivedMessage.content.asLocation.location.latitude,
          message.asReceivedMessage.content.asLocation.location.longitude,
        ),
        child: AspectRatio(
          aspectRatio: 2,
          child: Stack(
            children: [
              AppImage(
                path: message.asReceivedMessage.content.asLocation.map.medium,
                type: ImageType.CachedNetwork,
                width: double.infinity,
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: StyleRepo.white,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: StyleRepo.white),
                ),
              ),
              Center(
                child: Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Icon(
                    Icons.location_on,
                    color: StyleRepo.red.shade800,
                    size: 24,
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }
  }
}
