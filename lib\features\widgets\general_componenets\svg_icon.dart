import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

class SvgIcon extends StatelessWidget {
  final String path;
  final Color? color;
  final double? size;
  const SvgIcon(this.path, {super.key, this.color, this.size});

  @override
  Widget build(BuildContext context) {
    Color? effectiveColor = color ?? IconTheme.of(context).color;
    return SvgPicture.asset(
      path,
      height: size,
      width: size,
      colorFilter: effectiveColor == null
          ? null
          : ColorFilter.mode(effectiveColor, BlendMode.srcATop),
    );
  }
}
