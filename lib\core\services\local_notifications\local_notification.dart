import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:get/get.dart';
import 'package:gopal/core/models/notifications/notification.dart';

import '../../routes.dart';
import '../notifications_click/notification_click.dart' as notification_action;

class LocalNotificationService {
  static late FlutterLocalNotificationsPlugin notificationsService;

  static init({bool isBackGroundInit = false}) async {
    notificationsService = FlutterLocalNotificationsPlugin();

    bool willDoNavigation = false;
    NotificationAppLaunchDetails? notificationOppenedApp =
        await notificationsService.getNotificationAppLaunchDetails();
    if (notificationOppenedApp != null &&
        notificationOppenedApp.didNotificationLaunchApp) {
      willDoNavigation = await notificationClick(
          notificationOppenedApp.notificationResponse!,
          isTerminated: true);
    }

    //Andoid 13 permission
    if (GetPlatform.isAndroid && !isBackGroundInit) {
      await notificationsService
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()!
          .requestNotificationsPermission();
    }

    var android =
        const AndroidInitializationSettings('@drawable/notification_icon');
    var ios = const DarwinInitializationSettings();
    var initSettings = InitializationSettings(android: android, iOS: ios);
    notificationsService.initialize(
      initSettings,
      onDidReceiveNotificationResponse: (details) => notificationClick(details),
    );

    return willDoNavigation;
  }

  static showNotification({
    required int id,
    required String title,
    required String subTitle,
    required String payload,
  }) async {
    var android = AndroidNotificationDetails(
      'gopal',
      'gopal_channel',
      importance: Importance.max,
      priority: Priority.max,
      groupKey: 'group',
      setAsGroupSummary: true,
      enableVibration: true,
      groupAlertBehavior: GroupAlertBehavior.all,
      styleInformation: InboxStyleInformation(
        [subTitle],
        contentTitle: title,
        // summaryText: '',
      ),
    );
    var ios = const DarwinNotificationDetails();
    var platform = NotificationDetails(android: android, iOS: ios);
    await notificationsService.show(
      id,
      title,
      subTitle,
      platform,
      payload: payload,
    );
  }

  static Future<bool> notificationClick(NotificationResponse details,
      {bool isTerminated = false}) async {
    if (details.payload != null) {
      Map<String, dynamic>? data;
      try {
        data = jsonDecode(details.payload!);
        log('###########################');
        log("$data");
        log('##############################');
      } catch (_) {}
      if (data != null) {
        NotificationModel notification = NotificationModel.fromJson(data);
        (Pages page, dynamic arguments, bool preventDuplicates)? result =
            await notification_action.notificationClick(notification);
        if (result != null) {
          if (isTerminated) {
            Nav.offUntil(result.$1, (_) => false, arguments: result.$2);
          } else {
            Nav.to(result.$1,
                arguments: result.$2, preventDuplicates: result.$3);
          }
          return true;
        }
      }
    }
    return false;
  }

  static Future<bool> areNotificationsEnabled() async {
    if (Platform.isAndroid) {
      // Check for Android
      final bool isNotificationEnabled = await notificationsService
              .resolvePlatformSpecificImplementation<
                  AndroidFlutterLocalNotificationsPlugin>()
              ?.areNotificationsEnabled() ??
          false;

      return isNotificationEnabled;
    } else if (Platform.isIOS) {
      // Check for iOS
      final IOSFlutterLocalNotificationsPlugin? iosImplementation =
          notificationsService.resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>();

      if (iosImplementation != null) {
        // On iOS, you need to request permission first before checking.
        final bool? isGranted = await iosImplementation.requestPermissions(
            alert: true, badge: true, sound: true);

        return isGranted ?? false;
      }
    }

    // For platforms other than Android and iOS, return false (or you can add more checks).
    return false;
  }
}
