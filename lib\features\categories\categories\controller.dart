import 'package:get/get.dart';
import 'package:gopal/core/models/category.dart';

import '../../../core/services/rest_api/rest_api.dart';

class CategoriesPageController extends GetxController {
  Request<Category> categoriesRequest = Request(
    endPoint: EndPoints.categories,
    fromJson: Category.fromJson,
  );

  @override
  void onInit() {
    categoriesRequest.perform();
    super.onInit();
  }

  @override
  void onClose() {
    categoriesRequest.stop();
    super.onClose();
  }
}
