import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/style/repo.dart';

const double _kAppBarHeight = 70;

class GeneralAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Widget title;
  final List<Widget>? actions;
  final bool centerTitle;
  final bool mustHasBack;
  const GeneralAppBar(
      {super.key,
      required this.title,
      this.actions,
      this.centerTitle = true,
      this.mustHasBack = false});

  @override
  Widget build(BuildContext context) {
    final ModalRoute<dynamic>? parentRoute = ModalRoute.of(context);

    return Container(
      height: _kAppBarHeight + MediaQuery.of(context).viewPadding.top,
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        gradient: StyleRepo.generalAppBarGradient,
        borderRadius: BorderRadius.circular(10),
      ),
      child: Safe<PERSON>rea(
        child: IconTheme(
          data: const IconThemeData(color: StyleRepo.white),
          child: DefaultTextStyle(
            textAlign: TextAlign.center,
            style: context.textTheme.headlineSmall!
                .copyWith(fontWeight: FontWeight.w700, color: StyleRepo.white),
            child: NavigationToolbar(
              leading:
                  mustHasBack || (parentRoute?.impliesAppBarDismissal ?? false)
                      ? const BackButton()
                      : null,
              middle: title,
              centerMiddle: centerTitle,
              trailing: actions != null
                  ? Row(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: actions!,
                    )
                  : null,
            ),
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize =>
      Size.fromHeight(_kAppBarHeight + Get.mediaQuery.viewPadding.top);
}
