# Diff Details

Date : 2024-12-31 10:39:18

Directory d:\\work\\ixCoders\\current\\go_pal\\go_pal_flutter\\lib

Total : 128 files,  2402 codes, -158 comments, 178 blanks, all 2422 lines

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [lib/core/config/app_builder.dart](/lib/core/config/app_builder.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/core/config/defaults.dart](/lib/core/config/defaults.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/core/config/role.dart](/lib/core/config/role.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/core/constants/controllers_tags.dart](/lib/core/constants/controllers_tags.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/core/localization/codegen_loader.g.dart](/lib/core/localization/codegen_loader.g.dart) | Dart | 72 | 0 | 0 | 72 |
| [lib/core/localization/strings.dart](/lib/core/localization/strings.dart) | Dart | 30 | 0 | 0 | 30 |
| [lib/core/models/activity/activity.dart](/lib/core/models/activity/activity.dart) | Dart | -47 | 0 | -6 | -53 |
| [lib/core/models/activity/constants/price_type.dart](/lib/core/models/activity/constants/price_type.dart) | Dart | 15 | 0 | 2 | 17 |
| [lib/core/models/activity/constants/type.dart](/lib/core/models/activity/constants/type.dart) | Dart | 41 | 0 | 3 | 44 |
| [lib/core/models/activity/main_activity.dart](/lib/core/models/activity/main_activity.dart) | Dart | 34 | 0 | 6 | 40 |
| [lib/core/models/activity/set.dart](/lib/core/models/activity/set.dart) | Dart | 11 | 0 | 3 | 14 |
| [lib/core/models/activity/subactivity.dart](/lib/core/models/activity/subactivity.dart) | Dart | 47 | 0 | 6 | 53 |
| [lib/core/models/book/book.dart](/lib/core/models/book/book.dart) | Dart | 11 | 0 | 1 | 12 |
| [lib/core/models/book/payment_status.dart](/lib/core/models/book/payment_status.dart) | Dart | 25 | 0 | 3 | 28 |
| [lib/core/models/constants/support_number.dart](/lib/core/models/constants/support_number.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/core/models/image.dart](/lib/core/models/image.dart) | Dart | 7 | 0 | 1 | 8 |
| [lib/core/models/notifications/data/main_notification_data.dart](/lib/core/models/notifications/data/main_notification_data.dart) | Dart | 16 | 0 | 1 | 17 |
| [lib/core/models/notifications/notification.dart](/lib/core/models/notifications/notification.dart) | Dart | 5 | 1 | 0 | 6 |
| [lib/core/models/notifications/type.dart](/lib/core/models/notifications/type.dart) | Dart | 3 | 1 | 0 | 4 |
| [lib/core/models/user/main_user.dart](/lib/core/models/user/main_user.dart) | Dart | 5 | 0 | 0 | 5 |
| [lib/core/routes.dart](/lib/core/routes.dart) | Dart | 20 | 0 | 0 | 20 |
| [lib/core/services/deep_link.dart](/lib/core/services/deep_link.dart) | Dart | 0 | -83 | -9 | -92 |
| [lib/core/services/firebase_messaging/firebase_messaging.dart](/lib/core/services/firebase_messaging/firebase_messaging.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/core/services/firebase_messaging/notifications_actions.dart](/lib/core/services/firebase_messaging/notifications_actions.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/core/services/local_notifications/local_notification.dart](/lib/core/services/local_notifications/local_notification.dart) | Dart | 21 | 4 | 5 | 30 |
| [lib/core/services/notification_controller.dart](/lib/core/services/notification_controller.dart) | Dart | 0 | 0 | -2 | -2 |
| [lib/core/services/notifications_click/notification_click.dart](/lib/core/services/notifications_click/notification_click.dart) | Dart | 11 | 0 | 0 | 11 |
| [lib/core/services/notifications_counter/counter.dart](/lib/core/services/notifications_counter/counter.dart) | Dart | 0 | 1 | 0 | 1 |
| [lib/core/services/pusher.dart](/lib/core/services/pusher.dart) | Dart | 0 | -89 | -13 | -102 |
| [lib/core/services/rest_api/api_service.dart](/lib/core/services/rest_api/api_service.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/core/services/rest_api/constants/end_points.dart](/lib/core/services/rest_api/constants/end_points.dart) | Dart | 5 | 0 | 0 | 5 |
| [lib/core/services/rest_api/handlers/success_handler.dart](/lib/core/services/rest_api/handlers/success_handler.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/core/services/rest_api/models/exceptions.dart](/lib/core/services/rest_api/models/exceptions.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/core/services/rest_api/utilitis/parser.dart](/lib/core/services/rest_api/utilitis/parser.dart) | Dart | -10 | 0 | 3 | -7 |
| [lib/core/services/share/share.dart](/lib/core/services/share/share.dart) | Dart | 44 | 0 | 6 | 50 |
| [lib/core/style/assets/gen/assets.gen.dart](/lib/core/style/assets/gen/assets.gen.dart) | Dart | 21 | 9 | 9 | 39 |
| [lib/core/utils/social_urls.dart](/lib/core/utils/social_urls.dart) | Dart | 16 | 0 | 4 | 20 |
| [lib/features/activities/activitiy_details/controller.dart](/lib/features/activities/activitiy_details/controller.dart) | Dart | -205 | -6 | -31 | -242 |
| [lib/features/activities/activitiy_details/index.dart](/lib/features/activities/activitiy_details/index.dart) | Dart | 12 | 0 | 0 | 12 |
| [lib/features/activities/activitiy_details/models/activity.dart](/lib/features/activities/activitiy_details/models/activity.dart) | Dart | -58 | 0 | -2 | -60 |
| [lib/features/activities/activitiy_details/models/price_type.dart](/lib/features/activities/activitiy_details/models/price_type.dart) | Dart | -15 | 0 | -2 | -17 |
| [lib/features/activities/activitiy_details/models/set.dart](/lib/features/activities/activitiy_details/models/set.dart) | Dart | -11 | 0 | -3 | -14 |
| [lib/features/activities/activitiy_details/models/type.dart](/lib/features/activities/activitiy_details/models/type.dart) | Dart | -41 | 0 | -3 | -44 |
| [lib/features/activities/activitiy_details/widgets/calendar.dart](/lib/features/activities/activitiy_details/widgets/calendar.dart) | Dart | -142 | 0 | -5 | -147 |
| [lib/features/activities/activitiy_details/widgets/header.dart](/lib/features/activities/activitiy_details/widgets/header.dart) | Dart | 11 | 0 | 0 | 11 |
| [lib/features/activities/activitiy_details/widgets/open_sessions.dart](/lib/features/activities/activitiy_details/widgets/open_sessions.dart) | Dart | -109 | 0 | -4 | -113 |
| [lib/features/activities/activitiy_details/widgets/sets.dart](/lib/features/activities/activitiy_details/widgets/sets.dart) | Dart | -80 | 0 | 0 | -80 |
| [lib/features/activities/activitiy_details/widgets/specific_days_sessions.dart](/lib/features/activities/activitiy_details/widgets/specific_days_sessions.dart) | Dart | -85 | 0 | -4 | -89 |
| [lib/features/activities/main_activity/bindings.dart](/lib/features/activities/main_activity/bindings.dart) | Dart | 10 | 0 | 4 | 14 |
| [lib/features/activities/main_activity/controller.dart](/lib/features/activities/main_activity/controller.dart) | Dart | 26 | 0 | 6 | 32 |
| [lib/features/activities/main_activity/index.dart](/lib/features/activities/main_activity/index.dart) | Dart | 34 | 0 | 4 | 38 |
| [lib/features/activities/main_activity/models/nav.dart](/lib/features/activities/main_activity/models/nav.dart) | Dart | 4 | 0 | 2 | 6 |
| [lib/features/activities/main_activity/models/sub_activity_data.dart](/lib/features/activities/main_activity/models/sub_activity_data.dart) | Dart | 98 | 0 | 6 | 104 |
| [lib/features/activities/main_activity/widgets/card.dart](/lib/features/activities/main_activity/widgets/card.dart) | Dart | 271 | 3 | 10 | 284 |
| [lib/features/activities/main_activity/widgets/card_body.dart](/lib/features/activities/main_activity/widgets/card_body.dart) | Dart | 42 | 0 | 4 | 46 |
| [lib/features/activities/usecases/booking/booking.dart](/lib/features/activities/usecases/booking/booking.dart) | Dart | 168 | 8 | 25 | 201 |
| [lib/features/activities/usecases/open_sessions_calendar/controller.dart](/lib/features/activities/usecases/open_sessions_calendar/controller.dart) | Dart | 79 | 5 | 17 | 101 |
| [lib/features/activities/usecases/open_sessions_calendar/index.dart](/lib/features/activities/usecases/open_sessions_calendar/index.dart) | Dart | 22 | 0 | 4 | 26 |
| [lib/features/activities/usecases/open_sessions_calendar/widgets/calendar.dart](/lib/features/activities/usecases/open_sessions_calendar/widgets/calendar.dart) | Dart | 141 | 0 | 5 | 146 |
| [lib/features/activities/usecases/open_sessions_calendar/widgets/open_sessions.dart](/lib/features/activities/usecases/open_sessions_calendar/widgets/open_sessions.dart) | Dart | 113 | 0 | 4 | 117 |
| [lib/features/activities/usecases/points_selection/points_selection.dart](/lib/features/activities/usecases/points_selection/points_selection.dart) | Dart | 109 | 0 | 5 | 114 |
| [lib/features/activities/usecases/specific_days_calendar/controller.dart](/lib/features/activities/usecases/specific_days_calendar/controller.dart) | Dart | 76 | 1 | 14 | 91 |
| [lib/features/activities/usecases/specific_days_calendar/index.dart](/lib/features/activities/usecases/specific_days_calendar/index.dart) | Dart | 31 | 0 | 4 | 35 |
| [lib/features/activities/usecases/specific_days_calendar/widgets/calendar.dart](/lib/features/activities/usecases/specific_days_calendar/widgets/calendar.dart) | Dart | 141 | 0 | 5 | 146 |
| [lib/features/activities/usecases/specific_days_calendar/widgets/specific_days_sessions.dart](/lib/features/activities/usecases/specific_days_calendar/widgets/specific_days_sessions.dart) | Dart | 87 | 0 | 4 | 91 |
| [lib/features/activities/widgets/activity_card.dart](/lib/features/activities/widgets/activity_card.dart) | Dart | 1 | -17 | 0 | -16 |
| [lib/features/activities/widgets/periodic_time_info.dart](/lib/features/activities/widgets/periodic_time_info.dart) | Dart | 4 | 0 | 0 | 4 |
| [lib/features/activities/widgets/seasonal_activity_card.dart](/lib/features/activities/widgets/seasonal_activity_card.dart) | Dart | 4 | 0 | 0 | 4 |
| [lib/features/activities/widgets/sets.dart](/lib/features/activities/widgets/sets.dart) | Dart | 106 | 0 | 3 | 109 |
| [lib/features/auth/create_account/controller.dart](/lib/features/auth/create_account/controller.dart) | Dart | 3 | 0 | 0 | 3 |
| [lib/features/auth/create_account/widgets/form.dart](/lib/features/auth/create_account/widgets/form.dart) | Dart | 11 | 0 | 0 | 11 |
| [lib/features/auth/login/controller.dart](/lib/features/auth/login/controller.dart) | Dart | 12 | 1 | 2 | 15 |
| [lib/features/auth/login/index.dart](/lib/features/auth/login/index.dart) | Dart | 7 | 0 | 0 | 7 |
| [lib/features/auth/signup/bindings.dart](/lib/features/auth/signup/bindings.dart) | Dart | -10 | 0 | -3 | -13 |
| [lib/features/auth/signup/controller.dart](/lib/features/auth/signup/controller.dart) | Dart | -22 | 0 | -6 | -28 |
| [lib/features/auth/signup/index.dart](/lib/features/auth/signup/index.dart) | Dart | -69 | 0 | -4 | -73 |
| [lib/features/auth/signup/models/nav.dart](/lib/features/auth/signup/models/nav.dart) | Dart | -4 | 0 | -1 | -5 |
| [lib/features/auth/verification/index.dart](/lib/features/auth/verification/index.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/features/boarding/index.dart](/lib/features/boarding/index.dart) | Dart | 5 | 0 | 0 | 5 |
| [lib/features/books/widgets/book_card.dart](/lib/features/books/widgets/book_card.dart) | Dart | 103 | 0 | 5 | 108 |
| [lib/features/categories/categories/widgets/category_card.dart](/lib/features/categories/categories/widgets/category_card.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/features/categories/category/controller.dart](/lib/features/categories/category/controller.dart) | Dart | 15 | 0 | 1 | 16 |
| [lib/features/categories/category/widgets/sub_categories.dart](/lib/features/categories/category/widgets/sub_categories.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/features/center/centers_on_map/controller.dart](/lib/features/center/centers_on_map/controller.dart) | Dart | 30 | 0 | 6 | 36 |
| [lib/features/center/centers_on_map/index.dart](/lib/features/center/centers_on_map/index.dart) | Dart | 50 | 1 | 4 | 55 |
| [lib/features/center/centers_on_map/models/center_location.dart](/lib/features/center/centers_on_map/models/center_location.dart) | Dart | 25 | 0 | 4 | 29 |
| [lib/features/center/profile/index.dart](/lib/features/center/profile/index.dart) | Dart | 8 | 0 | 0 | 8 |
| [lib/features/center/profile/widgets/branches.dart](/lib/features/center/profile/widgets/branches.dart) | Dart | 4 | 0 | 0 | 4 |
| [lib/features/center/profile/widgets/header.dart](/lib/features/center/profile/widgets/header.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/features/chat/rooms/controller.dart](/lib/features/chat/rooms/controller.dart) | Dart | 5 | 3 | 1 | 9 |
| [lib/features/filters/controller.dart](/lib/features/filters/controller.dart) | Dart | 3 | 0 | 0 | 3 |
| [lib/features/filters/models/filters_data.dart](/lib/features/filters/models/filters_data.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/features/filters/widgets/subcategories.dart](/lib/features/filters/widgets/subcategories.dart) | Dart | 5 | 0 | 0 | 5 |
| [lib/features/home/<USER>/lib/features/home/<USER>
| [lib/features/home/<USER>/lib/features/home/<USER>
| [lib/features/home/<USER>/ad.dart](/lib/features/home/<USER>/ad.dart) | Dart | 18 | 0 | 2 | 20 |
| [lib/features/home/<USER>/about.dart](/lib/features/home/<USER>/about.dart) | Dart | 76 | 0 | 3 | 79 |
| [lib/features/home/<USER>/categories.dart](/lib/features/home/<USER>/categories.dart) | Dart | 95 | 0 | 4 | 99 |
| [lib/features/home/<USER>/centers_list.dart](/lib/features/home/<USER>/centers_list.dart) | Dart | 9 | 0 | 0 | 9 |
| [lib/features/home/<USER>/seasonal_activities_list.dart](/lib/features/home/<USER>/seasonal_activities_list.dart) | Dart | 5 | 0 | 0 | 5 |
| [lib/features/home/<USER>/text_slider.dart](/lib/features/home/<USER>/text_slider.dart) | Dart | 66 | 0 | 4 | 70 |
| [lib/features/main/widgets/drawer.dart](/lib/features/main/widgets/drawer.dart) | Dart | 21 | -2 | 1 | 20 |
| [lib/features/main/widgets/notification_switch.dart](/lib/features/main/widgets/notification_switch.dart) | Dart | 43 | 0 | 6 | 49 |
| [lib/features/notifications/controller.dart](/lib/features/notifications/controller.dart) | Dart | 7 | 0 | 0 | 7 |
| [lib/features/profile/child_profile/index.dart](/lib/features/profile/child_profile/index.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/features/profile/edit_profile/widgets/form.dart](/lib/features/profile/edit_profile/widgets/form.dart) | Dart | 3 | 0 | 0 | 3 |
| [lib/features/profile/my_profile/index.dart](/lib/features/profile/my_profile/index.dart) | Dart | 24 | 0 | 0 | 24 |
| [lib/features/profile/my_profile/models/profile.dart](/lib/features/profile/my_profile/models/profile.dart) | Dart | 2 | 0 | 1 | 3 |
| [lib/features/profile/my_profile/widgets/children.dart](/lib/features/profile/my_profile/widgets/children.dart) | Dart | 2 | 0 | 0 | 2 |
| [lib/features/search/controller.dart](/lib/features/search/controller.dart) | Dart | 63 | 0 | 11 | 74 |
| [lib/features/search/index.dart](/lib/features/search/index.dart) | Dart | 46 | 1 | 4 | 51 |
| [lib/features/search/widgets/actitvities.dart](/lib/features/search/widgets/actitvities.dart) | Dart | 59 | 0 | 4 | 63 |
| [lib/features/search/widgets/centers.dart](/lib/features/search/widgets/centers.dart) | Dart | 60 | 0 | 4 | 64 |
| [lib/features/settings/invitation/controller.dart](/lib/features/settings/invitation/controller.dart) | Dart | 40 | 0 | 11 | 51 |
| [lib/features/settings/invitation/index.dart](/lib/features/settings/invitation/index.dart) | Dart | 67 | 1 | 4 | 72 |
| [lib/features/settings/privacy/controller.dart](/lib/features/settings/privacy/controller.dart) | Dart | 17 | 0 | 2 | 19 |
| [lib/features/settings/privacy/index.dart](/lib/features/settings/privacy/index.dart) | Dart | 11 | -1 | 0 | 10 |
| [lib/features/settings/usecases/points/controller.dart](/lib/features/settings/usecases/points/controller.dart) | Dart | 30 | 0 | 5 | 35 |
| [lib/features/settings/usecases/points/index.dart](/lib/features/settings/usecases/points/index.dart) | Dart | 126 | 1 | 4 | 131 |
| [lib/features/settings/usecases/points/models/point_setting.dart](/lib/features/settings/usecases/points/models/point_setting.dart) | Dart | 18 | 0 | 3 | 21 |
| [lib/features/settings/usecases/support/index.dart](/lib/features/settings/usecases/support/index.dart) | Dart | 15 | 0 | 0 | 15 |
| [lib/features/usecases/gender/widgets/gender_avatar.dart](/lib/features/usecases/gender/widgets/gender_avatar.dart) | Dart | 76 | 0 | 4 | 80 |
| [lib/features/usecases/gender/widgets/gender_tile.dart](/lib/features/usecases/gender/widgets/gender_tile.dart) | Dart | -13 | 0 | 0 | -13 |
| [lib/features/widgets/available_bubble_cilpper.dart](/lib/features/widgets/available_bubble_cilpper.dart) | Dart | -68 | -1 | -7 | -76 |
| [lib/features/widgets/backgrounds/auth_bg.dart](/lib/features/widgets/backgrounds/auth_bg.dart) | Dart | 1 | 0 | 0 | 1 |
| [lib/features/widgets/general_componenets/buttons/elevated_button.dart](/lib/features/widgets/general_componenets/buttons/elevated_button.dart) | Dart | 3 | 0 | 0 | 3 |
| [lib/features/widgets/general_componenets/buttons/outlined_button.dart](/lib/features/widgets/general_componenets/buttons/outlined_button.dart) | Dart | -2 | 0 | 0 | -2 |
| [lib/main.dart](/lib/main.dart) | Dart | -10 | -1 | -2 | -13 |

[Summary](results.md) / [Details](details.md) / [Diff Summary](diff.md) / Diff Details