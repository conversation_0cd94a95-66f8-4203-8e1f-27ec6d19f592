import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';

import '../../../activities/widgets/calendar_activitiy_card.dart';
import '../controller.dart';

class ChildSessions extends StatelessWidget {
  const ChildSessions({super.key});

  @override
  Widget build(BuildContext context) {
    ChildProfilePageController controller = Get.find();

    return Obx(
      () {
        if (controller.calendarSessions.loading) {
          return Assets.lotties.loading.lottie();
        } else if (controller.calendarSessions.hasError) {
          return Assets.lotties.noData.lottie();
        } else {
          if (!controller.calendarSessions.value!.keys
              .contains(controller.selectedDate.day)) {
            return Assets.lotties.noData.lottie();
          }
          return ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: controller
                .calendarSessions.value![controller.selectedDate.day]!.length,
            separatorBuilder: (_, __) => const Gap(12),
            itemBuilder: (context, index) => CalendarActivitiyCard(
              session: controller
                  .calendarSessions.value![controller.selectedDate.day]![index],
            ),
          );
        }
      },
    );
  }
}
