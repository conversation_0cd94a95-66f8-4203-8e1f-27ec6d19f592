import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/services/pagination/options/list_view.dart';
import 'package:gopal/features/center/models/center_review.dart';
import 'package:gopal/features/center/widgets/review_card.dart';
import 'package:gopal/features/widgets/general_componenets/pages/rounded_page.dart';

import 'controller.dart';

class CenterReviewsPage extends GetView<CenterReviewsPageController> {
  const CenterReviewsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return RoundedPage(
      title: Text(LocaleKeys.reviews.tr()),
      child: ListViewPagination.separated(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        tag: ControllersTags.reviews_pager,
        fetchApi: controller.fetchData,
        fromJson: CenterReview.from<PERSON>son,
        itemBuilder: (context, index, review) => ReviewCard(review: review),
        separatorBuilder: (_, __) => const Divider(),
      ),
    );
  }
}
