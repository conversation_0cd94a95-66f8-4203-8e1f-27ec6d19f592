import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/features/widgets/general_componenets/pages/rounded_page.dart';

import 'controller.dart';

class TermsPage extends StatelessWidget {
  const TermsPage({super.key});

  @override
  Widget build(BuildContext context) {
    TermsPageController controller = Get.put(TermsPageController());
    return RoundedPage(
      title: Text(LocaleKeys.terms_and_conditions.tr()),
      child: controller.request.listBuilder(
        builder: (context, html) {
          return SingleChildScrollView(child: Html(data: html.first));
        },
      ),
    );
  }
}
