import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:video_thumbnail/video_thumbnail.dart';

class VideoPreviewPageController extends GetxController {
  final String video;

  VideoPreviewPageController({required this.video});

  late TextEditingController textController;

  final Rx<bool> _isLoaded = false.obs;
  bool get isLoaded => _isLoaded.value;
  set isLoaded(bool value) => _isLoaded.value = value;

  Uint8List? thumb;

  loadThumb() async {
    thumb = await VideoThumbnail.thumbnailData(
      video: video,
      imageFormat: ImageFormat.JPEG,
      maxWidth: 128,
      quality: 25,
    );
    isLoaded = true;
  }

  @override
  void onInit() {
    textController = TextEditingController();
    loadThumb();
    super.onInit();
  }

  sendVideo() async {
    Get.back(result: (thumb, textController.text));
  }

  @override
  void onClose() {
    textController.dispose();
    super.onClose();
  }
}
