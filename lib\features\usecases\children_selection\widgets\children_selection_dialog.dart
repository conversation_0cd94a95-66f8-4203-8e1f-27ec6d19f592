import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/user/main_child.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/error_widget.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/outlined_button.dart';

import '../controller.dart';

class ChildrenSelectionDialog extends StatelessWidget {
  final int? activityId;
  final List<int> initialSelected;
  final bool withAll;
  final bool isMultiSelect;
  final bool seperateParent;

  static Future<List<MainChild>?> open({
    int? activityId,
    List<int>? initialSelected,
    bool withAll = false,
    bool isMultiSelect = true,
    required bool seperateParent,
  }) async => await Get.dialog(
    ChildrenSelectionDialog(
      activityId: activityId,
      initialSelected: initialSelected ?? [],
      withAll: withAll,
      isMultiSelect: isMultiSelect,
      seperateParent: seperateParent,
    ),
  );

  ChildrenSelectionDialog({
    super.key,
    this.activityId,
    this.initialSelected = const [],
    this.withAll = false,
    this.isMultiSelect = true,
    required this.seperateParent,
  }) {
    if (!isMultiSelect) {
      assert(!isMultiSelect && !withAll);
    }
  }

  @override
  Widget build(BuildContext context) {
    ChildrenSelectionController controller = Get.put(
      ChildrenSelectionController(
        activityId: activityId,
        initialSelected: initialSelected,
        isMultiSelect: isMultiSelect,
      ),
    );
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.width * .1),
        child: Material(
          color: Colors.transparent,
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                padding: const EdgeInsets.fromLTRB(0, 40, 0, 16),
                margin: const EdgeInsets.only(top: 50),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(35),
                  color: StyleRepo.white,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        LocaleKeys.select_one_child_at_least.tr(),
                        style: context.textTheme.bodyMedium!.copyWith(
                          color: StyleRepo.grey.shade700,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const Gap(12),
                    controller.childrenRequest.listBuilder(
                      errorBuilder:
                          (context, response) => FieldErrorWidget(
                            error: response.message,
                            onRefresh: () => controller.refreshData(),
                          ),
                      loader:
                          (context) => ListView.separated(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: 3,
                            separatorBuilder: (_, __) => const Gap(12),
                            itemBuilder: (_, __) => const FieldLoadingWidget(),
                          ),
                      builder: (context, children) {
                        final parent = children.firstWhere(
                          (element) => element.isMain,
                        );
                        final originalChildren =
                            children
                                .where((element) => !element.isMain)
                                .toList();
                        return Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                              ),
                              child: SizedBox(
                                height: children.length < 3 ? 150 : 250,
                                child: Scrollbar(
                                  thumbVisibility: true,
                                  child: ListView.separated(
                                    padding: const EdgeInsetsDirectional.only(
                                      end: 10,
                                    ),
                                    itemCount:
                                        originalChildren.length +
                                        (withAll ? 1 : 0),
                                    separatorBuilder: (_, __) => const Gap(12),
                                    itemBuilder: (context, index) {
                                      if (withAll && index == 0) {
                                        return Obx(
                                          () => AppOutlinedButton(
                                            onTap: () => controller.selectAll(),
                                            child: Row(
                                              children: [
                                                Checkbox.adaptive(
                                                  value:
                                                      controller.isAllSelected,
                                                  onChanged:
                                                      (_) =>
                                                          controller
                                                              .selectAll(),
                                                ),
                                                Expanded(
                                                  child: Text(
                                                    LocaleKeys.all_children
                                                        .tr(),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        );
                                      }

                                      MainChild child =
                                          originalChildren[withAll
                                              ? index - 1
                                              : index];
                                      return Obx(
                                        () => AppOutlinedButton(
                                          onTap:
                                              () => controller.add$RemoveChild(
                                                child.id!,
                                              ),
                                          child: Row(
                                            children: [
                                              Checkbox.adaptive(
                                                value:
                                                    controller.selectedChildren
                                                        .contains(child.id) ||
                                                    controller.isAllSelected,
                                                onChanged:
                                                    (_) => controller
                                                        .add$RemoveChild(
                                                          child.id!,
                                                        ),
                                              ),
                                              Expanded(
                                                child: Text(child.firstName),
                                              ),
                                            ],
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),
                              ),
                            ),
                            const Gap(16),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 12,
                              ),
                              decoration: BoxDecoration(
                                color: StyleRepo.grey.shade200,
                                borderRadius: BorderRadius.circular(15),
                              ),
                              child: Column(
                                children: [
                                  Text(
                                    tr(
                                      LocaleKeys
                                          .do_you_want_to_book_the_activity_for_you,
                                    ),
                                  ),
                                  const Gap(12),
                                  Text(
                                    tr(
                                      LocaleKeys
                                          .book_this_activity_and_be_one_of_the_participants,
                                    ),
                                  ),
                                  const Gap(12),
                                  Obx(
                                    () => AppOutlinedButton(
                                      onTap:
                                          () => controller.add$RemoveChild(
                                            parent.id!,
                                          ),
                                      backgroundColor: StyleRepo.turquoise
                                          .withValues(alpha: .2),
                                      borderColor: StyleRepo.turquoise,
                                      child: Row(
                                        children: [
                                          Checkbox.adaptive(
                                            fillColor:
                                                WidgetStateProperty.resolveWith(
                                                  (states) {
                                                    if (states.contains(
                                                      WidgetState.selected,
                                                    )) {
                                                      return StyleRepo
                                                          .turquoise;
                                                    }
                                                    return null;
                                                  },
                                                ),
                                            value:
                                                controller.selectedChildren
                                                    .contains(parent.id) ||
                                                controller.isAllSelected,
                                            onChanged:
                                                (_) =>
                                                    controller.add$RemoveChild(
                                                      parent.id!,
                                                    ),
                                          ),
                                          Expanded(
                                            child: Text(parent.firstName),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const Gap(16),
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 16,
                              ),
                              child: Obx(
                                () => AppElevatedButton(
                                  onTap:
                                      () => Get.back(
                                        result:
                                            withAll && controller.isAllSelected
                                                ? <MainChild>[]
                                                : children
                                                    .where(
                                                      (element) => controller
                                                          .selectedChildren
                                                          .contains(element.id),
                                                    )
                                                    .toList(),
                                      ),
                                  enabled:
                                      controller.selectedChildren.isNotEmpty,
                                  child: Text(LocaleKeys.confirm.tr()),
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),
              Assets.icons.profileSticker.svg(),
            ],
          ),
        ),
      ),
    );
  }
}
