import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/category.dart';

class CategoryFilter extends Category {
  RangeValues? hourPriceRange;
  RangeValues? coursePriceRange;

  static CategoryFilter initialValue =
      CategoryFilter(id: 0, name: LocaleKeys.all.tr());

  CategoryFilter({required super.id, required super.name});

  CategoryFilter.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    if (json['min_hourly_price'] != json['max_hourly_price']) {
      hourPriceRange = RangeValues(
        json['min_hourly_price'] * 1.0,
        json['max_hourly_price'] * 1.0,
      );
    }
    if (json['min_course_price'] != json['max_course_price']) {
      coursePriceRange = RangeValues(
        json['min_course_price'] * 1.0,
        json['max_course_price'] * 1.0,
      );
    }
  }
}
