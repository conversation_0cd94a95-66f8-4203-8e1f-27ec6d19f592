import 'package:get/get.dart';
import 'package:gopal/core/services/rest_api/rest_api.dart';

class PrivacyPolicyPageController extends GetxController {
  Request<String> request = Request(
    endPoint: EndPoints.settings,
    params: {"type": "PRIVACY_AND_POLICY"},
    fromJson: (json) => json['text'],
  );

  @override
  void onInit() {
    request.perform();
    super.onInit();
  }

  @override
  void onClose() {
    request.stop();
    super.onClose();
  }
}
