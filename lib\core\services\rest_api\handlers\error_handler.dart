import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:gopal/core/models/errors/version_update_data.dart';
import 'package:gopal/core/services/version_checker/checker.dart';

import '../constants/messages.dart';
import '../constants/api_error.dart';
import '../models/response_model.dart';

///General error handler for all requests
ResponseModel mainErrorHandler(DioException error) {
  switch (error.type) {
    case DioExceptionType.badResponse:
      switch (error.response!.statusCode) {
        case 401:
          return ResponseModel(
            success: false,
            statusCode: 401,
            message: APIErrorMessages.noAuth,
            errorType: UN_AUTHORIZED(),
          );
        case 403:
          return ResponseModel(
            success: false,
            statusCode: 403,
            message: APIErrorMessages.forbidden.tr(),
            errorType: FORBIDDEN(),
          );
        case 419:
          return ResponseModel(
            success: false,
            statusCode: error.response!.statusCode,
            message: error.response?.data?['message'] ??
                APIErrorMessages.validation.tr(),
            errorType: VALIDATION_WARNING_ERROR(),
          );
        case 422:
          return ResponseModel(
            success: false,
            statusCode: error.response!.statusCode,
            message: error.response?.data?['message'] ??
                APIErrorMessages.validation.tr(),
            errorType: VALIDATION_ERROR(),
          );
        case 426:
          VersionChecker().getInvalidVersionInAPI(
              VersionUpdateData.fromJson(error.response!.data), false);
          return ResponseModel(
            success: false,
            statusCode: error.response!.statusCode,
            message: error.response?.data?['message'] ??
                APIErrorMessages.validation.tr(),
            data: VersionUpdateData.fromJson(error.response!.data),
            errorType: VERSION_UPDATE(),
          );
        case 500:
          return ResponseModel(
            success: false,
            statusCode: 500,
            message: APIErrorMessages.server.tr(),
            errorType: SERVER_ERROR(),
          );
        default:
          return ResponseModel(
            success: false,
            message: APIErrorMessages.unknown,
            errorType: OTHER(),
            statusCode: error.response?.statusCode,
          );
      }
    case DioExceptionType.badCertificate:
    case DioExceptionType.unknown:
    case DioExceptionType.connectionError:
    case DioExceptionType.connectionTimeout:
      return ResponseModel(
        success: false,
        message: APIErrorMessages.noInternet.tr(),
        errorType: NO_INTERNET(),
      );
    case DioExceptionType.receiveTimeout:
      return ResponseModel(
        success: false,
        message: APIErrorMessages.noInternet.tr(),
        errorType: RECIEVE_TIMEOUT(),
      );
    case DioExceptionType.sendTimeout:
      return ResponseModel(
        success: false,
        message: APIErrorMessages.noInternet.tr(),
        errorType: SEND_TIMEOUT(),
      );
    case DioExceptionType.cancel:
      return ResponseModel(
        success: false,
        errorType: CANCELED(),
        message: APIErrorMessages.canceled,
      );
  }
}
