import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/utils/image_utils.dart';
import 'package:path_provider/path_provider.dart';

class ImagesPreviewPageController extends GetxController {
  final List<String> images;
  ImagesPreviewPageController({required this.images});

  TextEditingController textController = TextEditingController();

  List<String> compressedImages = [];

  final Rx<bool> _isLoaded = false.obs;
  bool get isLoaded => _isLoaded.value;
  set isLoaded(bool value) => _isLoaded.value = value;

  compressImages() async {
    for (var image in images) {
      String? compressedImage = await ImageUtils.compressAndGetFile(File(image),
          "${(await getTemporaryDirectory()).path}/${DateTime.now()}.jpg");

      compressedImages.add(compressedImage ?? "");
    }
    isLoaded = true;
  }

  @override
  void onInit() {
    compressImages();
    super.onInit();
  }

  sendImages() {
    Get.back(result: (compressedImages, textController.text));
  }

  @override
  void onClose() {
    textController.dispose();
    super.onClose();
  }
}
