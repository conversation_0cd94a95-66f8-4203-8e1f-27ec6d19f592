import 'package:gopal/core/models/image.dart';

class ChatRoom {
  late int id;
  late String name;
  late DateTime date;
  late ImageModel image;
  late String messagePreview;

  ChatRoom({
    required this.id,
    required this.name,
    required this.image,
    required this.date,
    this.messagePreview = "",
  });

  ChatRoom.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    date = json['latest_message_created_at'] != null
        ? DateTime.parse(json['latest_message_created_at'])
        : DateTime.parse(json['created_at']);
    name = json['providers'].first['name'];
    image = ImageModel.fromJson(
        json['providers'].first['user']['media']['profile'].first);
    messagePreview = json['latest_message']?['preview'] ?? "";
  }
}
