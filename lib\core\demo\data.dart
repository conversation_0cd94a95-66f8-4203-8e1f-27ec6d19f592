import 'dart:math';

import '../models/selection.dart';

class DemoData {
  static List<Selection> hobbies = [
    Selection(id: 1, name: 'Reading'),
    Selection(id: 2, name: 'Painting'),
    Selection(id: 3, name: 'Gardening'),
    Selection(id: 4, name: 'Cooking'),
    Selection(id: 5, name: 'Photography'),
    Selection(id: 6, name: 'Playing an instrument'),
    Selection(id: 7, name: 'Hiking'),
    Selection(id: 8, name: 'Writing'),
    Selection(id: 9, name: 'Dancing'),
    Selection(id: 10, name: 'Singing'),
    Selection(id: 11, name: 'Knitting'),
    Selection(id: 12, name: 'Fishing'),
    Selection(id: 13, name: 'Yoga'),
    Selection(id: 14, name: 'Gaming'),
    Selection(id: 15, name: 'Traveling'),
    Selection(id: 16, name: 'Swimming'),
    Selection(id: 17, name: 'Cycling'),
    Selection(id: 18, name: 'Running'),
    Selection(id: 19, name: 'Watching movies'),
    Selection(id: 20, name: 'Collecting stamps'),
  ];

  static List<Selection> specialCases = List.generate(
    Random().nextInt(5) + 5,
    (index) => Selection(id: index, name: "Special Case ${index + 1}"),
  );

  static Map<String, List<String>> allCategories = {
    "Sports": [
      "Football",
      "Basketball",
      "Swimming",
      "Tennis",
      "Squash",
      "Padel",
      "Volleyball",
      "Golf",
    ],
    "Martial Arts": [
      "Boxing",
      "Karate",
      "Taekwando",
      "Judo",
      "Muay Tai",
      "Kickboxing",
      "Kung Fu",
    ],
    "Family": [
      "Bowling",
      "Cinema",
      "Escape Room",
      "Hiking",
      "Cycling",
      "Billiards",
      "Ping Pong",
      "Paintball",
      "Safari",
    ],
    "Nursery": ["Day Nursery", "Night Nursery"],
    "Handscraft": [
      "Caligraphy",
      "Drawing",
      "Pottery Sculpture",
      "Embroidery",
      "Cooking",
    ],
    "Science": [
      "Robots",
      "Computer",
      "Islam",
      "Self development",
      "Mental Math",
      "Physics",
      "Lab",
    ],
    "Language": [
      "Arabic",
      "English",
      "Chinese",
      "Japanese",
      "German",
      "Spanish",
      "French",
      "Turkish",
    ],
    "Playground": [
      "Trampoline",
      "Video Game",
      "Indoor",
    ],
  };

  static List<String> categories = allCategories.keys.toList();

  static List<String> subcategoriesOfCategory(String category) =>
      allCategories[category] ?? [];

  static List<String> branches =
      List.generate(Random().nextInt(5) + 5, (index) => "Branch ${index + 1}");

  static List<String> specialNeeds = List.generate(
      Random().nextInt(5) + 5, (index) => "Special Need ${index + 1}");
}
