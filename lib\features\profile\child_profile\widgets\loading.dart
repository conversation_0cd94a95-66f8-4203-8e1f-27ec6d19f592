import 'dart:math';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/widgets/shimmer_loading.dart';

class ChildProfileLoading extends StatelessWidget {
  const ChildProfileLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView(
      children: [
        Center(
          child: ShimmerWidget.card(
            height: 100,
            width: 100,
            borderRadius: BorderRadius.circular(50),
          ),
        ),
        const Gap(12),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: Get.width * .25),
          child: ShimmerWidget.card(
            height: 30,
            width: Get.width * .5,
            borderRadius: BorderRadius.circular(5),
          ),
        ),
        const Gap(36),
        SizedBox(
          height: 30,
          child: ListView.separated(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            scrollDirection: Axis.horizontal,
            itemBuilder: (_, __) =>
                ShimmerWidget.card(width: Random().nextInt(50) + 70),
            separatorBuilder: (_, __) => const Gap(12),
            itemCount: 3,
          ),
        ),
        const Gap(36),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: ShimmerWidget.card(
            height: 300,
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        const Gap(36),
      ],
    );
  }
}
