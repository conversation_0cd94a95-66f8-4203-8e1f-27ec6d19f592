import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide MultipartFile, FormData;
import 'package:gopal/core/config/app_builder.dart';
import 'package:gopal/core/models/constants/gender.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';
import 'package:gopal/core/utils/image_utils.dart';
import 'package:gopal/features/profile/my_profile/models/profile.dart';
import 'package:gopal/features/usecases/gender/choose_gender.dart';
import 'package:gopal/features/usecases/toast/toast.dart';

import '../../../core/services/rest_api/rest_api.dart';
import 'models/nav.dart';

class EditProfilePageController extends GetxController {
  AppBuilder appBuilder = Get.find();

  final EditProfilePageNav nav;
  EditProfilePageController(this.nav) {
    nameController.text = nav.profile.name;
    emailController.text = nav.profile.email ?? "";
    image = nav.profile.image?.medium ?? "";
    selectBirthDate(nav.profile.birthDate);
    gender.value = nav.profile.gender;
  }

  final form = GlobalKey<FormState>();

  //SECTION - Data
  TextEditingController nameController = TextEditingController();
  TextEditingController emailController = TextEditingController();

  final Rx<String> _image = "".obs;
  String get image => _image.value;
  set image(String value) => _image.value = value;

  //SECTION - Date
  DateTime? birthDate;
  TextEditingController birthDateController = TextEditingController();
  selectBirthDate(DateTime? date) {
    birthDate = date;
    birthDateController.text =
        date == null ? "" : DateFormat.yMd("en").format(birthDate!);
  }

  pickDate(context) async {
    DateTime? pickedDate = await showDatePicker(
      context: context,
      firstDate: DateTime(1900),
      lastDate: DateTime.now().subtract((365 * 14).days),
      initialDate: birthDate,
    );
    if (pickedDate != null) {
      selectBirthDate(pickedDate);
    }
  }
  //!SECTION

  //SECTION - Gender
  ObsVar<Gender> gender = ObsVar(null);

  Future<Gender?> pickGender() async {
    Gender? pickedGender = await ChooseGender.show(
      initialValue: gender.value,
    );
    if (pickedGender != null) {
      gender.value = pickedGender;
    }
    return pickedGender;
  }
  //!SECTION

  //!SECTION

  //SECTION - Data entry
  Future<String?> pickImage() async {
    String? result = await ImageUtils.pickAndCompressImage();
    if (result != null) {
      image = result;
    }
    return result;
  }

  //!SECTION

  confirm() async {
    if (!form.currentState!.validate()) {
      return;
    }
    Map<String, dynamic> body = {
      "name": nameController.text,
    };
    if (emailController.text.trim().isNotEmpty) {
      body['email'] = emailController.text;
    }
    if (birthDate != null) {
      body['birthdate'] = DateFormat("yyyy-MM-dd", "en").format(birthDate!);
    }
    if (gender.value != null) {
      body['gender'] = gender.value!.value;
    }
    if (image.isNotEmpty && (!image.startsWith("http"))) {
      body['profile'] = await MultipartFile.fromFile(image);
    }
    FormData data = FormData.fromMap(body);
    ResponseModel response = await Request(
      endPoint: EndPoints.update_profile,
      method: RequestMethod.Post,
      body: data,
    ).perform();
    if (response.success) {
      MyProfile profile = MyProfile.fromJson(response.data);
      Get.back(result: profile);
      appBuilder.setUser(profile);
    } else {
      Toast.show(message: response.message);
    }
  }
}
