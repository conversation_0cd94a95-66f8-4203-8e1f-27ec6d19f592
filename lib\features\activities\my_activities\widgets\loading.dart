import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gopal/core/widgets/shimmer_loading.dart';

class BooksLoading extends StatelessWidget {
  const BooksLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      itemCount: 10,
      separatorBuilder: (_, __) => const Gap(12),
      itemBuilder: (_, __) => ShimmerWidget.card(
        height: 150,
        borderRadius: BorderRadius.circular(12),
      ),
    );
  }
}
