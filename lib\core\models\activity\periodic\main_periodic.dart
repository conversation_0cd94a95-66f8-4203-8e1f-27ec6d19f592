import 'package:flutter/material.dart';
import 'package:gopal/core/utils/date.dart';

import 'periodic_type.dart';

class MainPeriodicModel {
  late int id;
  late PeriodicType type;
  DateTime? from;
  DateTime? to;
  int? period;

  MainPeriodicModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    type = PeriodicType.fromString(json['book_type']);
    from = DateTime.tryParse(json['from'] ?? "");
    to = DateTime.tryParse(json['to'] ?? "");
    period = json['period_days_number'];
  }

  static MainPeriodicModel parseTypes(Map<String, dynamic> json) {
    switch (PeriodicDaysType.fromString(json['periodic_day_type'])) {
      case PeriodicDaysType.daily:
        return DailyPeriodic.fromJson(json);
      case PeriodicDaysType.numbered_days:
        return NumberedDaysPeriodic.fromJson(json);
      case PeriodicDaysType.dedicated_days:
        return DedicatedDaysPeriodic.fromJson(json);
    }
  }
}

class DailyPeriodic extends MainPeriodicModel {
  late TimeOfDay openAt;
  late TimeOfDay closeAt;

  DailyPeriodic.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    openAt = TimeOfDayExt.parse(json['periodic_every_day']['open_at']);
    closeAt = TimeOfDayExt.parse(json['periodic_every_day']['close_at']);
  }
}

class NumberedDaysPeriodic extends MainPeriodicModel {
  late int daysNumber;
  late TimeOfDay openAt;
  late TimeOfDay closeAt;

  NumberedDaysPeriodic.fromJson(Map<String, dynamic> json)
      : super.fromJson(json) {
    json = json['periodic_numbered_day'];
    openAt = TimeOfDayExt.parse(json['open_at']);
    closeAt = TimeOfDayExt.parse(json['close_at']);
    daysNumber = json['number_of_days_per_week'];
  }
}

class DedicatedDaysPeriodic extends MainPeriodicModel {
  late List<DedicatedDayPeriodic> days;

  bool get areHaveTheSameTime {
    TimeOfDay open = days.first.openAt;
    for (var i = 1; i < days.length; i++) {
      if (open != days[i].openAt) {
        return false;
      }
    }
    TimeOfDay close = days.first.closeAt;
    for (var i = 1; i < days.length; i++) {
      if (close != days[i].closeAt) {
        return false;
      }
    }

    return true;
  }

  DedicatedDaysPeriodic.fromJson(Map<String, dynamic> json)
      : super.fromJson(json) {
    days = json['periodic_dedicated_days']
        .map<DedicatedDayPeriodic>((e) => DedicatedDayPeriodic.fromJson(e))
        .toList();
  }
}

class DedicatedDayPeriodic {
  late int weekDay;
  late TimeOfDay openAt;
  late TimeOfDay closeAt;

  DedicatedDayPeriodic.fromJson(Map<String, dynamic> json) {
    weekDay = json['week_day_number'];
    openAt = TimeOfDayExt.parse(json['open_at']);
    closeAt = TimeOfDayExt.parse(json['close_at']);
  }
}
