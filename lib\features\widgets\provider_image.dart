import 'package:flutter/material.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';

import '../../core/style/repo.dart';
import '../../core/widgets/image.dart';

class CenterImage extends StatelessWidget {
  final String image;
  final bool isTrusted;
  final double radius;
  const CenterImage({
    super.key,
    required this.image,
    required this.isTrusted,
    this.radius = 50,
  });

  @override
  Widget build(BuildContext context) {
    Widget imageWidget = AppImage(
      path: image,
      type: ImageType.CachedNetwork,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(radius / 2),
        boxShadow: StyleRepo.elevation,
      ),
      height: radius,
      width: radius,
    );
    if (isTrusted) {
      return imageWidget;
    } else {
      return SizedBox(
        height: radius,
        width: radius,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            imageWidget,
            Positioned(
              bottom: -2,
              right: -6,
              child: Assets.icons.verificationMark.svg(),
            ),
          ],
        ),
      );
    }
  }
}
