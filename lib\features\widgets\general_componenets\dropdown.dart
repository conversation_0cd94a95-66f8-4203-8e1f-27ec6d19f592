import 'package:flutter/material.dart';

class AppDropdown<T> extends StatelessWidget {
  final List<DropdownMenuItem<T>> items;
  final void Function(T?)? onChanged;
  final T? value;
  final String? Function(T?)? validator;
  final String? hint;
  const AppDropdown({
    super.key,
    required this.items,
    this.onChanged,
    this.value,
    this.validator,
    this.hint,
  });

  @override
  Widget build(BuildContext context) {
    return DropdownButtonFormField<T>(
      items: items,
      onChanged: onChanged,
      hint: hint != null ? Text(hint!) : null,
      value: value,
      icon: const Icon(Icons.keyboard_arrow_down),
      validator: validator,
    );
  }
}
