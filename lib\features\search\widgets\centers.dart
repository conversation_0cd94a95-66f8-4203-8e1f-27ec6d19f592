import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/user/center.dart';
import 'package:gopal/core/services/pagination/options/list_view.dart';
import 'package:gopal/features/center/widgets/center_card.dart';

import '../controller.dart';

class CentersSearch extends GetView<SearchPageController> {
  const CentersSearch({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: TextField(
            controller: controller.centersSearch,
            onChanged: controller.debouncerCentersSearch.add,
            decoration: InputDecoration(
              hintText: tr(LocaleKeys.search_for_centers),
              prefixIcon: const Icon(Icons.search),
            ),
          ),
        ),
        const Divider(height: 0),
        Expanded(
          child: Obx(
            () => AnimatedSwitcher(
              duration: 300.milliseconds,
              transitionBuilder: (child, animation) =>
                  ScaleTransition(scale: animation, child: child),
              child: controller.isSearchingForCenters
                  ? ListViewPagination.separated(
                      tag: ControllersTags.centers_search_pager,
                      fetchApi: controller.fetchCenters,
                      fromJson: CenterModel.fromJson,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 12),
                      separatorBuilder: (_, __) => const Gap(12),
                      itemBuilder: (context, index, center) =>
                          CenterCard(center: center),
                    )
                  : Center(
                      child: Text(tr(LocaleKeys.search_for_centers)),
                    ),
            ),
          ),
        ),
      ],
    );
  }
}
