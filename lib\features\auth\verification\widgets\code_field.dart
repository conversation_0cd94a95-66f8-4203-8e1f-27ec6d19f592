import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/style/repo.dart';
import 'package:pinput/pinput.dart';

import '../controller.dart';

class CodeField extends GetView<VerificationPageController> {
  const CodeField({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: Pinput(
        autofocus: true,
        controller: controller.codeController,
        onChanged: (str) => controller.canVerify = str.length == 4,
        submittedPinTheme: PinTheme(
          textStyle: context.textTheme.bodyLarge,
          constraints: const BoxConstraints(minHeight: 55, minWidth: 55),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: StyleRepo.turquoise.withValues(alpha: .2),
            border: Border.all(color: StyleRepo.grey),
          ),
        ),
        defaultPinTheme: PinTheme(
          constraints: const BoxConstraints(minHeight: 55, minWidth: 55),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: StyleRepo.grey),
          ),
        ),
      ),
    );
  }
}
