{"file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/test/test_360.dart": {"language": "Dart", "code": 18, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/main.dart": {"language": "Dart", "code": 79, "comment": 0, "blank": 11}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/widgets/grid_cards_loading.dart": {"language": "Dart", "code": 19, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/widgets/coming_soon.dart": {"language": "Dart", "code": 17, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/widgets/general_componenets/svg_icon.dart": {"language": "Dart", "code": 20, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/widgets/horizontal_list.dart": {"language": "Dart", "code": 73, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/widgets/general_componenets/dropdown.dart": {"language": "Dart", "code": 27, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/widgets/provider_image.dart": {"language": "Dart", "code": 47, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/widgets/location_row.dart": {"language": "Dart", "code": 35, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/widgets/general_componenets/pages/rounded_page.dart": {"language": "Dart", "code": 104, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/widgets/general_componenets/buttons/outlined_button.dart": {"language": "Dart", "code": 84, "comment": 1, "blank": 7}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/widgets/general_componenets/pages/general_page.dart": {"language": "Dart", "code": 41, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/toast/status.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/widgets/general_componenets/buttons/elevated_button.dart": {"language": "Dart", "code": 96, "comment": 1, "blank": 7}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/widgets/backgrounds/auth_bg.dart": {"language": "Dart", "code": 41, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/toast/widgets/toast.dart": {"language": "Dart", "code": 55, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/players/videos_duration_end.dart": {"language": "Dart", "code": 17, "comment": 0, "blank": 2}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/widgets/general_componenets/app_bars/general_app_bar.dart": {"language": "Dart", "code": 71, "comment": 8, "blank": 12}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/players/video/index.dart": {"language": "Dart", "code": 40, "comment": 1, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/widgets/general_componenets/form_field_error.dart": {"language": "Dart", "code": 21, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/splash_screen/controller.dart": {"language": "Dart", "code": 14, "comment": 2, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/splash_screen/index.dart": {"language": "Dart", "code": 22, "comment": 1, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/players/video/controller.dart": {"language": "Dart", "code": 49, "comment": 0, "blank": 9}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/gender/widgets/gender_tile.dart": {"language": "Dart", "code": 153, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/pictures/index.dart": {"language": "Dart", "code": 93, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/rating/index.dart": {"language": "Dart", "code": 106, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/toast/toast.dart": {"language": "Dart", "code": 38, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/gender/choose_gender.dart": {"language": "Dart", "code": 133, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/book_custom_session/models/result.dart": {"language": "Dart", "code": 7, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/confirmation/index.dart": {"language": "Dart", "code": 102, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/children_selection/widgets/children_selection_dialog.dart": {"language": "Dart", "code": 180, "comment": 0, "blank": 7}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/gender/widgets/gender_avatar.dart": {"language": "Dart", "code": 76, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/rating/controller.dart": {"language": "Dart", "code": 31, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/children_selection/controller.dart": {"language": "Dart", "code": 81, "comment": 1, "blank": 16}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/book_custom_session/index.dart": {"language": "Dart", "code": 123, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/gender/widgets/gender_card.dart": {"language": "Dart", "code": 96, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/search/index.dart": {"language": "Dart", "code": 46, "comment": 1, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/search/widgets/actitvities.dart": {"language": "Dart", "code": 59, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/search/controller.dart": {"language": "Dart", "code": 63, "comment": 0, "blank": 11}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/phone_field/phone_field.dart": {"language": "Dart", "code": 80, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/my_profile/widgets/children.dart": {"language": "Dart", "code": 103, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/search/widgets/centers.dart": {"language": "Dart", "code": 60, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/my_profile/widgets/profile_field.dart": {"language": "Dart", "code": 48, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/my_profile/widgets/loading.dart": {"language": "Dart", "code": 48, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/usecases/support/widgets/support_fab.dart": {"language": "Dart", "code": 28, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/usecases/points/models/point_setting.dart": {"language": "Dart", "code": 18, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/terms/index.dart": {"language": "Dart", "code": 22, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/usecases/points/index.dart": {"language": "Dart", "code": 126, "comment": 1, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/usecases/points/controller.dart": {"language": "Dart", "code": 30, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/terms/controller.dart": {"language": "Dart", "code": 19, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/support_replies/controller.dart": {"language": "Dart", "code": 12, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/usecases/support/controller.dart": {"language": "Dart", "code": 45, "comment": 0, "blank": 9}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/usecases/support/index.dart": {"language": "Dart", "code": 104, "comment": 1, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/usecases/book_custom_session/controller.dart": {"language": "Dart", "code": 101, "comment": 8, "blank": 9}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/support_replies/models/suppoert_reply.dart": {"language": "Dart", "code": 27, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/privacy/controller.dart": {"language": "Dart", "code": 19, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/about/controller.dart": {"language": "Dart", "code": 19, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/help/controller.dart": {"language": "Dart", "code": 19, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/support_replies/index.dart": {"language": "Dart", "code": 33, "comment": 1, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/help/index.dart": {"language": "Dart", "code": 22, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/support_replies/widgets/question_card.dart": {"language": "Dart", "code": 80, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/about/index.dart": {"language": "Dart", "code": 22, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/privacy/index.dart": {"language": "Dart", "code": 23, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/my_profile/controller.dart": {"language": "Dart", "code": 90, "comment": 0, "blank": 13}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/my_profile/models/profile.dart": {"language": "Dart", "code": 17, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/my_profile/index.dart": {"language": "Dart", "code": 144, "comment": 1, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/edit_profile/bindings.dart": {"language": "Dart", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/edit_profile/models/nav.dart": {"language": "Dart", "code": 5, "comment": 0, "blank": 2}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/child_profile/models/nav.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/edit_profile/widgets/form.dart": {"language": "Dart", "code": 175, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/invitation/controller.dart": {"language": "Dart", "code": 40, "comment": 0, "blank": 11}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/settings/invitation/index.dart": {"language": "Dart", "code": 67, "comment": 1, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/edit_profile/index.dart": {"language": "Dart", "code": 29, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/child_profile/bindings.dart": {"language": "Dart", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/child_profile/index.dart": {"language": "Dart", "code": 134, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/child_profile/controller.dart": {"language": "Dart", "code": 113, "comment": 3, "blank": 20}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/edit_profile/controller.dart": {"language": "Dart", "code": 76, "comment": 4, "blank": 14}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/child_profile/models/calendar_session.dart": {"language": "Dart", "code": 20, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/change_phone/controller.dart": {"language": "Dart", "code": 12, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/notifications/controller.dart": {"language": "Dart", "code": 29, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/child_profile/models/child.dart": {"language": "Dart", "code": 60, "comment": 0, "blank": 7}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/notifications/index.dart": {"language": "Dart", "code": 32, "comment": 1, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/notifications/widgets/card.dart": {"language": "Dart", "code": 72, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/home/<USER>/text_slider.dart": {"language": "Dart", "code": 66, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/main/controller.dart": {"language": "Dart", "code": 34, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/home/<USER>/centers_list.dart": {"language": "Dart", "code": 72, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/home/<USER>/slider.dart": {"language": "Dart", "code": 136, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/change_phone/index.dart": {"language": "Dart", "code": 66, "comment": 1, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/home/<USER>/seasonal_activities_list.dart": {"language": "Dart", "code": 68, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/home/<USER>/categories.dart": {"language": "Dart", "code": 95, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/home/<USER>": {"language": "Dart", "code": 159, "comment": 12, "blank": 16}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/filters/widgets/subcategories.dart": {"language": "Dart", "code": 72, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/home/<USER>/about.dart": {"language": "Dart", "code": 76, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/filters/widgets/special_need.dart": {"language": "Dart", "code": 59, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/home/<USER>/ad.dart": {"language": "Dart", "code": 86, "comment": 0, "blank": 11}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/filters/widgets/gender.dart": {"language": "Dart", "code": 66, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/main/index.dart": {"language": "Dart", "code": 58, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/main/models/destinations.dart": {"language": "Dart", "code": 34, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/filters/widgets/price_range.dart": {"language": "Dart", "code": 59, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/filters/widgets/category.dart": {"language": "Dart", "code": 49, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/filters/models/price_type.dart": {"language": "Dart", "code": 23, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/main/widgets/bottom_nav_bar.dart": {"language": "Dart", "code": 24, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/filters/models/categories.dart/main_category.dart": {"language": "Dart", "code": 25, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/filters/widgets/age.dart": {"language": "Dart", "code": 102, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/filters/widgets/rating.dart": {"language": "Dart", "code": 84, "comment": 33, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/filters/widgets/price_type.dart": {"language": "Dart", "code": 60, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/create_child/bindings.dart": {"language": "Dart", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/filters/models/filters_data.dart": {"language": "Dart", "code": 47, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/filters/index.dart": {"language": "Dart", "code": 77, "comment": 1, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/create_child/controller.dart": {"language": "Dart", "code": 168, "comment": 8, "blank": 20}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/rooms/widgets/loading.dart": {"language": "Dart", "code": 43, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/main/widgets/drawer.dart": {"language": "Dart", "code": 173, "comment": 5, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/create_child/models/nav.dart": {"language": "Dart", "code": 6, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/filters/controller.dart": {"language": "Dart", "code": 228, "comment": 2, "blank": 24}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/create_child/widgets/medical_info.dart": {"language": "Dart", "code": 96, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/create_child/index.dart": {"language": "Dart", "code": 36, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/main/widgets/drawer_card.dart": {"language": "Dart", "code": 72, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/rooms/controller.dart": {"language": "Dart", "code": 35, "comment": 3, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/rooms/widgets/chat_card.dart": {"language": "Dart", "code": 69, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/room/widgets/sending_bar.dart": {"language": "Dart", "code": 238, "comment": 1, "blank": 13}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/room/widgets/message_types_popup.dart": {"language": "Dart", "code": 190, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/create_child/widgets/form.dart": {"language": "Dart", "code": 279, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/main/widgets/notification_switch.dart": {"language": "Dart", "code": 43, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/room/widgets/messages/media_message_content.dart": {"language": "Dart", "code": 380, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/room/widgets/messages/voice_message_content.dart": {"language": "Dart", "code": 144, "comment": 3, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/create_child/models/child_creation.dart": {"language": "Dart", "code": 90, "comment": 0, "blank": 7}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/preview/location/controller.dart": {"language": "Dart", "code": 46, "comment": 0, "blank": 9}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/preview/video/bindings.dart": {"language": "Dart", "code": 9, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/room/widgets/messages/text_message_content.dart": {"language": "Dart", "code": 21, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/rooms/index.dart": {"language": "Dart", "code": 31, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/preview/images/index.dart": {"language": "Dart", "code": 132, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/preview/video/controller.dart": {"language": "Dart", "code": 36, "comment": 0, "blank": 11}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/preview/images/bindings.dart": {"language": "Dart", "code": 9, "comment": 0, "blank": 2}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/preview/images/controller.dart": {"language": "Dart", "code": 35, "comment": 0, "blank": 11}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/room/bindings.dart": {"language": "Dart", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/room/widgets/messages/location_message.dart": {"language": "Dart", "code": 80, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/preview/document/controller.dart": {"language": "Dart", "code": 15, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/preview/video/index.dart": {"language": "Dart", "code": 111, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/room/index.dart": {"language": "Dart", "code": 72, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/preview/document/bindings.dart": {"language": "Dart", "code": 9, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/preview/location/index.dart": {"language": "Dart", "code": 74, "comment": 1, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/categories/category/widgets/sub_categories.dart": {"language": "Dart", "code": 66, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/categories/category/models/nav.dart": {"language": "Dart", "code": 5, "comment": 0, "blank": 2}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/preview/document/index.dart": {"language": "Dart", "code": 110, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/profile/widgets/pictures.dart": {"language": "Dart", "code": 88, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/room/controller.dart": {"language": "Dart", "code": 439, "comment": 24, "blank": 85}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/widgets/review_card.dart": {"language": "Dart", "code": 52, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/profile/widgets/loading.dart": {"language": "Dart", "code": 71, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/chat/room/widgets/message.dart": {"language": "Dart", "code": 115, "comment": 0, "blank": 7}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/profile/widgets/header.dart": {"language": "Dart", "code": 85, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/profile/widgets/categories.dart": {"language": "Dart", "code": 27, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/reviews/index.dart": {"language": "Dart", "code": 27, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/reviews/controller.dart": {"language": "Dart", "code": 18, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/profile/bindings.dart": {"language": "Dart", "code": 11, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/profile/widgets/branches.dart": {"language": "Dart", "code": 52, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/reviews/bindings.dart": {"language": "Dart", "code": 9, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/profile/models/nav.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 2}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/models/center_review.dart": {"language": "Dart", "code": 8, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/profile/index.dart": {"language": "Dart", "code": 93, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/widgets/center_card.dart": {"language": "Dart", "code": 119, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/centers/models/nav.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/profile/controller.dart": {"language": "Dart", "code": 71, "comment": 0, "blank": 9}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/profile/widgets/reviews.dart": {"language": "Dart", "code": 61, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/centers/bindings.dart": {"language": "Dart", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/centers_on_map/index.dart": {"language": "Dart", "code": 50, "comment": 1, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/profile/widgets/about.dart": {"language": "Dart", "code": 49, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/categories/category/bindings.dart": {"language": "Dart", "code": 13, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/centers/index.dart": {"language": "Dart", "code": 39, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/profile/models/center_details.dart": {"language": "Dart", "code": 64, "comment": 0, "blank": 9}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/categories/categories/widgets/loading.dart": {"language": "Dart", "code": 18, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/centers_on_map/controller.dart": {"language": "Dart", "code": 30, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/categories/category/index.dart": {"language": "Dart", "code": 67, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/centers/controller.dart": {"language": "Dart", "code": 33, "comment": 0, "blank": 8}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/categories/categories/widgets/category_card.dart": {"language": "Dart", "code": 50, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/books/book_details/widgets/header.dart": {"language": "Dart", "code": 72, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/categories/categories/widgets/all_card.dart": {"language": "Dart", "code": 33, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/books/book_details/widgets/specific_days_sessions.dart": {"language": "Dart", "code": 74, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/books/book_details/widgets/pictures.dart": {"language": "Dart", "code": 87, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/books/book_details/bindings.dart": {"language": "Dart", "code": 7, "comment": 0, "blank": 2}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/center/centers_on_map/models/center_location.dart": {"language": "Dart", "code": 25, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/categories/categories/controller.dart": {"language": "Dart", "code": 19, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/books/book_details/widgets/calendar.dart": {"language": "Dart", "code": 138, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/books/book_details/controller.dart": {"language": "Dart", "code": 106, "comment": 5, "blank": 24}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/books/book_details/index.dart": {"language": "Dart", "code": 84, "comment": 1, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/categories/category/controller.dart": {"language": "Dart", "code": 74, "comment": 0, "blank": 12}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/categories/categories/index.dart": {"language": "Dart", "code": 39, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/books/book_details/widgets/date_time_info.dart": {"language": "Dart", "code": 164, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/boarding/controller.dart": {"language": "Dart", "code": 35, "comment": 0, "blank": 8}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/auth/verification/models/nav.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 2}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/auth/verification/widgets/code_timer.dart": {"language": "Dart", "code": 54, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/auth/widgets/terms_privacy_row.dart": {"language": "Dart", "code": 36, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/auth/verification/bindings.dart": {"language": "Dart", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/auth/verification/widgets/code_field.dart": {"language": "Dart", "code": 35, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/auth/login/widgets/languages.dart": {"language": "Dart", "code": 49, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/boarding/index.dart": {"language": "Dart", "code": 117, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/boarding/models/boarding_data.dart": {"language": "Dart", "code": 24, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/auth/verification/controller.dart": {"language": "Dart", "code": 75, "comment": 0, "blank": 9}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/auth/create_account/widgets/terms_acception.dart": {"language": "Dart", "code": 67, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/auth/create_account/widgets/children.dart": {"language": "Dart", "code": 86, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/auth/login/index.dart": {"language": "Dart", "code": 91, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/auth/create_account/models/age_range.dart": {"language": "Dart", "code": 1, "comment": 1, "blank": 1}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/auth/verification/index.dart": {"language": "Dart", "code": 91, "comment": 2, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/auth/widgets/auth_card.dart": {"language": "Dart", "code": 24, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/auth/login/controller.dart": {"language": "Dart", "code": 45, "comment": 1, "blank": 8}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/auth/create_account/models/nav.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/auth/create_account/bindings.dart": {"language": "Dart", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/auth/create_account/controller.dart": {"language": "Dart", "code": 87, "comment": 0, "blank": 15}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/widgets/periodic_time_info.dart": {"language": "Dart", "code": 182, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/usecases/specific_days_calendar/widgets/specific_days_sessions.dart": {"language": "Dart", "code": 87, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/widgets/activity_card.dart": {"language": "Dart", "code": 137, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/auth/create_account/widgets/form.dart": {"language": "Dart", "code": 93, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/auth/create_account/index.dart": {"language": "Dart", "code": 71, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/widgets/calendar_activitiy_card.dart": {"language": "Dart", "code": 102, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/usecases/specific_days_calendar/index.dart": {"language": "Dart", "code": 31, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/widgets/favourite_activity_card.dart": {"language": "Dart", "code": 117, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/usecases/specific_days_calendar/widgets/calendar.dart": {"language": "Dart", "code": 141, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/usecases/specific_days_calendar/controller.dart": {"language": "Dart", "code": 76, "comment": 1, "blank": 14}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/widgets/seasonal_activity_card.dart": {"language": "Dart", "code": 54, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/widgets/activities_list.dart": {"language": "Dart", "code": 72, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/widgets/sets.dart": {"language": "Dart", "code": 106, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/usecases/open_sessions_calendar/widgets/open_sessions.dart": {"language": "Dart", "code": 113, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/usecases/points_selection/points_selection.dart": {"language": "Dart", "code": 109, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/usecases/open_sessions_calendar/controller.dart": {"language": "Dart", "code": 79, "comment": 5, "blank": 17}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/my_activities/widgets/pending.dart": {"language": "Dart", "code": 57, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/my_activities/widgets/loading.dart": {"language": "Dart", "code": 18, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/my_activities/widgets/progress.dart": {"language": "Dart", "code": 56, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/usecases/open_sessions_calendar/widgets/calendar.dart": {"language": "Dart", "code": 141, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/usecases/open_sessions_calendar/index.dart": {"language": "Dart", "code": 22, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/my_activities/index.dart": {"language": "Dart", "code": 75, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/books/widgets/book_card.dart": {"language": "Dart", "code": 380, "comment": 5, "blank": 12}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/my_activities/models/favourite_activity.dart": {"language": "Dart", "code": 8, "comment": 0, "blank": 2}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/my_activities/models/tabs.dart": {"language": "Dart", "code": 21, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/my_activities/widgets/favourites.dart": {"language": "Dart", "code": 34, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/main_activity/models/nav.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 2}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/my_activities/controller.dart": {"language": "Dart", "code": 162, "comment": 5, "blank": 15}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/my_activities/widgets/completed.dart": {"language": "Dart", "code": 26, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/main_activity/models/sub_activity_data.dart": {"language": "Dart", "code": 98, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/usecases/booking/booking.dart": {"language": "Dart", "code": 168, "comment": 8, "blank": 25}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/main_activity/widgets/card.dart": {"language": "Dart", "code": 271, "comment": 3, "blank": 10}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/main_activity/index.dart": {"language": "Dart", "code": 34, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/main_activity/widgets/card_body.dart": {"language": "Dart", "code": 42, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/activitiy_details/widgets/info_column.dart": {"language": "Dart", "code": 318, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/activitiy_details/widgets/sets.dart": {"language": "Dart", "code": 87, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/activitiy_details/widgets/organizers.dart": {"language": "Dart", "code": 56, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/activitiy_details/widgets/about.dart": {"language": "Dart", "code": 47, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/activitiy_details/widgets/header.dart": {"language": "Dart", "code": 93, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/activitiy_details/index.dart": {"language": "Dart", "code": 128, "comment": 4, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/main_activity/controller.dart": {"language": "Dart", "code": 26, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/activitiy_details/bindings.dart": {"language": "Dart", "code": 10, "comment": 0, "blank": 2}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/activities/bindings.dart": {"language": "Dart", "code": 10, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/activitiy_details/widgets/pictures.dart": {"language": "Dart", "code": 87, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/activitiy_details/widgets/categories.dart": {"language": "Dart", "code": 27, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/activitiy_details/models/activity.dart": {"language": "Dart", "code": 66, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/my_activities/models/completed_book.dart": {"language": "Dart", "code": 23, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/utils/list_utils.dart": {"language": "Dart", "code": 25, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/main_activity/bindings.dart": {"language": "Dart", "code": 10, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/utils/responsivity.dart": {"language": "Dart", "code": 31, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/widgets/shimmer_loading.dart": {"language": "Dart", "code": 30, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/utils/firebase_options.dart": {"language": "Dart", "code": 0, "comment": 57, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/activitiy_details/controller.dart": {"language": "Dart", "code": 127, "comment": 8, "blank": 18}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/utils/file_utils.dart": {"language": "Dart", "code": 14, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/utils/num_utils.dart": {"language": "Dart", "code": 30, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/widgets/responsive_view.dart": {"language": "Dart", "code": 26, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/widgets/error_widget.dart": {"language": "Dart", "code": 76, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/utils/action_stack.dart": {"language": "Dart", "code": 22, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/style/utils/theme_value.dart": {"language": "Dart", "code": 6, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/utils/social_urls.dart": {"language": "Dart", "code": 16, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/style/utils/hex_colors.dart": {"language": "Dart", "code": 11, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/utils/date.dart": {"language": "Dart", "code": 62, "comment": 30, "blank": 12}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/style/assets/assets.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 0}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/widgets/loading.dart": {"language": "Dart", "code": 51, "comment": 0, "blank": 8}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/style/themes.dart": {"language": "Dart", "code": 17, "comment": 8, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/utils/timer.dart": {"language": "Dart", "code": 27, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/widgets/guest_bottom_sheet.dart": {"language": "Dart", "code": 48, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/widgets/image.dart": {"language": "Dart", "code": 107, "comment": 1, "blank": 11}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/style/style.dart": {"language": "Dart", "code": 87, "comment": 11, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/utils/validator.dart": {"language": "Dart", "code": 52, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/utils/string_utiles.dart": {"language": "Dart", "code": 39, "comment": 1, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/utils/image_utils.dart": {"language": "Dart", "code": 43, "comment": 36, "blank": 14}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/state_management/variable_status.dart": {"language": "Dart", "code": 5, "comment": 9, "blank": 10}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/share/share.dart": {"language": "Dart", "code": 44, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/firebase_messaging/notifications_actions.dart": {"language": "Dart", "code": 43, "comment": 1, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/rest_api/rest_api.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/rest_api/logger/logger.dart": {"language": "Dart", "code": 38, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/style/repo.dart": {"language": "Dart", "code": 48, "comment": 0, "blank": 10}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/rest_api/models/exceptions.dart": {"language": "Dart", "code": 6, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/firebase_messaging/firebase_messaging.dart": {"language": "Dart", "code": 123, "comment": 3, "blank": 19}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/firebase_messaging/widgets/fcm_token.dart": {"language": "Dart", "code": 36, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/rest_api/constants/messages.dart": {"language": "Dart", "code": 11, "comment": 0, "blank": 2}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/state_management/observable_variable.dart": {"language": "Dart", "code": 178, "comment": 0, "blank": 38}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/rest_api/widgets/request_refresher.dart": {"language": "Dart", "code": 99, "comment": 1, "blank": 11}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/rest_api/constants/api_error.dart": {"language": "Dart", "code": 12, "comment": 1, "blank": 13}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/rest_api/utilitis/queue.dart": {"language": "Dart", "code": 65, "comment": 6, "blank": 8}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/firebase_messaging/constants/topics.dart": {"language": "Dart", "code": 0, "comment": 0, "blank": 2}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/rest_api/models/request.dart": {"language": "Dart", "code": 124, "comment": 18, "blank": 30}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/rest_api/models/response_model.dart": {"language": "Dart", "code": 24, "comment": 1, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/rest_api/api_service.dart": {"language": "Dart", "code": 290, "comment": 12, "blank": 19}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/rest_api/constants/end_points.dart": {"language": "Dart", "code": 50, "comment": 24, "blank": 14}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/register_fcm/status.dart": {"language": "Dart", "code": 17, "comment": 0, "blank": 2}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/player/errors.dart": {"language": "Dart", "code": 1, "comment": 0, "blank": 1}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/player/status.dart": {"language": "Dart", "code": 11, "comment": 0, "blank": 2}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/rest_api/handlers/success_handler.dart": {"language": "Dart", "code": 41, "comment": 1, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/rest_api/utilitis/parser.dart": {"language": "Dart", "code": 46, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/player/player.dart": {"language": "Dart", "code": 106, "comment": 4, "blank": 19}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/notifications_counter/counter.dart": {"language": "Dart", "code": 48, "comment": 1, "blank": 8}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/pagination/widgets/loading.dart": {"language": "Dart", "code": 15, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/pagination/widgets/initial_loading.dart": {"language": "Dart", "code": 17, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/pagination/widgets/empty.dart": {"language": "Dart", "code": 16, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/pagination/widgets/page_error.dart": {"language": "Dart", "code": 24, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/firebase_messaging/constants/firebase_options.dart": {"language": "Dart", "code": 53, "comment": 12, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/pagination/widgets/initial_error.dart": {"language": "Dart", "code": 82, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/pagination/options/pager.dart": {"language": "Dart", "code": 93, "comment": 16, "blank": 19}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/register_fcm/service.dart": {"language": "Dart", "code": 49, "comment": 1, "blank": 10}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/pagination/options/grid_view.dart": {"language": "Dart", "code": 195, "comment": 1, "blank": 8}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/pagination/options/list_view.dart": {"language": "Dart", "code": 176, "comment": 0, "blank": 8}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/rest_api/handlers/error_handler.dart": {"language": "Dart", "code": 75, "comment": 1, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/style/assets/gen/fonts.gen.dart": {"language": "Dart", "code": 4, "comment": 8, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/feedback/controller.dart": {"language": "Dart", "code": 49, "comment": 0, "blank": 11}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/pagination/controller.dart": {"language": "Dart", "code": 133, "comment": 3, "blank": 17}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/notifications_click/notification_click.dart": {"language": "Dart", "code": 88, "comment": 3, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/device_info.dart": {"language": "Dart", "code": 19, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/activities/models/nav.dart": {"language": "Dart", "code": 6, "comment": 0, "blank": 1}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/category.dart": {"language": "Dart", "code": 24, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/local_notifications/local_notification.dart": {"language": "Dart", "code": 115, "comment": 6, "blank": 16}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/book/status.dart": {"language": "Dart", "code": 18, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/routes.dart": {"language": "Dart", "code": 269, "comment": 6, "blank": 12}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/selection.dart": {"language": "Dart", "code": 17, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/book/payment_status.dart": {"language": "Dart", "code": 25, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/rate.dart": {"language": "Dart", "code": 9, "comment": 0, "blank": 2}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/user/main_child.dart": {"language": "Dart", "code": 20, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/style/assets/gen/assets.gen.dart": {"language": "Dart", "code": 429, "comment": 107, "blank": 127}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/image.dart": {"language": "Dart", "code": 59, "comment": 0, "blank": 7}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/constants/recording_status.dart": {"language": "Dart", "code": 8, "comment": 0, "blank": 2}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/location.dart": {"language": "Dart", "code": 39, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/constants/support_number.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/constants/discount.dart": {"language": "Dart", "code": 15, "comment": 0, "blank": 2}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/user/center.dart": {"language": "Dart", "code": 49, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/chat/chat_room.dart": {"language": "Dart", "code": 25, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/user/main_user.dart": {"language": "Dart", "code": 46, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/age_range.dart": {"language": "Dart", "code": 20, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/notifications/notification.dart": {"language": "Dart", "code": 40, "comment": 1, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/activity/set.dart": {"language": "Dart", "code": 11, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/book/book.dart": {"language": "Dart", "code": 86, "comment": 1, "blank": 16}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/constants/gender.dart": {"language": "Dart", "code": 49, "comment": 0, "blank": 7}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/notifications/data/main_notification_data.dart": {"language": "Dart", "code": 50, "comment": 0, "blank": 9}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/activity/subactivity.dart": {"language": "Dart", "code": 47, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/activity/periodic/periodic_type.dart": {"language": "Dart", "code": 32, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/chat/messages/types.dart": {"language": "Dart", "code": 37, "comment": 0, "blank": 8}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/notifications/type.dart": {"language": "Dart", "code": 31, "comment": 8, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/chat/messages/messages.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/chat/messages/message.dart": {"language": "Dart", "code": 23, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/activity/main_activity.dart": {"language": "Dart", "code": 34, "comment": 0, "blank": 6}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/chat/messages/received_message.dart": {"language": "Dart", "code": 84, "comment": 1, "blank": 16}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/activity/constants/price_type.dart": {"language": "Dart", "code": 15, "comment": 0, "blank": 2}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/activity/periodic/main_periodic.dart": {"language": "Dart", "code": 81, "comment": 0, "blank": 15}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/activity/session.dart": {"language": "Dart", "code": 24, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/activity/constants/type.dart": {"language": "Dart", "code": 41, "comment": 0, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/models/chat/messages/send_message.dart": {"language": "Dart", "code": 122, "comment": 0, "blank": 29}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/demo/media.dart": {"language": "Dart", "code": 24, "comment": 9, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/localization/helper.dart": {"language": "Dart", "code": 9, "comment": 1, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/constants/storages_names.dart": {"language": "Dart", "code": 4, "comment": 0, "blank": 1}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/demo/data.dart": {"language": "Dart", "code": 101, "comment": 0, "blank": 9}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/localization/codegen_loader.g.dart": {"language": "Dart", "code": 556, "comment": 2, "blank": 7}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/constants/breakpoints.dart": {"language": "Dart", "code": 3, "comment": 0, "blank": 1}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/activities/controller.dart": {"language": "Dart", "code": 21, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/config/role_middleware.dart": {"language": "Dart", "code": 23, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/localization/localization.dart": {"language": "Dart", "code": 56, "comment": 1, "blank": 10}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/feedback/widgets/dialog.dart": {"language": "Dart", "code": 43, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/services/feedback/widgets/feedback_button.dart": {"language": "Dart", "code": 30, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/activities/activities/index.dart": {"language": "Dart", "code": 31, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/config/role.dart": {"language": "Dart", "code": 74, "comment": 4, "blank": 15}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/constants/controllers_tags.dart": {"language": "Dart", "code": 17, "comment": 0, "blank": 9}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/child_profile/widgets/loading.dart": {"language": "Dart", "code": 52, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/child_profile/widgets/calendar.dart": {"language": "Dart", "code": 137, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/child_profile/widgets/heading_card.dart": {"language": "Dart", "code": 77, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/child_profile/widgets/hobbies.dart": {"language": "Dart", "code": 41, "comment": 0, "blank": 4}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/features/profile/child_profile/widgets/activities.dart": {"language": "Dart", "code": 39, "comment": 0, "blank": 5}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/config/defaults.dart": {"language": "Dart", "code": 19, "comment": 0, "blank": 9}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/localization/strings.dart": {"language": "Dart", "code": 236, "comment": 1, "blank": 3}, "file:///d%3A/work/ixCoders/current/go_pal/go_pal_flutter/lib/core/config/app_builder.dart": {"language": "Dart", "code": 147, "comment": 20, "blank": 34}}