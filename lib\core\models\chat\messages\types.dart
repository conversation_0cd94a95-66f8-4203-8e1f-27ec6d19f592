enum MessageType {
  text,
  voice,
  media,
  location,
  contact,
  other;

  static MessageType fromString(String s) {
    s = s.toLowerCase();
    for (var type in MessageType.values) {
      if (type.name == s) {
        return type;
      }
    }
    return other;
  }

  String get value => name.toUpperCase();
}

enum MessageStatus { waiting, error }

enum MessageMediaType {
  images,
  video,
  doc,
  ;

  bool get isImages => this == images;
  bool get isVideo => this == video;
  bool get isDoc => this == doc;

  String get value => name;

  static MessageMediaType fromString(String s) {
    for (var message in MessageMediaType.values) {
      if (message.name == s) {
        return message;
      }
    }
    return doc;
  }
}
