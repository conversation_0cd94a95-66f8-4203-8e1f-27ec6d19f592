import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/activity/session.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/error_widget.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';

import '../controller.dart';
import 'calendar.dart';

class SpecificDaysSessions extends StatelessWidget {
  final int id;
  final int specificDayId;
  const SpecificDaysSessions(
      {super.key, required this.id, required this.specificDayId});

  @override
  Widget build(BuildContext context) {
    SpecificDayController controller = Get.find(tag: "$id");
    return Column(
      children: [
        SpecificDaysCalendar(id: id),
        Obx(
          () {
            if (controller.calendarSessions.loading) {
              return ListView.separated(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: 4,
                separatorBuilder: (_, __) => const Gap(8),
                itemBuilder: (context, index) => const FieldLoadingWidget(),
              );
            } else if (controller.calendarSessions.hasError) {
              return FieldErrorWidget(
                  error: controller.calendarSessions.error!);
            } else {
              List<ActivitySession> sessions = controller
                      .calendarSessions.value![controller.selectedDate.day] ??
                  [];
              return ListView.separated(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: sessions.length,
                separatorBuilder: (_, __) => const Gap(8),
                itemBuilder: (context, index) {
                  return Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 12, horizontal: 12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: StyleRepo.grey),
                    ),
                    child: Center(
                      child: Text(
                        LocaleKeys.from_to.tr(
                          args: [
                            sessions[index].from.format(context),
                            sessions[index].to.format(context),
                          ],
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  );
                },
              );
            }
          },
        ),
        const Gap(24),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: Get.width * .1),
          child: AppElevatedButton(
            onTap: () => controller.bookASpecificSession(
                activityId: id, specificDayId: specificDayId),
            child: Text(LocaleKeys.booking.tr()),
          ),
        ),
      ],
    );
  }
}
