import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';

class BoardingData {
  String image;
  String title;

  BoardingData({
    required this.image,
    required this.title,
  });

  static List<BoardingData> boarding = [
    BoardingData(
      image: Assets.images.boarding1.path,
      title: LocaleKeys.boarding_1,
    ),
    BoardingData(
      image: Assets.images.boarding2.path,
      title: LocaleKeys.boarding_2,
    ),
    BoardingData(
      image: Assets.images.boarding3.path,
      title: LocaleKeys.boarding_3,
    ),
  ];
}
