import 'package:easy_localization/easy_localization.dart';
import 'package:gopal/core/localization/strings.dart';

enum PriceType {
  hourly,
  course,
  person,
  ;

  static fromString(String s) {
    switch (s) {
      case "HOURLY":
        return hourly;
      case "COURSE":
        return course;
      case "PER_PERSON":
        return person;
      default:
        throw "not defined price type";
    }
  }

  String get explainedText => switch (this) {
        hourly => LocaleKeys.per_hour.tr(),
        person => LocaleKeys.per_person.tr(),
        _ => "",
      };
}
