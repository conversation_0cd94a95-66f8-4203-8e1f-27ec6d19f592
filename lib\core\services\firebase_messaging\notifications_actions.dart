import 'dart:async';

import 'package:get/get.dart';
import 'package:gopal/core/models/chat/messages/messages.dart';
import 'package:gopal/core/models/notifications/data/main_notification_data.dart';
import 'package:gopal/core/models/notifications/notification.dart';
import 'package:gopal/core/models/notifications/type.dart';
import 'package:gopal/features/chat/room/controller.dart';
import 'package:gopal/features/chat/rooms/controller.dart';

FutureOr<bool> recieveNotificationAction(NotificationModel notification) async {
  // NotificationsCounter.notificationsCount++;
  switch (notification.type) {
    case NotificationType.activity:
    case NotificationType.book:
    case NotificationType.general:
    case NotificationType.points:
    case NotificationType.suggestion:
    case NotificationType.update:
      return true;
    case NotificationType.new_message:
      return newMessageAction(notification);
    default:
      return true;
  }
}

FutureOr<bool> newMessageAction(NotificationModel notification) async {
  ReceivedMessage message =
      (notification.data as NewMessageNotificationData).message;
  if (Get.isRegistered<ChatRoomPageController>()) {
    final roomController = Get.find<ChatRoomPageController>();
    if (roomController.chatRoom.id ==
        notification.data!.asMessageData.room.id) {
      roomController.receiveMessage(message);
      return false;
    }
  }
  if (Get.isRegistered<ChatRoomsPageController>()) {
    final roomsController = Get.find<ChatRoomsPageController>();
    roomsController.reorderRooms(
      roomId: notification.data!.asMessageData.room.id,
      preview: notification.data!.asMessageData.messagePreview,
    );
  }
  return true;
}
