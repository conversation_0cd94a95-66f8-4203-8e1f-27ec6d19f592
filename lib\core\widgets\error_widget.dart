import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/style/repo.dart';

class AppErrorWidget extends StatelessWidget {
  final String error;
  final bool withScrolling;

  const AppErrorWidget({super.key, this.error = '', this.withScrolling = true});
  @override
  Widget build(BuildContext context) {
    if (!withScrolling) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              child: Text(
                error,
                maxLines: 3,
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      );
    }
    return Stack(
      children: [
        Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 12.0),
                child: Text(
                  error,
                  maxLines: 3,
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
        Theme(
          data: Get.theme.copyWith(
              colorScheme: Get.theme.colorScheme
                  .copyWith(secondary: Colors.transparent)),
          child: SingleChildScrollView(
              child: SizedBox(height: Get.height, width: Get.width)),
        ),
      ],
    );
  }
}

class FieldErrorWidget extends StatelessWidget {
  final String error;
  final void Function()? onRefresh;
  const FieldErrorWidget({super.key, required this.error, this.onRefresh});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        if (onRefresh != null)
          IconButton(onPressed: onRefresh, icon: const Icon(Icons.refresh)),
        if (onRefresh != null) const Gap(12),
        Expanded(
            child: Text(
          error,
          style: const TextStyle(
              fontWeight: FontWeight.bold, color: StyleRepo.red),
        ))
      ],
    );
  }
}
