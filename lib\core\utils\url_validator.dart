/// مساعد للتحقق من صحة الـ URLs
class UrlValidator {
  /// فحص صحة الـ URL
  static bool isValidUrl(String? url) {
    if (url == null || url.isEmpty) return false;
    
    try {
      final uri = Uri.parse(url);
      return uri.hasScheme && uri.host.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  /// فحص صحة الـ URL مع إرجاع رسالة خطأ مفصلة
  static ValidationResult validateUrl(String? url) {
    if (url == null) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'URL is null',
      );
    }
    
    if (url.isEmpty) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'URL is empty',
      );
    }
    
    try {
      final uri = Uri.parse(url);
      
      if (!uri.hasScheme) {
        return ValidationResult(
          isValid: false,
          errorMessage: 'URL has no scheme (http/https)',
        );
      }
      
      if (uri.host.isEmpty) {
        return ValidationResult(
          isValid: false,
          errorMessage: 'No host specified in URI',
        );
      }
      
      return ValidationResult(isValid: true);
    } catch (e) {
      return ValidationResult(
        isValid: false,
        errorMessage: 'Invalid URL format: $e',
      );
    }
  }

  /// إصلاح الـ URL إذا كان ممكناً
  static String? fixUrl(String? url) {
    if (url == null || url.isEmpty) return null;
    
    // إضافة https إذا لم يكن موجود
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://$url';
    }
    
    return isValidUrl(url) ? url : null;
  }
}

/// نتيجة فحص الـ URL
class ValidationResult {
  final bool isValid;
  final String? errorMessage;

  ValidationResult({
    required this.isValid,
    this.errorMessage,
  });
}
