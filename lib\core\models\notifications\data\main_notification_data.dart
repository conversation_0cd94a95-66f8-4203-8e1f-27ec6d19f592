import 'dart:convert';

import 'package:gopal/core/models/chat/chat_room.dart';
import 'package:gopal/core/models/chat/messages/messages.dart';
import 'package:gopal/core/models/image.dart';

abstract class MainNotificationData {
  ActivityNotificationData get asActivityData =>
      this as ActivityNotificationData;
  SuggestionsActivitiesNotificationData get asSuggestionsData =>
      this as SuggestionsActivitiesNotificationData;
  NewMessageNotificationData get asMessageData =>
      this as NewMessageNotificationData;
  PaymentSuccessNotificationData get asPaymentSuccessData =>
      this as PaymentSuccessNotificationData;
  BookAcceptedNotificationData get asBookAcceptedData =>
      this as BookAcceptedNotificationData;
}

class ActivityNotificationData extends MainNotificationData {
  late int activityId;
  ActivityNotificationData.fromJson(Map<String, dynamic> json) {
    activityId = json['sub_activity_id'];
  }
}

class SuggestionsActivitiesNotificationData extends MainNotificationData {
  late List<int> activitiesIds;

  SuggestionsActivitiesNotificationData.fromJson(Map<String, dynamic> json) {
    activitiesIds = json['sub_activities_ids'].cast<int>();
  }
}

class NewMessageNotificationData extends MainNotificationData {
  late ReceivedMessage message;
  late ChatRoom room;
  late String messagePreview;

  NewMessageNotificationData.fromJson(Map<String, dynamic> json) {
    message = ReceivedMessage.fromJson(json['message'] is String
        ? jsonDecode(json['message'])
        : json['message']);
    room = ChatRoom(
      id: json['room_id'],
      name: json['message']['sender']['name'],
      image:
          ImageModel.fromJson(json['message']['sender']['media']['profile'][0]),
      date: DateTime.parse(json['message']['created_at']),
    );
    messagePreview = json['message']['preview'];
  }
}

class PaymentSuccessNotificationData extends MainNotificationData {
  late String link;
  PaymentSuccessNotificationData.fromJson(Map<String, dynamic> json) {
    link = json['pdf_url'] ?? "https://gopalsa.com";
  }
}

class BookAcceptedNotificationData extends MainNotificationData {
  late int? bookId;
  BookAcceptedNotificationData.fromJson(Map<String, dynamic> json) {
    bookId = json['book_transaction_id'];
  }
}
