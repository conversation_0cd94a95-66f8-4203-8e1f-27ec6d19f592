import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/models/book/book.dart';
import 'package:gopal/core/services/pagination/options/list_view.dart';

import '../../../books/widgets/book_card.dart';
import '../controller.dart';
import 'loading.dart';

class MyProgressActivities extends GetView<MyActivitiesPageController> {
  const MyProgressActivities({super.key});

  @override
  Widget build(BuildContext context) {
    return ListViewPagination.separated(
      tag: ControllersTags.my_in_progress_activities,
      fetchApi: controller.fetchInProgress,
      fromJson: BookedActivity.fromJson,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      separatorBuilder: (_, __) => const Gap(12),
      initialLoading: const BooksLoading(),
      itemBuilder: (context, index, book) {
        return BookCard(
          bookedActivity: book,
          // trailing: PopupMenuButton(
          //   surfaceTintColor: StyleRepo.white,
          //   itemBuilder: (context) => <PopupMenuEntry>[
          //     PopupMenuItem(
          //       onTap: () => ConfirmationDialog.confirm(
          //         onAccept: () {},
          //         body: LocaleKeys.cancel_book_confirmation.tr(),
          //       ),
          //       child: Row(
          //         children: [
          //           const Icon(Icons.cancel_outlined, color: StyleRepo.red),
          //           const Gap(8),
          //           Text(
          //             LocaleKeys.cancel.tr(),
          //             style: const TextStyle(color: StyleRepo.red),
          //           ),
          //         ],
          //       ),
          //     ),
          //   ],
          //   child: SvgIcon(Assets.icons.menu.path),
          // ),
        );
      },
    );
  }
}
