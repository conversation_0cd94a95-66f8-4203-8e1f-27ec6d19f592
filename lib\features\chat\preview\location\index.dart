import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Trans;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';

import 'controller.dart';

class PickLocationPage extends StatelessWidget {
  const PickLocationPage({super.key});

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    PickLocationPageController controller =
        Get.put(PickLocationPageController());
    return Scaffold(
      body: Obx(
        () => controller.initLocation.hasData
            ? Column(
                children: [
                  Expanded(
                    child: Stack(
                      children: [
                        GoogleMap(
                          onMapCreated: (mapController) =>
                              controller.mapController = mapController,
                          initialCameraPosition: CameraPosition(
                            target: controller.initLocation.value!,
                            zoom: 13,
                          ),
                          myLocationButtonEnabled: true,
                          onCameraMoveStarted: () {
                            controller.isCameraMoving = true;
                          },
                          onCameraMove: (CameraPosition cameraPosition) {
                            if (!controller.isCameraMoving) {
                              controller.isCameraMoving = true;
                            }
                            controller.cameraMovingActionStack
                                .add(cameraPosition.target);
                          },
                        ),
                        Center(
                          child: Obx(
                            () => AnimatedPadding(
                              duration: 300.milliseconds,
                              padding: EdgeInsets.only(
                                  bottom: controller.isCameraMoving ? 50 : 25),
                              child: Icon(
                                Icons.location_on,
                                size: 50,
                                color: StyleRepo.red.shade800,
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                  AppElevatedButton(
                    onTap: () async => await controller.confirm(),
                    child: Text(
                      LocaleKeys.confirm.tr(),
                    ),
                  ),
                ],
              )
            : const Center(
                child: LoadingWidget(),
              ),
      ),
    );
  }
}
