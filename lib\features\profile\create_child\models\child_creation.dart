import 'package:dio/dio.dart';
import 'package:gopal/core/models/constants/gender.dart';
import 'package:gopal/core/models/selection.dart';
import 'package:gopal/core/models/user/main_child.dart';

class ChildCreation extends MainChild {
  String? image;
  DateTime birthDate;
  List<Selection> hobbies;
  Gender gender;
  String? allergy;
  List<Selection> specialCases = [];

  ChildCreation({
    super.id,
    required super.firstName,
    required super.lastName,
    this.image,
    required this.birthDate,
    required this.hobbies,
    required this.gender,
    this.allergy,
    this.specialCases = const [],
  }) {
    if (allergy != null && allergy!.trim().isEmpty) {
      allergy = null;
    }
  }

  toJson() {
    var body = {
      if (id != null) "id": id,
      "image": image,
      "first_name": firstName,
      "last_name": lastName,
      "birthdate": birthDate,
      "gender": gender.value,
      "allergy": allergy,
    };
    for (var i = 0; i < hobbies.length; i++) {
      body["hobbies[$i]"] = hobbies[i].id;
    }
    for (var i = 0; i < specialCases.length; i++) {
      body["specialCase[$i]"] = specialCases[i].id;
    }
    return body;
  }

  Future<FormData> toForm() async {
    var body = {
      if (id != null) "id": id,
      if (image != null && image!.isNotEmpty && !image!.startsWith("http"))
        "image": MultipartFile.fromFile(image!),
      "first_name": firstName,
      "last_name": lastName,
      "birthdate": birthDate,
      "gender": gender.value,
      "allergy": allergy,
      "specialCase": [specialCases],
    };
    for (var i = 0; i < hobbies.length; i++) {
      body["hobbies[$i]"] = hobbies[i].id;
    }
    for (var i = 0; i < specialCases.length; i++) {
      body["special_cases[$i]"] = specialCases[i].id;
    }
    return FormData.fromMap(body);
  }

  addToForm(FormData data, int index) async {
    if (id != null) {
      data.fields.add(MapEntry("children[$index][id]", "$id"));
    }
    data.fields.add(MapEntry("children[$index][first_name]", firstName));
    data.fields.add(MapEntry("children[$index][last_name]", lastName));
    data.fields
        .add(MapEntry("children[$index][birthdate]", birthDate.toString()));
    data.fields.add(MapEntry("children[$index][gender]", "${gender.value}"));
    if (allergy != null) {
      data.fields.add(MapEntry("children[$index][alergies]", allergy!));
    }

    for (int i = 0; i < hobbies.length; i++) {
      data.fields
          .add(MapEntry("children[$index][hobbies][$i]", "${hobbies[i].id}"));
    }
    for (int i = 0; i < specialCases.length; i++) {
      data.fields.add(MapEntry(
          "children[$index][special_cases][$i]", "${specialCases[i].id}"));
    }
    if (image != null && image!.isNotEmpty && !image!.startsWith("http")) {
      data.files.add(MapEntry(
          "children[$index][profile]", await MultipartFile.fromFile(image!)));
    }
  }
}
