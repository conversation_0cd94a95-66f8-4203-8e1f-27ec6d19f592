import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/features/main/models/destinations.dart';
import 'package:gopal/features/main/widgets/drawer.dart';

import '../categories/categories/index.dart';
import '../activities/my_activities/index.dart';
import '../home/<USER>';
import '../profile/my_profile/index.dart';
import '../settings/usecases/support/widgets/support_fab.dart';
import 'controller.dart';
import 'widgets/bottom_nav_bar.dart';

class MainPage extends StatelessWidget {
  const MainPage({super.key});

  @override
  Widget build(BuildContext context) {
    MainPageController controller = Get.put(MainPageController());
    return Scaffold(
      bottomNavigationBar: const MainNavBar(),
      endDrawer: const HomeDrawer(),
      floatingActionButton: Obx(
        () {
          if (controller.currentDestination == MainDestinations.home) {
            return SupportFab(
              page: Pages.home,
              data: controller.currentDestination.name,
            );
          } else {
            return const SizedBox();
          }
        },
      ),
      body: Obx(
        () {
          Widget currentPage;
          switch (controller.currentDestination) {
            case MainDestinations.home:
              currentPage = const HomePage();
            case MainDestinations.categories:
              currentPage = const CategoriesPage();
            case MainDestinations.my_activities:
              currentPage = const MyActivitiesPage();
            case MainDestinations.profile:
              currentPage = const ProfilePage();
          }
          return AnimatedSwitcher(
            duration: 300.milliseconds,
            transitionBuilder: (child, animation) => FadeTransition(
              opacity: animation,
              child: child,
            ),
            child: currentPage,
          );
        },
      ),
    );
  }
}
