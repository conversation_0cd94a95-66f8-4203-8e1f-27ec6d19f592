import 'dart:math';

import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/widgets/shimmer_loading.dart';

class CenterProfileLoading extends StatelessWidget {
  const CenterProfileLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      children: [
        Row(
          children: [
            ShimmerWidget.card(
              height: 60,
              width: 60,
              borderRadius: BorderRadius.circular(30),
            ),
            const Gap(12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ShimmerWidget.card(
                    height: 25,
                    width: Get.width * .4,
                  ),
                  const Gap(12),
                  ShimmerWidget.card(
                    height: 20,
                    width: Get.width * .5,
                  ),
                ],
              ),
            ),
          ],
        ),
        const Gap(24),
        SizedBox(
          height: 30,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemBuilder: (_, __) =>
                ShimmerWidget.card(width: Random().nextInt(50) + 70),
            separatorBuilder: (_, __) => const Gap(12),
            itemCount: 3,
          ),
        ),
        const Gap(24),
        SizedBox(
          height: 150,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemBuilder: (_, __) => ShimmerWidget.card(
              width: 150,
              borderRadius: BorderRadius.circular(8),
            ),
            separatorBuilder: (_, __) => const Gap(12),
            itemCount: 3,
          ),
        ),
        const Gap(24),
        ShimmerWidget.card(
          height: 200,
          borderRadius: BorderRadius.circular(12),
        ),
      ],
    );
  }
}
