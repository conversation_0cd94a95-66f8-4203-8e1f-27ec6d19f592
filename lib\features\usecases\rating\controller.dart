import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/models/rate.dart';
import 'package:gopal/core/widgets/loading.dart';

import '../../../core/services/rest_api/rest_api.dart';
import '../toast/toast.dart';

class RateController extends GetxController {
  final int bookId;
  RateController(this.bookId);

  TextEditingController comment = TextEditingController();
  double rate = 3.5;

  confirm() async {
    Loading.show();
    ResponseModel response = await Request(
      endPoint: EndPoints.rate_book,
      method: RequestMethod.Post,
      body: {
        "rate": rate,
        "comment": comment.text,
        "causeable_type": "BOOK_TRANSACTION",
        "causeable_id": bookId,
      },
    ).perform();
    Loading.dispose();
    if (response.success) {
      Get.back(result: Review(rate: rate, comment: comment.text));
    } else {
      Toast.show(message: response.message);
    }
  }
}
