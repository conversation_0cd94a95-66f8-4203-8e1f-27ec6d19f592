import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/config/app_builder.dart';
import 'package:gopal/core/config/role.dart';
import 'package:gopal/core/models/user/main_user.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/utils/timer.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/auth/create_account/models/nav.dart';

import '../../../core/services/rest_api/rest_api.dart';
import '../../usecases/toast/toast.dart';
import 'models/nav.dart';

class VerificationPageController extends GetxController with ResendCodeTimer {
  final VerificationPageNav nav;
  VerificationPageController(this.nav);

  AppBuilder appBuilder = Get.find();
  TextEditingController codeController = TextEditingController();

  final Rx<bool> _canVerify = false.obs;
  bool get canVerify => _canVerify.value;
  set canVerify(bool value) => _canVerify.value = value;

  verify() async {
    ResponseModel response = await Request(
      endPoint: nav.isChangePhoneVerification
          ? EndPoints.change_phone_verification
          : EndPoints.verify_code,
      method: RequestMethod.Post,
      body: {
        "phone": nav.phone,
        "code": codeController.text,
      },
    ).perform();
    if (response.success) {
      if (nav.isChangePhoneVerification) {
        Nav.offUntil(appBuilder.role.landing, (_) => false);
        return;
      }
      if ((response.data as Map<String, dynamic>).containsKey('token')) {
        appBuilder.setUserData(
          role: const User(),
          token: response.data['token'],
          user: MainUser.fromJson(response.data),
        );
        await appBuilder.role.initialize();
        Nav.offUntil(appBuilder.role.landing, (_) => false);
      } else {
        Nav.offUntil(
          Pages.create_new_account,
          (route) => route.settings.name == Pages.login.value ? true : false,
          arguments: CreateAccountPageNav(nav.phone),
        );
      }
    } else {
      Toast.show(message: response.message);
    }
  }

  resendCode() async {
    Loading.show();
    ResponseModel response = await Request(
      endPoint: EndPoints.send_code,
      method: RequestMethod.Post,
      body: {"phone": nav.phone},
    ).perform();
    Loading.dispose();
    if (response.success) {
      startTimer(60);
    } else {
      Toast.show(message: response.message);
    }
  }

  @override
  void onInit() {
    startTimer(60);
    super.onInit();
  }

  @override
  void onClose() {
    codeController.dispose();
    cancelTimer();
    super.onClose();
  }
}
