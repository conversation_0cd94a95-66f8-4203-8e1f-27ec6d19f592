enum PeriodicType {
  period,
  date,
  ;

  static PeriodicType fromString(String s) {
    switch (s) {
      case "PERIOD":
        return period;
      case "DATE":
        return date;
      default:
        throw "";
    }
  }
}

enum PeriodicDaysType {
  daily,
  numbered_days,
  dedicated_days;

  static PeriodicDaysType fromString(String s) {
    switch (s) {
      case "EVERY_DAYS":
        return daily;
      case "NUMBERED_DAYS":
        return numbered_days;
      case "DEDICATED_DAYS":
        return dedicated_days;
      default:
        throw "";
    }
  }
}
