import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/rate.dart';
import 'package:gopal/core/models/user/center.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import 'controller.dart';

class RateDialog extends StatelessWidget {
  static Future<Review?> open(
          {required int bookId,
          required CenterModel center,
          Review? rate}) async =>
      await Get.dialog(RateDialog(bookId: bookId, center: center, rate: rate));

  const RateDialog(
      {super.key, required this.center, this.rate, required this.bookId});

  final int bookId;
  final CenterModel center;
  final Review? rate;

  @override
  Widget build(BuildContext context) {
    RateController controller = Get.put(RateController(bookId));
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.width * .1),
        child: Material(
          color: Colors.transparent,
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                padding: const EdgeInsets.fromLTRB(16, 40, 16, 16),
                margin: const EdgeInsets.only(top: 50),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(35),
                  color: StyleRepo.white,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      children: [
                        AppImage(
                          path: center.profileImage.small,
                          type: ImageType.CachedNetwork,
                          height: 50,
                          width: 50,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(25),
                          ),
                        ),
                        const Gap(12),
                        Expanded(child: Text(center.name)),
                      ],
                    ),
                    const Gap(16),
                    RatingBar.builder(
                      itemSize: Get.width * .1,
                      unratedColor: StyleRepo.grey.shade300,
                      allowHalfRating: true,
                      initialRating: rate?.rate ?? controller.rate,
                      glowColor: StyleRepo.grey.shade100,
                      itemBuilder: (_, __) {
                        return SvgIcon(
                          Assets.icons.star.path,
                          color: StyleRepo.yellow.shade600,
                        );
                      },
                      ignoreGestures: rate != null,
                      onRatingUpdate: (value) => controller.rate = value,
                    ),
                    const Gap(12),
                    if (rate == null)
                      TextField(
                        controller: controller.comment,
                        minLines: 2,
                        maxLines: 3,
                        decoration: InputDecoration(
                          hintText: LocaleKeys.add_your_comment.tr(),
                        ),
                      ),
                    if (rate != null) Text(rate?.comment ?? ""),
                    const Gap(12),
                    AppElevatedButton(
                      onTap: () async => rate == null
                          ? await controller.confirm()
                          : Get.back(),
                      child: Text(LocaleKeys.confirm.tr()),
                    ),
                  ],
                ),
              ),
              Assets.icons.rateSticker.svg(),
            ],
          ),
        ),
      ),
    );
  }
}
