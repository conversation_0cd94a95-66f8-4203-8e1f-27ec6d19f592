import 'dart:developer';
import 'dart:io';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:dio/dio.dart';
import 'package:get/get_rx/get_rx.dart';
import 'package:path_provider/path_provider.dart';

import 'errors.dart';
import 'status.dart';

class AudioPlayer {
  static final AudioPlayer _singleton = AudioPlayer._internal();

  factory AudioPlayer() {
    return _singleton;
  }

  AudioPlayer._internal();

  final Rx<PlayerStatus> _playerStatus = PlayerStatus.notStarted.obs;
  PlayerStatus get playerStatus => _playerStatus.value;

  init(String filePath) async {
    try {
      _playerStatus.value = PlayerStatus.initializing;
      String? path = await _downloadFile(filePath);
      if (path != null) {
        await initPlaying(path);
      }
    } on CancelledException catch (_) {
      _playerStatus.value = PlayerStatus.notStarted;
    } catch (e) {
      _playerStatus.value = PlayerStatus.error;
      rethrow;
    }
  }

  dispose() {
    cancelToken?.cancel();
    player?.dispose();
    player = null;
    _playerStatus.value = PlayerStatus.notStarted;
    _playerStatus.refresh();
  }

  //SECTION - Download File and save it
  CancelToken? cancelToken;
  Future<String?> _downloadFile(String filePath) async {
    Dio dio = Dio();
    cancelToken = CancelToken();
    try {
      Response<List<int>> response = await dio.get(
        filePath,
        cancelToken: cancelToken,
        options: Options(
          responseType: ResponseType.bytes,
          followRedirects: false,
        ),
      );
      cancelToken = null;
      return await saveFile(response.data!);
    } on DioException catch (error) {
      cancelToken = null;
      if (error.type == DioExceptionType.cancel) {
        throw CancelledException();
      }
      throw "Can't connect to the audio";
    } catch (e) {
      cancelToken = null;
      throw "Can't connect to the audio";
    }
  }

  Future<String> getFilePath({
    String fileName = "temp",
    String fileExtension = "m4a",
  }) async {
    Directory base = await getTemporaryDirectory();
    return "${base.path}/$fileName.$fileExtension";
  }

  Future<String> saveFile(List<int> data) async {
    File file = File(await getFilePath());
    log(file.path, name: "Player");
    var raf = file.openSync(mode: FileMode.write);
    raf.writeFromSync(data);
    await raf.close();
    log("file saved in ${file.path}");
    return file.path;
  }

  //!SECTION

  //SECTION - Player
  PlayerController? player;

  initPlayer() async {
    if (player != null) {
      player!.release();
      player = PlayerController();
      return;
    }
    player = PlayerController();
  }

  initPlaying(String filePath) async {
    await initPlayer();
    await player!.preparePlayer(path: filePath);
    player!.setFinishMode(finishMode: FinishMode.pause);
    _playerStatus.value = PlayerStatus.ready;
    play();
  }

  play() async {
    await player?.startPlayer();
  }

  pause() async {
    await player?.pausePlayer();
  }

  playPause() async {
    if (player!.playerState == PlayerState.playing) {
      await pause();
    } else {
      await play();
    }
  }

  //!SECTION
}
