import 'package:gopal/core/models/category.dart';
import 'package:gopal/core/models/chat/chat_room.dart';
import 'package:gopal/core/models/image.dart';

import '../../../../core/models/user/center.dart';

class CenterDetails extends CenterModel {
  late List<Category> categories = [];
  late List<ImageModel> media = [];
  late List<CenterBranch> branches = [];
  late CenterBranch mainBranch;
  late int activitiesNumber;
  late int booksCount;
  int? roomId;

  CenterDetails.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    activitiesNumber = json['activities_count'] ?? 0;
    booksCount = json['book_transactions_count'] ?? 10;
    roomId = json['room_id'];
    for (var category in json['categories']) {
      categories.add(Category.fromJson(category));
    }
    for (var image in json['media']['media']) {
      media.add(ImageModel.fromJson(image));
    }
    bool isMainDetected = false;
    for (var branch in json['providerBranches']) {
      if (branch['is_main'] == 1) {
        mainBranch = CenterBranch.fromJson(branch);
        isMainDetected = true;
        continue;
      }
      branches.add(CenterBranch.fromJson(branch));
    }
    if (!isMainDetected) {
      mainBranch = branches.first;
      branches.removeAt(0);
    }
  }

  ChatRoom? toChatRoom() => roomId == null
      ? null
      : ChatRoom(
          id: roomId!,
          name: name,
          image: profileImage,
          date: DateTime.now(),
        );
}

class CenterBranch {
  double lat;
  double lng;
  String cityName;
  String? name;

  CenterBranch({
    required this.lat,
    required this.lng,
    this.name,
    required this.cityName,
  });

  factory CenterBranch.fromJson(Map<String, dynamic> json) => CenterBranch(
      lat: json["lat"]?.toDouble(),
      lng: json["lng"]?.toDouble(),
      name: json['name'],
      cityName: json['city']?["name"] ?? "");

  Map<String, dynamic> toJson() => {
        "lat": lat,
        "lng": lng,
        "name": name,
      };
}
