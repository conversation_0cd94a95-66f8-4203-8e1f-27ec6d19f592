import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:gopal/core/services/location.dart';
import 'package:gopal/core/services/rest_api/rest_api.dart';
import 'package:gopal/features/center/centers_on_map/models/center_location.dart';

class CentersOnMapPageController extends GetxController {
  Request<CenterLocation> centersRequest = Request(
    endPoint: EndPoints.centers_on_map,
    fromJson: CenterLocation.fromJson,
  );

  LatLng? location;

  init() async {
    LatLng? result = await LocationUtils.getMyLocation(openSettings: false);
    if (result != null) {
      location = result;
    }
    await centersRequest.perform();
  }

  @override
  void onInit() {
    init();
    super.onInit();
  }

  @override
  void onClose() {
    centersRequest.stop();
    super.onClose();
  }
}
