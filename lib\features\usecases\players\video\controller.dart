import 'package:get/get.dart';
import 'package:chewie/chewie.dart';
import 'package:video_player/video_player.dart';

import '../videos_duration_end.dart';

class VideoPlayerWidgetController extends GetxController {
  final Function()? onCloseToEnd;
  final bool autoPlay;

  late VideoPlayerController videoPlayerController;
  late ChewieController chewieController;
  late String video;

  VideoPlayerWidgetController(this.video, this.onCloseToEnd, this.autoPlay);

  final Rx<bool> _isInitialized = false.obs;
  bool get isInitialized => _isInitialized.value;
  set isInitialized(bool value) => _isInitialized.value = value;
  player() async {
    videoPlayerController = VideoPlayerController.networkUrl(Uri.parse(video));
    try {
      await videoPlayerController.initialize();
    } catch (_) {}
    chewieController = ChewieController(
      videoPlayerController: videoPlayerController,
      autoPlay: autoPlay,
      looping: true,
      aspectRatio: videoPlayerController.value.aspectRatio,
    );
    isInitialized = true;
    videoPlayerController.addListener(() {
      if (!videoPlayerController.value.isPlaying) return;
      if (isMediaCloseToEnd(videoPlayerController.value.position.inSeconds,
          videoPlayerController.value.duration.inSeconds)) {
        onCloseToEnd?.call();
      }
    });
  }

  @override
  void onInit() {
    player();
    super.onInit();
  }

  pause() {
    chewieController.pause();
  }

  @override
  void onClose() {
    videoPlayerController.dispose();
    chewieController.dispose();
    super.onClose();
  }
}
