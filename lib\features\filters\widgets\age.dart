import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/features/widgets/general_componenets/dropdown.dart';

import '../controller.dart';

const int _kmaxAge = 35;

class AgeFilter extends GetView<FiltersPageController> {
  const AgeFilter({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Gap(24),
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    LocaleKeys.minimum_age.tr(),
                    style: context.textTheme.titleMedium!.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  const Gap(6),
                  Obx(
                    () {
                      int length = _kmaxAge -
                          (controller.maxAge == 0
                              ? 0
                              : _kmaxAge - controller.maxAge) -
                          1;
                      return AppDropdown<int>(
                        value: controller.minAge,
                        items: List.generate(length + 1, (index) => index)
                            .map(
                              (e) => e == 0
                                  ? DropdownMenuItem<int>(
                                      value: 0,
                                      child: Text(LocaleKeys.none.tr()),
                                    )
                                  : DropdownMenuItem<int>(
                                      value: e,
                                      child: Text(e.toString()),
                                    ),
                            )
                            .toList(),
                        onChanged: (value) => controller.minAge = value!,
                      );
                    },
                  ),
                ],
              ),
            ),
            const Gap(16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    LocaleKeys.maximum_age.tr(),
                    style: context.textTheme.titleMedium!.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  const Gap(6),
                  Obx(
                    () {
                      int length = _kmaxAge - controller.minAge;
                      return AppDropdown<int>(
                        value: controller.maxAge,
                        items: List.generate(length + 1,
                                (index) => index + controller.minAge)
                            .map(
                              (e) => e == controller.minAge
                                  ? DropdownMenuItem<int>(
                                      value: 0,
                                      child: Text(LocaleKeys.none.tr()),
                                    )
                                  : DropdownMenuItem<int>(
                                      value: e,
                                      child: Text(e.toString()),
                                    ),
                            )
                            .toList(),
                        onChanged: (value) => controller.maxAge = value!,
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }
}
