import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';

class AuthBackground extends StatelessWidget {
  final Widget child;
  final AssetGenImage? backgroundImage;
  const AuthBackground({super.key, required this.child, this.backgroundImage});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: double.infinity,
      width: double.infinity,
      child: Stack(
        children: [
          Positioned(
            top: 0,
            right: 0,
            left: 0,
            child: (backgroundImage ?? Assets.images.background3).image(
              color: Colors.white.withValues(alpha: .5),
              colorBlendMode: BlendMode.srcATop,
            ),
          ),
          Positioned(
            bottom: 0,
            right: 0,
            left: 0,
            child: Transform.rotate(
              angle: -math.pi,
              child: Assets.images.background3.image(
                color: Colors.white.withValues(alpha: .5),
                colorBlendMode: BlendMode.srcATop,
              ),
            ),
          ),
          child,
        ],
      ),
    );
  }
}
