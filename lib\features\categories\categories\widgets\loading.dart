import 'package:flutter/material.dart';
import 'package:gopal/core/widgets/shimmer_loading.dart';

class CategoriesLoading extends StatelessWidget {
  const CategoriesLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        mainAxisSpacing: 16,
        crossAxisSpacing: 12,
      ),
      itemBuilder: (_, __) =>
          ShimmerWidget.card(borderRadius: BorderRadius.circular(12)),
    );
  }
}
