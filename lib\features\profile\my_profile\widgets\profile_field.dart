import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/style/repo.dart';

class ProfielField extends StatelessWidget {
  final String title;
  final Widget child;
  final Widget? trailing;
  const ProfielField({
    super.key,
    required this.title,
    required this.child,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: context.textTheme.bodyMedium!.copyWith(
            color: StyleRepo.grey.shade700,
          ),
        ),
        const Gap(8),
        Row(
          children: [
            Expanded(
              child: Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                decoration: BoxDecoration(
                  color: StyleRepo.white,
                  boxShadow: StyleRepo.elevation_3,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: DefaultTextStyle(
                  style: context.textTheme.bodyLarge!.copyWith(fontSize: 18),
                  child: child,
                ),
              ),
            ),
            if (trailing != null) const Gap(8),
            if (trailing != null) trailing!,
          ],
        ),
      ],
    );
  }
}
