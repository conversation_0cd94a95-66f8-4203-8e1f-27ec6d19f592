import 'package:get/get.dart';
import 'package:gopal/core/services/rest_api/rest_api.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';

import 'models/point_setting.dart';

class PointsGuidController extends GetxController {
  ObsList<PointsSetting> settings = ObsList([]);

  loadData() async {
    ResponseModel response = await Request(
      endPoint: EndPoints.points_settings,
    ).perform();
    if (response.success) {
      List<PointsSetting> temp = [];
      for (var json in response.data) {
        PointsSetting setting = PointsSetting.fromJson(json);
        temp.add(setting);
      }
      settings.value = temp;
    } else {
      settings.error = response.message;
    }
  }

  @override
  void onInit() {
    loadData();
    super.onInit();
  }
}
