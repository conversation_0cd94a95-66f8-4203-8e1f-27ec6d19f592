import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/style/repo.dart';

import '../status.dart';

class AppToast extends StatelessWidget {
  final String title;
  final String message;
  final ToastStatus status;

  const AppToast({
    super.key,
    required this.title,
    required this.message,
    this.status = ToastStatus.success,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: Get.width,
      padding: const EdgeInsetsDirectional.only(start: 5),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(10),
        color:
            status == ToastStatus.success
                ? StyleRepo.turquoise
                : status == ToastStatus.warning
                ? StyleRepo.yellow.shade800
                : StyleRepo.red,
      ),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          color: StyleRepo.white.withValues(alpha: 0.3),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                color: StyleRepo.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(message, style: const TextStyle(color: StyleRepo.white)),
          ],
        ),
      ),
    );
  }
}
