import 'package:blur/blur.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../style/assets/gen/assets.gen.dart';
import 'shimmer_loading.dart';

abstract class Loading {
  static show() {
    Get.dialog(
      PopScope(
        canPop: false,
        child: Stack(
          children: [
            Blur(
              blur: 4,
              blurColor: Colors.white.withValues(alpha: 0.1),
              child: const SizedBox.expand(),
            ),
            const LoadingWidget(),
          ],
        ),
      ),
      useSafeArea: true,
      barrierDismissible: false,
      barrierColor: Colors.transparent,
    );
  }

  static dispose() {
    if (Get.isDialogOpen!) {
      Get.back();
    }
  }
}

class LoadingWidget extends StatelessWidget {
  const LoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(child: Assets.lotties.loading.lottie());
  }
}

class FieldLoadingWidget extends StatelessWidget {
  const FieldLoadingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return ShimmerWidget.card(
      height: 50,
      borderRadius: BorderRadius.circular(12),
    );
  }
}
