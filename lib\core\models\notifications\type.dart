enum NotificationType {
  general, // 1, 7 ,17, 22
  activity, // 3, 6
  book, // 4, 5, 9
  confirm_book,
  activity_end,
  suggestion, // 8
  update, // 10
  points, // 2, 15
  payment_success,
  answer_question,
  new_message, // For chat
  ;

  static NotificationType fromString(String s) {
    switch (s) {
      case "NEW_ACTIVITY":
      case "ACTIVITY_UPDATE":
        return activity;
      case "ACTIVITY_RECOMMENDATION":
        return suggestion;
      case "BOOK_TRANSACTION_CONFIRMATION":
        return confirm_book;
      case "REVIEW_PROVIDER":
        return activity_end;
      case "PAYMENT_ACCEPTED":
        return payment_success;
      case "ANSWER_QUESTION":
        return answer_question;
      //
      case "NEW_MESSAGE":
        return new_message;
      default:
        return general;
    }
  }
}


// general : null
// activity: {"id": activity_id}
// book: {"id": book_id}
// suggestion: {ids: [suggestions_ids]} // need API to get them
// update: {"abdroid": "google play link","ios": "app store link"}
// payment_success: {"pdf_url": "link"} // invoicement link
// points: IDK