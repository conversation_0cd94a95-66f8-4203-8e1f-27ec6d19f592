// DO NOT EDIT. This is code generated via package:easy_localization/generate.dart

abstract class  LocaleKeys {
  static const arabic = 'arabic';
  static const english = 'english';
  static const confirm = 'confirm';
  static const or = 'or';
  static const optional = 'optional';
  static const this_field_is_required = 'this_field_is_required';
  static const email_is_not_valid = 'email_is_not_valid';
  static const phone_number_is_not_valid = 'phone_number_is_not_valid';
  static const very_short = 'very_short';
  static const very_long = 'very_long';
  static const enter_your_number = 'enter_your_number';
  static const login_using_phone_number = 'login_using_phone_number';
  static const terms_and_conditions = 'terms_and_conditions';
  static const privacy_policy = 'privacy_policy';
  static const verify_phone_number = 'verify_phone_number';
  static const enter_the_code_sent_to = 'enter_the_code_sent_to';
  static const did_not_receive_a_code = 'did_not_receive_a_code';
  static const resend_code = 'resend_code';
  static const select_language = 'select_language';
  static const select_your_language_to_continue = 'select_your_language_to_continue';
  static const signup = 'signup';
  static const signup_message = 'signup_message';
  static const continue_as_a_guest = 'continue_as_a_guest';
  static const create_new_account = 'create_new_account';
  static const enter_your_information_to_create_new_account = 'enter_your_information_to_create_new_account';
  static const name = 'name';
  static const enter_your_name = 'enter_your_name';
  static const date_of_birth = 'date_of_birth';
  static const age = 'age';
  static const email = 'email';
  static const add_child = 'add_child';
  static const edit_child = 'edit_child';
  static const accept_terms_message_1 = 'accept_terms_message_1';
  static const accept_terms_message_2 = 'accept_terms_message_2';
  static const comma = 'comma';
  static const accept_terms_message_3 = 'accept_terms_message_3';
  static const accept_terms_message_4 = 'accept_terms_message_4';
  static const accept_terms_message_5 = 'accept_terms_message_5';
  static const select_photo = 'select_photo';
  static const first_name = 'first_name';
  static const enter_first_name = 'enter_first_name';
  static const last_name = 'last_name';
  static const enter_last_name = 'enter_last_name';
  static const hobbies = 'hobbies';
  static const gender = 'gender';
  static const select_gender = 'select_gender';
  static const gender_male = 'gender_male';
  static const gender_female = 'gender_female';
  static const medical_information = 'medical_information';
  static const allergies = 'allergies';
  static const spring_allergy = 'spring_allergy';
  static const special_case = 'special_case';
  static const please_select_child_gender = 'please_select_child_gender';
  static const home = 'home';
  static const categories = 'categories';
  static const subcategories = 'subcategories';
  static const my_activities = 'my_activities';
  static const profile = 'profile';
  static const see_all = 'see_all';
  static const centers_near_you = 'centers_near_you';
  static const recommended_activities = 'recommended_activities';
  static const seasonal_activities = 'seasonal_activities';
  static const family_activities = 'family_activities';
  static const available = 'available';
  static const unavailable = 'unavailable';
  static const available_seats = 'available_seats';
  static const season_expiration_days = 'season_expiration_days';
  static const activity_details = 'activity_details';
  static const pictures_related_to_the_activity = 'pictures_related_to_the_activity';
  static const pictures_related_to_the_center = 'pictures_related_to_the_center';
  static const activity_price = 'activity_price';
  static const activity_date = 'activity_date';
  static const activity_duration = 'activity_duration';
  static const time = 'time';
  static const target_age = 'target_age';
  static const capacity = 'capacity';
  static const from_to = 'from_to';
  static const about_activity = 'about_activity';
  static const about_center = 'about_center';
  static const read_more = 'read_more';
  static const read_less = 'read_less';
  static const organizers = 'organizers';
  static const booking = 'booking';
  static const search_for_activities = 'search_for_activities';
  static const contact_us = 'contact_us';
  static const language = 'language';
  static const change_language = 'change_language';
  static const notifications = 'notifications';
  static const about_us = 'about_us';
  static const help_and_support = 'help_and_support';
  static const send_your_questions_and_problems = 'send_your_questions_and_problems';
  static const delete_account = 'delete_account';
  static const delete_my_account = 'delete_my_account';
  static const logout = 'logout';
  static const logout_of_your_account = 'logout_of_your_account';
  static const edit_profile = 'edit_profile';
  static const number = 'number';
  static const children = 'children';
  static const years = 'years';
  static const months = 'months';
  static const hello = 'hello';
  static const center = 'center';
  static const provided_activities = 'provided_activities';
  static const activities_provided = 'activities_provided';
  static const branches_location = 'branches_location';
  static const reviews = 'reviews';
  static const booked_activities = 'booked_activities';
  static const delete = 'delete';
  static const cancel = 'cancel';
  static const filters = 'filters';
  static const price_range = 'price_range';
  static const rating = 'rating';
  static const minimum_age = 'minimum_age';
  static const maximum_age = 'maximum_age';
  static const special_need = 'special_need';
  static const apply_filters = 'apply_filters';
  static const reset = 'reset';
  static const child_profile = 'child_profile';
  static const none = 'none';
  static const all = 'all';
  static const change_phone_number = 'change_phone_number';
  static const send_message_to_admin = 'send_message_to_admin';
  static const support_subtitle = 'support_subtitle';
  static const write_your_note = 'write_your_note';
  static const no_internet = 'no_internet';
  static const validation_error = 'validation_error';
  static const server_error = 'server_error';
  static const forbidden = 'forbidden';
  static const no_data_found = 'no_data_found';
  static const no_named_data_found = 'no_named_data_found';
  static const success = 'success';
  static const warning = 'warning';
  static const failure = 'failure';
  static const distance_m_from_your_area = 'distance_m_from_your_area';
  static const distance_km_from_your_area = 'distance_km_from_your_area';
  static const related_activities = 'related_activities';
  static const s_a_r = 's_a_r';
  static const set = 'set';
  static const session = 'session';
  static const per_hour = 'per_hour';
  static const per_person = 'per_person';
  static const per_course = 'per_course';
  static const family_activity = 'family_activity';
  static const targeted_for_families = 'targeted_for_families';
  static const season = 'season';
  static const seats = 'seats';
  static const available_sets = 'available_sets';
  static const open_close = 'open_close';
  static const week_day_sunday = 'week_day.sunday';
  static const week_day_monday = 'week_day.monday';
  static const week_day_tuesday = 'week_day.tuesday';
  static const week_day_wednesday = 'week_day.wednesday';
  static const week_day_thursday = 'week_day.thursday';
  static const week_day_friday = 'week_day.friday';
  static const week_day_suterday = 'week_day.suterday';
  static const week_day = 'week_day';
  static const daily = 'daily';
  static const for_days = 'for_days';
  static const days_at_week = 'days_at_week';
  static const starting_from_the_date_of_joining = 'starting_from_the_date_of_joining';
  static const select_one_child_at_least = 'select_one_child_at_least';
  static const date = 'date';
  static const select_date = 'select_date';
  static const select_time = 'select_time';
  static const to = 'to';
  static const delete_account_confirmation_message = 'delete_account_confirmation_message';
  static const logout_confirmation_message = 'logout_confirmation_message';
  static const all_children = 'all_children';
  static const add_session = 'add_session';
  static const number_of_sessions = 'number_of_sessions';
  static const delete_book_confirmation = 'delete_book_confirmation';
  static const cancel_book_confirmation = 'cancel_book_confirmation';
  static const booked_set = 'booked_set';
  static const booked_session = 'booked_session';
  static const next_session = 'next_session';
  static const book_details = 'book_details';
  static const certificate = 'certificate';
  static const click_to_rate = 'click_to_rate';
  static const rejected = 'rejected';
  static const messages = 'messages';
  static const support_replies = 'support_replies';
  static const add_your_comment = 'add_your_comment';
  static const your_note_added_successfully = 'your_note_added_successfully';
  static const price_type = 'price_type';
  static const any = 'any';
  static const and_up = 'and_up';
  static const discount = 'discount';
  static const guest_message = 'guest_message';
  static const delete_child_confirmation_message = 'delete_child_confirmation_message';
  static const pending = 'pending';
  static const progress = 'progress';
  static const completed = 'completed';
  static const favourite = 'favourite';
  static const photos = 'photos';
  static const camera = 'camera';
  static const document = 'document';
  static const location = 'location';
  static const video = 'video';
  static const type_message = 'type_message';
  static const file_must_be_smaller_than = 'file_must_be_smaller_than';
  static const you_should_enable_mic_permission_in_app_settings = 'you_should_enable_mic_permission_in_app_settings';
  static const next = 'next';
  static const skip = 'skip';
  static const boarding_1 = 'boarding_1';
  static const boarding_2 = 'boarding_2';
  static const boarding_3 = 'boarding_3';
  static const activity_summary = 'activity_summary';
  static const for_something = 'for_something';
  static const family = 'family';
  static const seasonal = 'seasonal';
  static const details = 'details';
  static const sessions_number = 'sessions_number';
  static const discover_centers = 'discover_centers';
  static const go_to_payment = 'go_to_payment';
  static const holding = 'holding';
  static const friend_invitation_code = 'friend_invitation_code';
  static const n_points = 'n_points';
  static const invite_friends = 'invite_friends';
  static const your_invitation_code = 'your_invitation_code';
  static const copy_the_code = 'copy_the_code';
  static const invitation_message = 'invitation_message';
  static const share = 'share';
  static const gifting_system = 'gifting_system';
  static const redeem_points = 'redeem_points';
  static const redeem_n_points_to_m_unit = 'redeem_n_points_to_m_unit';
  static const chats = 'chats';
  static const gopal_home_message = 'gopal_home_message';
  static const invoice = 'invoice';
  static const contact_us_on_whatsapp = 'contact_us_on_whatsapp';
  static const n_books_tow = 'n_books.tow';
  static const n_books = 'n_books';
  static const search = 'search';
  static const centers = 'centers';
  static const activities = 'activities';
  static const search_for_centers = 'search_for_centers';
  static const todays_offers = 'todays_offers';
  static const select_persons_count = 'select_persons_count';
  static const select_the_number_of_people_participating_in_the_activity = 'select_the_number_of_people_participating_in_the_activity';
  static const do_you_want_to_book_the_activity_for_you = 'do_you_want_to_book_the_activity_for_you';
  static const book_this_activity_and_be_one_of_the_participants = 'book_this_activity_and_be_one_of_the_participants';
  static const access_your_current_location = 'access_your_current_location';
  static const to_get_perfect_experience_you_need_to_activate_the_access_to_your_location = 'to_get_perfect_experience_you_need_to_activate_the_access_to_your_location';
  static const update = 'update';
  static const update_app = 'update_app';
  static const this_version_will_expire_at_x = 'this_version_will_expire_at_x';
  static const download_the_new_version_now = 'download_the_new_version_now';
  static const book_success_msg = 'book_success_msg';
  static const interactive_map = 'interactive_map';
  static const you_have_x_and_you_can_redeem_y = 'you_have_x_and_you_can_redeem_y';

}
