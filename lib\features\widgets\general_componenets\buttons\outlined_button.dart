// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/style/repo.dart';

const Duration _kAnimationDuration = Duration(milliseconds: 300);

class AppOutlinedButton extends StatelessWidget {
  final Widget child;
  final Function() onTap;
  bool withLoading;
  final double height;
  final double? width;
  final double horizontalPadding;
  final bool enabled;
  final Color? backgroundColor;
  final Color? borderColor;

  final Rx<bool> _loading = false.obs;
  bool get loading => _loading.value;
  set loading(bool value) => _loading.value = value;

  AppOutlinedButton({
    super.key,
    required this.onTap,
    required this.child,
    this.withLoading = true,
    this.backgroundColor,
    this.borderColor,
    this.height = 50,
    this.horizontalPadding = 8,
    this.width = double.infinity,
    this.enabled = true,
  }) {
    if (onTap is! Future Function()) {
      withLoading = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(height / 2),
      onTap: () async {
        if (!enabled) return;
        if (!withLoading) {
          onTap();
        } else {
          if (loading) return;
          loading = true;
          await onTap();
          loading = false;
        }
      },
      child: AnimatedContainer(
        duration: _kAnimationDuration,
        padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
        height: height,
        width: width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(height / 2),
          color: backgroundColor ?? StyleRepo.blueViolet.withValues(alpha: .3),
          border: Border.all(color: borderColor ?? StyleRepo.blueViolet),
        ),
        alignment: Alignment.center,
        child: Obx(
          () => AnimatedSwitcher(
            duration: 300.milliseconds,
            transitionBuilder: (child, animation) {
              return FadeTransition(opacity: animation, child: child);
            },
            child:
                loading
                    ? const SizedBox(
                      height: 20,
                      width: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 3,
                        color: StyleRepo.white,
                      ),
                    )
                    : DefaultTextStyle(
                      style: context.textTheme.titleMedium!,
                      child: child,
                    ),
          ),
        ),
      ),
    );
  }
}
