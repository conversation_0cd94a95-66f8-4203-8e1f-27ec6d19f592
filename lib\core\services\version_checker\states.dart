enum VersionState {
  good,
  soft,
  hard,
  ;

  bool get isGood => this == good;
  bool get isSoft => this == soft;
  bool get isHard => this == hard;

  static VersionState fromString(String s) {
    switch (s) {
      case "good":
        return good;
      case "must_update":
        return hard;
      case "should_update":
        return soft;
      default:
        throw "";
    }
  }
}

enum VersionCheckingState {
  unchecked,
  inProgress,
  failed,
  checked,
  ;

  bool get isUncheked => this == unchecked;
  bool get isInProgress => this == inProgress;
  bool get isFailed => this == failed;
  bool get isChecked => this == checked;
}
