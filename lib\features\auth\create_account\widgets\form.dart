import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/constants/gender.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/utils/validator.dart';
import 'package:gopal/features/widgets/general_componenets/form_field_error.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import '../../../usecases/gender/widgets/gender_tile.dart';
import '../controller.dart';

class CreateAccountForm extends GetView<CreateAccountPageController> {
  const CreateAccountForm({super.key});

  @override
  Widget build(BuildContext context) {
    return Form(
      key: controller.form,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(LocaleKeys.name.tr()),
          const Gap(4),
          TextFormField(
            controller: controller.nameController,
            decoration: InputDecoration(
              hintText: LocaleKeys.enter_your_name.tr(),
            ),
            validator: (str) {
              String? error;
              error = Validator.notNullValidation(str);
              if (error != null) return error;
              error = Validator.lengthValidation(str);
              if (error != null) return error;
              return null;
            },
          ),
          const Gap(20),
          Text("${LocaleKeys.email.tr()} (${LocaleKeys.optional.tr()})"),
          const Gap(4),
          TextFormField(
            controller: controller.emailController,
            keyboardType: TextInputType.emailAddress,
            decoration: const InputDecoration(hintText: "<EMAIL>"),
            validator: Validator.optionalEmailValidation,
          ),
          const Gap(20),
          Text(LocaleKeys.date_of_birth.tr()),
          const Gap(12),
          GestureDetector(
            onTap: () => controller.pickDate(context),
            child: TextFormField(
              controller: controller.birthDateController,
              style: TextStyle(color: StyleRepo.grey.shade800),
              enabled: false,
              decoration: InputDecoration(
                hintText: LocaleKeys.date_of_birth.tr(),
                suffixIcon: Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: SvgIcon(Assets.icons.calenderOutlined.path),
                ),
              ),
              validator: Validator.notNullValidation,
            ),
          ),
          const Gap(20),
          Text(LocaleKeys.gender.tr()),
          const Gap(12),
          FormField<Gender>(
            initialValue: controller.gender.value,
            validator: Validator.notNullValidation,
            builder: (state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  GestureDetector(
                    onTap: () async {
                      Gender? result = await controller.pickGender();
                      if (result != null) {
                        state.didChange(result);
                      }
                    },
                    child: Obx(() {
                      if (controller.gender.hasData) {
                        return GenderTile(controller.gender.value!);
                      } else {
                        return Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(
                            vertical: 12,
                            horizontal: 16,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: StyleRepo.blueViolet),
                          ),
                          child: Text(
                            LocaleKeys.select_gender.tr(),
                            style: context.textTheme.bodyMedium!.copyWith(
                              fontWeight: FontWeight.w600,
                              color: StyleRepo.blueViolet,
                            ),
                          ),
                        );
                      }
                    }),
                  ),
                  if (state.hasError) FormFieldError(error: state.errorText!),
                ],
              );
            },
          ),
          const Gap(20),
          Text(
            "${LocaleKeys.friend_invitation_code.tr()} (${LocaleKeys.optional.tr()})",
          ),
          const Gap(4),
          TextFormField(
            controller: controller.invitationCodeController,
            keyboardType: TextInputType.emailAddress,
            decoration: const InputDecoration(hintText: "#####"),
          ),
          const Gap(20),
        ],
      ),
    );
  }
}
