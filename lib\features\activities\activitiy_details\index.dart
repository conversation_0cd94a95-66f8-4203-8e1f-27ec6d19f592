import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/activity/constants/type.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/services/share/deep_link.dart';
import 'package:gopal/core/services/share/share.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/activities/widgets/activities_list.dart';
import 'package:gopal/features/settings/usecases/support/widgets/support_fab.dart';
import 'package:gopal/features/widgets/general_componenets/banner.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/outlined_button.dart';
import 'package:gopal/features/widgets/general_componenets/pages/general_page.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';
import 'package:gopal/features/widgets/horizontal_list.dart';

import '../usecases/open_sessions_calendar/index.dart';
import '../usecases/specific_days_calendar/index.dart';
import 'controller.dart';
import 'widgets/about.dart';
import 'widgets/categories.dart';
import 'widgets/header.dart';
import 'widgets/info_column.dart';
import 'widgets/organizers.dart';
import 'widgets/pictures.dart';
import 'widgets/sets.dart';

class ActivityDetailsPage extends StatelessWidget {
  final int id;
  const ActivityDetailsPage({super.key, required this.id});

  @override
  Widget build(BuildContext context) {
    ActivityDetailsPageController controller = Get.find(tag: "$id");
    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () async {
        DeepLinks.pageBack(context);
        return false;
      },
      child: GeneralPage(
        mustHasBack: true,
        title: controller.activityRequest.objectBuilder(
          builder: (context, activity) => Text(activity.name),
          errorBuilder: (_, __) => Text(LocaleKeys.activity_details.tr()),
          loader: (_) => Text(LocaleKeys.activity_details.tr()),
        ),
        actions: [
          IconButton(
            onPressed: () => ShareHelper.shareActivity(id),
            icon: SvgIcon(Assets.icons.share.path),
          ),
        ],
        floatingActionButton:
            SupportFab(page: Pages.activity_details, data: id),
        withBackground: true,
        child: controller.activityRequest.objectBuilder(
          withRefresh: true,
          onRefresh: () => controller.refreshData(),
          builder: (context, activity) {
            return BanneredWidget(
              message: "discount",
              color: StyleRepo.turquoise,
              isVisible: activity.isDiscountedToday,
              child: ListView(
                padding: const EdgeInsets.only(top: 24),
                children: [
                  ActivityDetailsHeader(activity: activity),
                  const Gap(16),
                  ActivityCategories(categories: activity.categories),
                  const Gap(16),
                  ActivityPictures(media: activity.media),
                  const Gap(16),
                  ActivityInfoColumn(activity: activity),
                  const Gap(24),
                  AboutActivity(description: activity.description),
                  const Gap(24),
                  ActivityOrganizers(organizers: activity.organizers),
                  //
                  if (activity.type == ActivityType.numbered_session)
                    const Gap(24),
                  if (activity.type == ActivityType.numbered_session)
                    ActivitySets(id: activity.id),
                  //
                  if (activity.type == ActivityType.periodic_days)
                    const Gap(24),
                  if (activity.type == ActivityType.periodic_days)
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: Get.width * .1),
                      child: AppElevatedButton(
                        enabled: activity.isAvailable,
                        onTap: () => controller.bookAPeriodic(
                            activityId: activity.id,
                            periodId: activity.periodic!.id),
                        child: Text(LocaleKeys.booking.tr()),
                      ),
                    ),
                  if (activity.type == ActivityType.specific_days &&
                      activity.isAvailable)
                    const Gap(24),
                  if (activity.type == ActivityType.specific_days &&
                      activity.isAvailable)
                    SpecificDaysDataWidget(
                      id: activity.id,
                      specificDayId: activity.specificDayId!,
                      minDate: activity.minDate,
                      maxDate: activity.maxDate,
                    ),
                  //
                  if (activity.type == ActivityType.open_sessions &&
                      activity.isAvailable)
                    const Gap(24),
                  if (activity.type == ActivityType.open_sessions &&
                      activity.isAvailable)
                    OpenSessionsDataWidget(
                      id: activity.id,
                      priceType: activity.priceType,
                      minDate: activity.minDate!,
                      maxDate: activity.maxDate!,
                      minPersons: activity.minPersonsNumber,
                      maxPersons: activity.maxPersonsNumber,
                    ),
                  //
                  if (activity.isSupportCustom) const Gap(24),
                  if (activity.isSupportCustom)
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: Get.width * .1),
                      child: AppOutlinedButton(
                        enabled: activity.isAvailable,
                        onTap: () =>
                            activity.type == ActivityType.custom_session
                                ? controller.bookCustomSession(
                                    activityId: activity.id,
                                    minPersons: activity.minPersonsNumber,
                                    maxPersons: activity.maxPersonsNumber,
                                  )
                                : controller.bookCustomSessionForAnotherType(
                                    activityId: activity.id,
                                    minPersons: activity.minPersonsNumber,
                                    maxPersons: activity.maxPersonsNumber,
                                  ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.add),
                            const Gap(8),
                            Text(LocaleKeys.add_session.tr()),
                          ],
                        ),
                      ),
                    ),
                  const Gap(24),
                  controller.relatedActivitiesRequest.listBuilder(
                    loader: (_) => HorizontalListLoading(
                        title: LocaleKeys.related_activities.tr()),
                    errorBuilder: (_, response) => HorizontalListError(
                        title: LocaleKeys.related_activities.tr(),
                        error: response.message),
                    builder: (context, activities) {
                      if (activities.isEmpty) {
                        return const SizedBox();
                      }
                      return ActivitiesList(
                        title: LocaleKeys.related_activities.tr(),
                        activities: activities,
                      );
                    },
                  ),
                  const Gap(24),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
