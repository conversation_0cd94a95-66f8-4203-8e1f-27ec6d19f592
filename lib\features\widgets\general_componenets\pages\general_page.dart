import 'package:flutter/material.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';

import '../app_bars/general_app_bar.dart';

class GeneralPage extends StatelessWidget {
  final String? titleText;
  final Widget? title;
  final Widget child;
  final bool withBackground;
  final List<Widget>? actions;
  final bool mustHasBack;
  final Widget? floatingActionButton;

  const GeneralPage({
    super.key,
    this.titleText,
    this.title,
    required this.withBackground,
    required this.child,
    this.actions,
    this.mustHasBack = false,
    this.floatingActionButton,
  }) : assert(title != null || titleText != null);

  @override
  Widget build(BuildContext context) {
    Widget body = child;
    if (withBackground) {
      body = Stack(
        children: [
          Positioned(child: Assets.images.background3.image()),
          body,
        ],
      );
    }
    return Scaffold(
      floatingActionButton: floatingActionButton,
      appBar: GeneralAppBar(
        title: title ?? Text(titleText!),
        actions: actions,
        mustHasBack: mustHasBack,
      ),
      body: body,
    );
  }
}
