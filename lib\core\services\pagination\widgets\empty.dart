import 'package:flutter/material.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';

class EmptyWidget extends StatelessWidget {
  const EmptyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(child: Assets.lotties.noData.lottie());
  }
}

class SliverEmptyWidget extends StatelessWidget {
  const SliverEmptyWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return SliverList(delegate: SliverChildListDelegate([const EmptyWidget()]));
  }
}
