{
	"obs var": {
		"prefix": "ObsVar",
		"body": [
			"ObsVar<$1> $2 = ObsVar($3);",
		],
		"description": "Obs Var"
	},
	"obs list": {
		"prefix": "ObsList",
		"body": [
			"ObsList<$1> $2 = ObsList([]);",
		],
		"description": "Obs List"
	},
	"app image": {
		"prefix": "AppImage",
		"body": [
			"AppImage(path: $1, type: ImageType.$2),",
		],
		"description": "image"
	},
	"Rx Var": {
		"prefix": "rxvar",
		"body": [
			"final Rx<$1> _$2 = $3.obs;",
			"$1 get $2 => _$2.value;",
			"set $2($1 value) => _$2.value = value;"
		],
		"description": "rx variable"
	},
	"Page": {
		"prefix": "page",
		"body": [
			"import 'package:flutter/material.dart';",
			"import 'package:get/get.dart';",
			"",
			"import 'controller.dart';",
			"",
			"class $1Page extends StatelessWidget {",
			"  const $1Page({super.key});",
			"",
			"  @override",
			"  Widget build(BuildContext context) {",
			"    // ignore: unused_local_variable",
			"    $1PageController controller = Get.put($1PageController());",
			"    return Scaffold();",
			"  }",
			"}",
			"",
		]
	},
	"Controller": {
		"prefix": "controller",
		"body": [
			"import 'package:get/get.dart';",
			"",
			"class $1PageController extends GetxController {",
			"",
			"  @override",
			"  void onInit() {",
			"    super.onInit();",
			"  }",
			"",
			"  @override",
			"  void onClose() {",
			"    super.onClose();",
			"  }",
			"}",
			"",
		]
	},
	"Page Route": {
		"prefix": "pageRoute",
		"body": [
			"GetPage(name: Pages.$1.value, page: () => $2Page()),"
		]
	},
	"widget": {
		"prefix": "widget",
		"body": [
			"import 'package:flutter/material.dart';",
			"",
			"class $1 extends StatelessWidget{",
			"  const $1({super.key});",
			"",
			"  @override",
			"  Widget build(BuildContext context) {",
			"    return SizedBox();",
			"  }",
			"",
			"}",
		]
	},
	"Repo": {
		"prefix": "repo",
		"body": [
			"import 'package:graphql_flutter/graphql_flutter.dart';",
			"",
			"class $1Repo {",
			"  static Future<QueryResult> request(){",
			"    ",
			"  }",
			"}",
			"",
		]
	},
	"height":{
		"prefix":"height",
		"body":[
            "const SizedBox(height:$1 ),"
		]

	},
	"width":{
		"prefix":"width",
		"body":[
            "const SizedBox(width:$1 ),"
		]

	},
	"strings":{
		"prefix":"string",
		"body":["static const String $1 = \"$1\";"]
	},
}