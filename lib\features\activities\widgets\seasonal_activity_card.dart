import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/models/activity/main_activity.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/features/activities/main_activity/models/nav.dart';
import 'package:gopal/features/widgets/provider_image.dart';

class SeasonalActivityCard extends StatelessWidget {
  final MainActivity activity;
  const SeasonalActivityCard({super.key, required this.activity});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Nav.to(
        Pages.main_activity,
        arguments: MainActivityPageNav(id: activity.id),
        preventDuplicates: false,
      ),
      child: SizedBox(
        height: Get.height * .3,
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            AspectRatio(
              aspectRatio: 9 / 16,
              child: Container(
                height: double.infinity,
                width: double.infinity,
                margin: const EdgeInsets.only(top: 20),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: StyleRepo.elevation,
                ),
                child: AppImage(
                  path: activity.seasonCover?.large ?? activity.cover.large,
                  type: ImageType.CachedNetwork,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10),
                  ),
                ),
              ),
            ),
            CenterImage(
              image: activity.center.profileImage.small,
              isTrusted: activity.center.isTrusted,
              radius: 40,
            ),
          ],
        ),
      ),
    );
  }
}
