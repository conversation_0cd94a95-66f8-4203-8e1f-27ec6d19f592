import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/models/activity/main_activity.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/services/pagination/controller.dart';
import 'package:gopal/core/utils/action_stack.dart';

import '../../../core/services/rest_api/rest_api.dart';
import '../../filters/controller.dart';
import '../../filters/models/categories.dart/main_category.dart';
import '../../filters/models/filters_data.dart';
import 'models/nav.dart';

class CategoryPageController extends GetxController {
  final CategoryPageNav nav;
  late FiltersData filters;

  FiltersPageController get filtersController =>
      Get.find<FiltersPageController>();

  PaginationController<MainActivity> get resultPager =>
      Get.find<PaginationController<MainActivity>>(
          tag: ControllersTags.filters_results_pager);

  CategoryPageController(this.nav) {
    _selectedCategory = (nav.category?.name ?? "").obs;
    filters = FiltersData();
    if (nav.category != null) {
      filters.category =
          CategoryFilter(id: nav.category!.id, name: nav.category!.name);
    }
    debouncer = ActionStack(execute: (_) => resultPager.refreshData());
  }

  late final Rx<String> _selectedCategory;
  String get selectedCategory => _selectedCategory.value;
  set selectedCategory(String value) => _selectedCategory.value = value;

  final Rx<bool> _isSubcategoriesExpanded = false.obs;
  bool get isSubcategoriesExpanded => _isSubcategoriesExpanded.value;
  set isSubcategoriesExpanded(bool value) =>
      _isSubcategoriesExpanded.value = value;

  openFilters() async {
    FiltersData? data = await Nav.to(Pages.filters);
    if (data != null) {
      filters = data;
      selectedCategory = data.category?.name ?? "";

      Get.find<PaginationController<MainActivity>>(
              tag: ControllersTags.filters_results_pager)
          .refreshData();
    }
  }

  late ActionStack debouncer;

  filterOnSubcategory(CategoryFilter subcategory, bool value) {
    if (value) {
      filtersController.subcategoriesController.addSelectedOption(
        filtersController.subcategoriesController.options
            .firstWhere((element) => element.value?.id == subcategory.id),
      );
      if (filters.subcategories == null) {
        filters.subcategories = [subcategory];
      } else {
        filters.subcategories?.add(subcategory);
      }
    } else {
      filtersController.subcategoriesController.clearSelection(
        filtersController.subcategoriesController.options
            .firstWhere((element) => element.value?.id == subcategory.id),
      );
      filters.subcategories?.remove(subcategory);
      if (filters.subcategories?.isEmpty ?? false) {
        filters.subcategories = null;
      }
    }
    debouncer.add(filters.subcategories);
  }

  Future<ResponseModel> fetchData(int page, CancelToken cancel) async {
    return await Request(
      endPoint: EndPoints.main_activities,
      params: filters.toParams()..['page'] = page,
      cancelToken: cancel,
    ).perform();
  }
}
