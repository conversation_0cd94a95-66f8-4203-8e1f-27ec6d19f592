import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/chat/chat_room.dart';
import 'package:gopal/core/services/pagination/options/list_view.dart';
import 'package:gopal/features/chat/rooms/widgets/loading.dart';
import 'package:gopal/features/widgets/general_componenets/pages/rounded_page.dart';

import 'controller.dart';
import 'widgets/chat_card.dart';

class ChatRoomsPage extends StatelessWidget {
  const ChatRoomsPage({super.key});

  @override
  Widget build(BuildContext context) {
    ChatRoomsPageController controller = Get.put(ChatRoomsPageController());
    return RoundedPage(
      title: Text(LocaleKeys.messages.tr()),
      child: ListViewPagination<ChatRoom>.separated(
        tag: ControllersTags.chat_rooms_pager,
        closeToListEnd: 99999,
        fetchApi: controller.fetchRooms,
        fromJson: ChatRoom.fromJson,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        separatorBuilder: (_, __) => const Divider(),
        initialLoading: const ChatRoomsLoading(),
        itemBuilder: (context, index, chatRoom) => ChatCard(chatRoom: chatRoom),
      ),
    );
  }
}
