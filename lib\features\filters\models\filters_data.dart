import 'package:flutter/material.dart';
import 'package:gopal/core/models/constants/gender.dart';
import 'package:gopal/core/models/selection.dart';

import 'categories.dart/main_category.dart';
import 'price_type.dart';

class FiltersData {
  CategoryFilter? category;
  List<CategoryFilter>? subcategories;
  RangeValues? priceRange;
  PriceType? priceType;
  int? rating;
  int? minAge;
  int? maxAge;
  Selection? specialNeed;
  Gender? gender;

  FiltersData({
    this.category,
    this.subcategories,
    this.priceType,
    this.priceRange,
    this.rating,
    this.minAge,
    this.maxAge,
    this.specialNeed,
    this.gender,
  });

  Map<String, dynamic> toParams() {
    Map<String, dynamic> params = {
      if (category != null) "category_parent_id": category!.id,
      if (minAge != null) "min_age": minAge,
      if (maxAge != null) "max_age": maxAge,
      if (priceType != null && priceType != PriceType.any)
        "price_type": priceType!.value,
      if (rating != null) "rate": rating,
      if (priceRange != null) "min_price": priceRange!.start.toInt(),
      if (priceRange != null) "max_price": priceRange!.end.toInt(),
      if (gender != null) "gender": gender!.name.toUpperCase(),
      if (specialNeed != null) "special_case": specialNeed!.id,
    };
    if (subcategories != null && subcategories!.isNotEmpty) {
      for (int i = 0; i < subcategories!.length; i++) {
        params["children_categories[$i]"] = subcategories![i].id;
      }
    }
    return params;
  }
}
