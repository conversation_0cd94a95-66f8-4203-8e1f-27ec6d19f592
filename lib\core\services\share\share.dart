import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

import 'deep_link.dart';

class ShareHelper {
  static share(String text) async {
    final box = Get.context?.findRenderObject() as RenderBox?;
    await SharePlus.instance.share(
      ShareParams(
        text: text,
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
      ),
    );
  }

  static shareActivity(int id) {
    share(DeepLinks.subactivityLink(id));
  }

  static shareCenter(int id) {
    share(DeepLinks.centerLink(id));
  }

  static FutureOr<void> shareOnFacebook(String text) async {
    final Uri launchUri = Uri.parse(
      "https://www.facebook.com/sharer/sharer.php?u=$text",
    );
    log(launchUri.toString());
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri, mode: LaunchMode.externalApplication);
    } else {
      log('Could not launch $launchUri');
    }
  }

  static FutureOr<void> shareOnTwitter(String text) async {
    final Uri launchUri = Uri.parse(
      "https://twitter.com/intent/tweet?text=$text",
    );
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri, mode: LaunchMode.externalApplication);
    } else {
      log('Could not launch $launchUri');
    }
  }

  static FutureOr<void> shareOnWhatsApp(String text) async {
    Clipboard.setData(ClipboardData(text: text));
    final Uri launchUri = Uri.parse("https://wa.me/?text=$text");
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri, mode: LaunchMode.externalApplication);
    } else {
      log('Could not launch $launchUri');
    }
  }
}
