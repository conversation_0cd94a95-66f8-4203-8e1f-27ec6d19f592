import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:readmore/readmore.dart';

class AboutActivity extends StatelessWidget {
  final String description;
  const AboutActivity({super.key, required this.description});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleKeys.about_activity.tr(),
            style: context.textTheme.bodyLarge!.copyWith(
              color: StyleRepo.blueViolet,
              fontWeight: FontWeight.w600,
              decoration: TextDecoration.underline,
            ),
          ),
          const Gap(12),
          ReadMoreText(
            description,
            style: TextStyle(color: StyleRepo.grey.shade700),
            moreStyle: context.textTheme.bodyMedium!.copyWith(
              color: StyleRepo.turquoise.withValues(alpha: .5),
              fontWeight: FontWeight.w600,
            ),
            lessStyle: context.textTheme.bodyMedium!.copyWith(
              color: StyleRepo.turquoise.withValues(alpha: .5),
              fontWeight: FontWeight.w600,
            ),
            trimMode: TrimMode.Line,
            trimLines: 3,
            trimCollapsedText: " ${LocaleKeys.read_more.tr()}",
            trimExpandedText: " ${LocaleKeys.read_less.tr()}",
          ),
        ],
      ),
    );
  }
}
