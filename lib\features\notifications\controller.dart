import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:gopal/core/models/notifications/data/main_notification_data.dart';
import 'package:gopal/core/models/notifications/notification.dart';
import 'package:gopal/core/models/notifications/type.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/services/notifications_counter/counter.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/usecases/toast/toast.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../core/services/rest_api/rest_api.dart';

class NotificationsPageController extends GetxController {
  Future<ResponseModel> fetchData(int page, CancelToken cancel) async {
    ResponseModel response = await Request(
      endPoint: EndPoints.notifications,
      params: {"page": page},
      cancelToken: cancel,
    ).perform();
    if (page == 1 && response.success) {
      NotificationsCounter.notificationsCount = 0;
    }
    return response;
  }

  notificationClick(NotificationModel notification) async {
    if (notification.type == NotificationType.answer_question) {
      return Nav.to(Pages.support_replies);
    }
    MainNotificationData? data = notification.data;
    if (data is ActivityNotificationData) {
      return Nav.to(Pages.activity_details, arguments: data.activityId);
    } else if (data is PaymentSuccessNotificationData) {
      return launchUrlString(data.link);
    } else if (data is BookAcceptedNotificationData) {
      BookAcceptedNotificationData data = notification.data!.asBookAcceptedData;
      if (data.bookId != null) {
        Loading.show();
        ResponseModel response = await Request(
          endPoint: EndPoints.checkout_payment,
          method: RequestMethod.Post,
          body: {"book_transaction_id": data.bookId},
        ).perform();
        Loading.dispose();
        if (response.success) {
          if (response.data[0]['redirect_url'] != null) {
            launchUrl(
              Uri.parse(response.data[0]['redirect_url']),
              mode: LaunchMode.externalApplication,
            );
          }
        } else {
          Toast.show(message: response.message);
        }
      }
    }
  }
}
