import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/activity/constants/type.dart';
import 'package:gopal/features/activities/widgets/periodic_time_info.dart';
import 'package:gopal/features/activities/widgets/sets.dart';

import '../controller.dart';
import '../../models/sub_activity_data.dart';

class CardBody extends GetView<MainActivityPageController> {
  final SubActivityData activity;
  const CardBody({super.key, required this.activity});

  @override
  Widget build(BuildContext context) {
    if (activity.type == ActivityType.periodic_days) {
      return PeriodicTimeInfo(periodic: activity.periodic!);
    }
    if (activity.type == ActivityType.specific_days) {
      return Row(
        children: [
          Text(
            "${tr(LocaleKeys.sessions_number)}: ${activity.specificDaySessionsCount}",
            style: context.textTheme.titleMedium!.copyWith(
              color: Theme.of(context).primaryColor,
              decoration: TextDecoration.underline,
            ),
          ),
        ],
      );
    }
    if (activity.type == ActivityType.numbered_session) {
      return SetsWidget(
        sets: activity.sets!,
        onBook: !activity.isAvailable
            ? null
            : (setId) =>
                controller.bookASet(activityId: activity.id, setId: setId),
      );
    }
    return const SizedBox();
  }
}
