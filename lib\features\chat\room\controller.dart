import 'dart:developer';
import 'dart:typed_data';

import 'package:audio_waveforms/audio_waveforms.dart';
import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide FormData, Trans;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/models/chat/chat_room.dart';
import 'package:gopal/core/models/constants/recording_status.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/services/pagination/controller.dart';
import 'package:gopal/core/services/player/player.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';
import 'package:gopal/core/utils/file_utils.dart';
import 'package:gopal/features/usecases/toast/status.dart';
import 'package:gopal/features/usecases/toast/toast.dart';
import 'package:image_picker/image_picker.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:wechat_assets_picker/wechat_assets_picker.dart'
    as assets_picker;

import '../../../core/models/chat/messages/messages.dart';
import '../../../core/services/rest_api/rest_api.dart';
import '../rooms/controller.dart';
import 'widgets/message_types_popup.dart';
import 'package:gopal/core/localization/strings.dart';

const _kMaxFileSize = 25;

class ChatRoomPageController extends GetxController {
  final ChatRoom chatRoom;
  ChatRoomPageController(this.chatRoom);

  PaginationController<Message> get paginationController =>
      Get.find<PaginationController<Message>>(
        tag: ControllersTags.messages_pager,
      );

  ObsList<Message> get messages => paginationController.data;

  Future<ResponseModel> fetchMessages(int page, CancelToken cancel) async {
    return await Request(
      endPoint: EndPoints.messages,
      params: {"page": page, "room_id": chatRoom.id},
      cancelToken: cancel,
    ).perform();
  }

  //SECTION - Sending message

  bool get isEmptyMessage {
    return textController.text.isEmpty &&
        recordFilePath == null &&
        images == null &&
        document == null &&
        video == null &&
        location == null;
  }

  sendMessage() async {
    if (isEmptyMessage) return;
    SendMessage message = getSendMessage();
    messages.insert(0, message);
    _clearFields();
    FormData form = await message.toFormData();
    form.fields.add(MapEntry("room_id", chatRoom.id.toString()));
    ResponseModel response =
        await Request(
          endPoint: EndPoints.messages,
          method: RequestMethod.Post,
          body: form,
        ).perform();
    if (response.success) {
      ReceivedMessage receivedMessage = ReceivedMessage.fromJson(response.data);
      messages.replace(message, receivedMessage);
      try {
        Get.find<ChatRoomsPageController>().reorderRooms(
          roomId: chatRoom.id,
          preview: response.data['preview'],
        );
      } catch (_) {}
    } else {
      message.status = MessageStatus.error;
    }
  }

  SendMessage getSendMessage() {
    if (recordFilePath != null) {
      return SendMessage(
        type: MessageType.voice,
        date: DateTime.now(),
        content: SendVoiceMessageContent(
          voiceFile: recordFilePath!,
          waves: recordWaves,
        ),
      );
    }
    if (images != null) {
      return SendMessage(
        type: MessageType.media,
        date: DateTime.now(),
        content: SendImagesMessageContent(
          images: List.generate(images!.length, (index) => images![index]),
          content: imagesContent,
        ),
      );
    }
    if (video != null) {
      return SendMessage(
        type: MessageType.media,
        date: DateTime.now(),
        content: SendVideoMessageContent(
          video: video!,
          content: videoContent,
          thumb: thumb,
        ),
      );
    }
    if (document != null) {
      return SendMessage(
        type: MessageType.media,
        date: DateTime.now(),
        content: SendDocumentMessageContent(
          documentFile: document!,
          fileName: documentName!,
          content: documentContent,
        ),
      );
    }
    if (location != null) {
      return SendMessage(
        type: MessageType.location,
        date: DateTime.now(),
        content: SendLocationMessageContent(
          location: location!,
          mapImage: mapImage!,
        ),
      );
    }
    return SendMessage(
      type: MessageType.text,
      date: DateTime.now(),
      content: SendTextMessageContent(text: textController.text),
    );
  }

  _clearFields() {
    textController.clear();
    disposeRecorderPlayer();
    clearImages();
    clearDocument();
    clearvideo();
    recordEnabled = true;
    canOpenMessagesTypesPopup = true;
  }

  resendMessage(SendMessage message) async {
    message.status = MessageStatus.waiting;
    FormData form = await message.toFormData();
    form.fields.add(MapEntry("room_id", chatRoom.id.toString()));
    ResponseModel response =
        await Request(
          endPoint: EndPoints.messages,
          method: RequestMethod.Post,
          body: form,
        ).perform();
    if (response.success) {
      ReceivedMessage receivedMessage = ReceivedMessage.fromJson(response.data);
      messages.replace(message, receivedMessage);
    } else {
      message.status = MessageStatus.error;
    }
  }

  //SECTION - Send Text message
  late TextEditingController textController;
  //!SECTION

  //SECTION - Messages Types popup

  late final Rx<bool> _canOpenMessagesTypesPopup;
  bool get canOpenMessagesTypesPopup => _canOpenMessagesTypesPopup.value;
  set canOpenMessagesTypesPopup(bool value) =>
      _canOpenMessagesTypesPopup.value = value;

  OverlayEntry? overlayEntry;

  openMessagesTypesPopup(TapUpDetails details, BuildContext context) {
    if (overlayEntry != null) {
      closeMessageTypesPopup();
    }
    overlayEntry = OverlayEntry(
      builder: (context) => MessageTypesPopup(dy: details.globalPosition.dy),
    );

    Overlay.of(context).insert(overlayEntry!);
  }

  closeMessageTypesPopup() {
    overlayEntry?.remove();
    overlayEntry?.dispose();
    overlayEntry = null;
  }
  //!SECTION

  //SECTION - Send Image message
  List<String>? images;
  String? imagesContent;

  clearImages() {
    images = null;
    imagesContent = null;
  }

  pickImageFromCamera() async {
    closeMessageTypesPopup();

    var pickedImage = await ImagePicker().pickImage(source: ImageSource.camera);

    if (pickedImage == null) return;

    (List<String> images, String text)? message = await Nav.to(
      Pages.images_preview,
      arguments: [pickedImage.path],
    );
    if (message == null) return;

    images = message.$1;
    imagesContent = message.$2;

    sendMessage();
  }

  pickImages() async {
    closeMessageTypesPopup();
    final List<assets_picker.AssetEntity>? result = await assets_picker
        .AssetPicker.pickAssets(
      Get.context!,
      pickerConfig: assets_picker.AssetPickerConfig(
        maxAssets: 10,
        pickerTheme: Theme.of(Get.context!),
        requestType: assets_picker.RequestType.image,
      ),
    );
    if (result == null) return;

    List<String> images = [];
    for (var image in result) {
      images.add((await image.file)?.path ?? "");
    }
    (List<String> images, String text)? message = await Nav.to(
      Pages.images_preview,
      arguments: images,
    );
    if (message == null) return;

    this.images = message.$1;
    imagesContent = message.$2;

    sendMessage();
  }

  //!SECTION

  //SECTION - Send Video message
  String? video;
  Uint8List? thumb;
  String? videoContent;

  clearvideo() {
    video = null;
    thumb = null;
    videoContent = null;
  }

  pickVideo() async {
    closeMessageTypesPopup();
    final List<assets_picker.AssetEntity>? result = await assets_picker
        .AssetPicker.pickAssets(
      Get.context!,
      pickerConfig: assets_picker.AssetPickerConfig(
        maxAssets: 1,
        pickerTheme: Theme.of(Get.context!),
        requestType: assets_picker.RequestType.video,
      ),
    );
    if (result == null) return;

    int fileSize = (await (await result.first.file)!.length());
    if (fileSize > FileUtils.mb$to$b(_kMaxFileSize)) {
      Toast.show(
        message: LocaleKeys.file_must_be_smaller_than.tr(
          args: ["${_kMaxFileSize}Mb"],
        ),
        status: ToastStatus.warning,
      );
      return;
    }

    video = (await result.first.file)!.path;

    (Uint8List thumb, String text)? message = await Nav.to(
      Pages.video_preview,
      arguments: video,
    );
    if (message == null) return;

    thumb = message.$1;
    imagesContent = message.$2;

    sendMessage();
  }

  //!SECTION

  //SECTION - Send Document Message
  String? document;
  String? documentName;
  String? documentContent;

  clearDocument() {
    document = null;
    documentName = null;
    documentContent = null;
  }

  pickDocument() async {
    closeMessageTypesPopup();

    FilePickerResult? pickedFile = await FilePicker.platform.pickFiles();
    if (pickedFile == null) return;

    int fileSize = pickedFile.files.first.size;
    if (fileSize > FileUtils.mb$to$b(_kMaxFileSize)) {
      Toast.show(
        message: LocaleKeys.file_must_be_smaller_than.tr(
          args: ["${_kMaxFileSize}Mb"],
        ),
        status: ToastStatus.warning,
      );
      return;
    }

    (String, String)? result = await Nav.to(
      Pages.document_preview,
      arguments: pickedFile.paths.first,
    );
    if (result == null) return;

    document = result.$1;
    documentContent = result.$2;
    documentName = pickedFile.files.first.name;

    sendMessage();
  }
  //!SECTION

  //SECTION - Send Location Message
  LatLng? location;
  Uint8List? mapImage;

  clearLocation() {
    location = null;
    mapImage = null;
  }

  pickLocation() async {
    closeMessageTypesPopup();

    (LatLng location, Uint8List mapImage)? result = await Nav.to(
      Pages.pick_location,
    );

    if (result == null) return;

    location = result.$1;
    mapImage = result.$2;

    sendMessage();
  }
  //!SECTION

  //SECTION - Send Voice Record Message

  //SECTION - Recoding
  String? recordFilePath;
  List<double> recordWaves = [];

  final Rx<bool> _recordEnabled = true.obs;
  bool get recordEnabled => _recordEnabled.value;
  set recordEnabled(bool value) => _recordEnabled.value = value;

  final Rx<RecordingStatus> _recordingStatus = RecordingStatus.get_started.obs;
  RecordingStatus get recordingStatus => _recordingStatus.value;
  set recordingStatus(RecordingStatus value) => _recordingStatus.value = value;

  late RecorderController recordController;

  void _initialiseControllers() {
    recordController =
        RecorderController()
          ..androidEncoder = AndroidEncoder.aac
          ..androidOutputFormat = AndroidOutputFormat.mpeg4
          ..iosEncoder = IosEncoder.kAudioFormatMPEG4AAC
          ..sampleRate = 44100;
  }

  startRecording() async {
    log("press down");
    PermissionStatus micPermission = await Permission.microphone.status;
    log("$micPermission");
    if (!micPermission.isGranted) {
      PermissionStatus micPermission = await Permission.microphone.request();
      log("$micPermission");
      return;
    }
    if (!micPermission.isGranted) {
      Toast.show(
        status: ToastStatus.warning,
        message:
            LocaleKeys.you_should_enable_mic_permission_in_app_settings.tr(),
      );
      return false;
    }
    if (!recordController.hasPermission) {
      await recordController.checkPermission();
    }
    textController.clear();
    recordController.reset();
    recordWaves.clear();
    recordingStatus = RecordingStatus.recording;
    await recordController.record();
  }

  stopRecording() async {
    log("press up");
    if (recordController.elapsedDuration < 2.seconds) {
      await recordController.stop();
      recordingStatus = RecordingStatus.get_started;
      recordEnabled = true;
      Toast.show(message: "Short voice message", status: ToastStatus.warning);
      return;
    }
    recordWaves = downsampleList(recordController.waveData, 50);
    recordFilePath = await recordController.stop();
    recordingStatus = RecordingStatus.finished;
    log(recordWaves.toString());
    recordEnabled = false;
    initReordedVoicePlayer();
  }

  List<double> downsampleList(List<double> waves, int maxLength) {
    if (waves.length <= maxLength) {
      return List.from(waves);
    }

    List<double> downsampled = [];
    double step = waves.length / maxLength;

    for (int i = 0; i < maxLength; i++) {
      int start = (i * step).toInt();
      int end = ((i + 1) * step).toInt();

      if (end >= waves.length) {
        end = waves.length - 1;
      }

      double sum = 0.0;
      for (int j = start; j <= end; j++) {
        sum += waves[j];
      }

      downsampled.add(sum / (end - start + 1));
    }

    return downsampled;
  }
  //!SECTION

  //SECTION - Recorded Voice Player
  late PlayerController recordedVoicePlayer = PlayerController();

  final Rx<bool> _isRecordedPlayerInit = false.obs;
  bool get isRecordedPlayerInit => _isRecordedPlayerInit.value;
  set isRecordedPlayerInit(bool value) => _isRecordedPlayerInit.value = value;

  initReordedVoicePlayer() async {
    recordedVoicePlayer = PlayerController();
    await recordedVoicePlayer.preparePlayer(
      path: recordFilePath!,
      shouldExtractWaveform: false,
    );
    recordedVoicePlayer.setFinishMode(finishMode: FinishMode.pause);
    isRecordedPlayerInit = true;
  }

  playeRecordedVoice() async {
    if (!isRecordedPlayerInit) {
      await initReordedVoicePlayer();
    }
    if (recordedVoicePlayer.playerState == PlayerState.stopped) {
      await recordedVoicePlayer.seekTo(0);
    }
    recordedVoicePlayer.startPlayer();
  }

  pauseRecordedVoice() {
    recordedVoicePlayer.pausePlayer();
  }

  disposeRecorderPlayer() {
    recordedVoicePlayer.dispose();
    recordingStatus = RecordingStatus.get_started;
    isRecordedPlayerInit = false;
    recordEnabled = true;
    recordFilePath = null;
    recordWaves = [];
  }
  //!SECTION

  //!SECTION
  //!SECTION

  //SECTION - Audio Player
  final Rx<int> _playingVoiceMessage = 0.obs;
  int get playingVoiceMessage => _playingVoiceMessage.value;
  set playingVoiceMessage(int value) => _playingVoiceMessage.value = value;

  playVoice(ReceivedMessage message) {
    AudioPlayer().pause();
    playingVoiceMessage = message.id;
    AudioPlayer().init(message.content.asVoice.filePath);
  }
  //!SECTION

  @override
  void onInit() {
    _initialiseControllers();
    textController = TextEditingController();
    _canOpenMessagesTypesPopup = textController.text.isEmpty.obs;
    super.onInit();
  }

  @override
  void onClose() {
    recordController.dispose();
    textController.dispose();
    disposeRecorderPlayer();
    AudioPlayer().dispose();
    super.onClose();
  }

  //SECTION - Receiving messages
  receiveMessage(ReceivedMessage message) {
    messages.insert(0, message);
  }

  //!SECTION
}
