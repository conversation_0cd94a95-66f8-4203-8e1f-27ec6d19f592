{"buildFiles": ["C:\\src\\versions\\3.29.3\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\work\\ixCoders\\current\\go_pal\\go_pal_flutter\\android\\app\\.cxx\\RelWithDebInfo\\6z4g5e4l\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "D:\\work\\ixCoders\\current\\go_pal\\go_pal_flutter\\android\\app\\.cxx\\RelWithDebInfo\\6z4g5e4l\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}