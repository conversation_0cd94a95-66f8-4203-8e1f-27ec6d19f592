enum PaymentStatus {
  unpaid,
  paid,
  hold,
  ;

  static PaymentStatus parse(String s) {
    s = s.toLowerCase();
    return switch (s) {
      "unpaid" => unpaid,
      "failed" => unpaid,
      "declined" => unpaid,
      "hold" => hold,
      "paid" => paid,
      _ => throw "New Payment Status",
    };
  }

  static PaymentStatus? tryParse(String? s) {
    if (s == null) return null;
    try {
      return parse(s);
    } catch (_) {
      return null;
    }
  }
}
