import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/style/repo.dart';

class DrawerCard extends StatelessWidget {
  final Widget icon;
  final String title;
  final bool isRed;
  final Widget? trailing;
  final void Function()? onTap;
  const DrawerCard({
    super.key,
    required this.icon,
    required this.title,
    this.isRed = false,
    this.trailing,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Get.back();
        onTap?.call();
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: isRed ? StyleRepo.red.shade100 : StyleRepo.grey.shade200,
                shape: BoxShape.circle,
                border: isRed ? Border.all(color: StyleRepo.red) : null,
              ),
              child: IconTheme(
                data: IconThemeData(
                  color: isRed ? StyleRepo.red : StyleRepo.grey.shade600,
                ),
                child: icon,
              ),
            ),
            const Gap(12),
            Expanded(
              child: Text(
                title,
                style: context.textTheme.bodyLarge!.copyWith(
                    fontWeight: FontWeight.w600,
                    color: isRed ? StyleRepo.red : null),
              ),
            ),
            if (trailing != null) trailing!,
          ],
        ),
      ),
    );
  }
}
