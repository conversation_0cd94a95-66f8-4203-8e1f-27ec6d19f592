import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/services/pagination/options/list_view.dart';

import '../../../books/widgets/book_card.dart';
import '../controller.dart';
import '../models/completed_book.dart';
import 'loading.dart';

class MyCompletedActivities extends GetView<MyActivitiesPageController> {
  const MyCompletedActivities({super.key});

  @override
  Widget build(BuildContext context) {
    return ListViewPagination.separated(
      tag: ControllersTags.my_completed_activities,
      fetchApi: controller.fetchCompleted,
      fromJson: CompletedBook.fromJson,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      separatorBuilder: (_, __) => const Gap(12),
      initialLoading: const BooksLoading(),
      itemBuilder: (context, index, book) {
        return BookCard(bookedActivity: book);
      },
    );
  }
}
