import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';

import '../controller.dart';
import '../models/price_type.dart';

class PriceTypeFilter extends GetView<FiltersPageController> {
  const PriceTypeFilter({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Gap(24),
        Text(
          LocaleKeys.price_type.tr(),
          style: context.textTheme.titleMedium!.copyWith(
            fontWeight: FontWeight.w700,
          ),
        ),
        const Gap(6),
        Wrap(
          children: List.generate(
            PriceType.values.length,
            (index) => InkWell(
              onTap: () =>
                  controller.selectedPriceType = PriceType.values[index],
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Obx(
                    () => Radio<PriceType>(
                      value: PriceType.values[index],
                      groupValue: controller.selectedPriceType,
                      onChanged: (priceType) => priceType != null
                          ? controller.selectedPriceType = priceType
                          : null,
                    ),
                  ),
                  Obx(
                    () => Text(
                      PriceType.values[index].text,
                      style: TextStyle(
                        fontWeight: controller.selectedPriceType ==
                                PriceType.values[index]
                            ? FontWeight.bold
                            : null,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
