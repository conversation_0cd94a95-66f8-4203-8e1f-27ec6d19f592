import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/services/pagination/options/list_view.dart';
import 'package:gopal/features/settings/usecases/support/widgets/support_fab.dart';
import 'package:gopal/features/widgets/general_componenets/pages/rounded_page.dart';

import 'controller.dart';
import 'models/suppoert_reply.dart';
import 'widgets/question_card.dart';

class SupportRepliesPage extends StatelessWidget {
  const SupportRepliesPage({super.key});

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    SupportRepliesPageController controller =
        Get.put(SupportRepliesPageController());
    return RoundedPage(
      title: Text(LocaleKeys.support_replies.tr()),
      floatingActionButton: const SupportFab(page: Pages.support_replies),
      child: ListViewPagination.separated(
        tag: ControllersTags.support_replies_pager,
        fetchApi: controller.fetchData,
        fromJson: SupportReplyModel.fromJson,
        padding: const EdgeInsets.fromLTRB(16, 16, 16, 100),
        separatorBuilder: (_, __) => const Gap(16),
        itemBuilder: (context, index, reply) => QuestionCard(reply: reply),
      ),
    );
  }
}
