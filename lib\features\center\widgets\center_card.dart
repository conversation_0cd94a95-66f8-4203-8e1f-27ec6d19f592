import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/models/user/center.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/utils/num_utils.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/features/center/profile/models/nav.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

class CenterCard extends StatelessWidget {
  final CenterModel center;
  const CenterCard({super.key, required this.center});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => Nav.to(
        Pages.center,
        arguments: CenterProfilePageNav(center.id),
        preventDuplicates: false,
      ),
      child: Container(
        width: Get.width * .7,
        decoration: BoxDecoration(
          color: StyleRepo.white,
          borderRadius: BorderRadius.circular(18),
          boxShadow: StyleRepo.elevation,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            AspectRatio(
              aspectRatio: 16 / 9,
              child: AppImage(
                path: center.cover?.large ?? "",
                type: ImageType.CachedNetwork,
                margin: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(18),
                ),
                height: double.infinity,
                width: double.infinity,
              ),
            ),
            const Gap(8),
            Row(
              children: [
                const Gap(12),
                Expanded(
                  child: Text(
                    center.name,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: context.textTheme.bodyLarge!
                        .copyWith(fontWeight: FontWeight.w600),
                  ),
                ),
                const Gap(8),
                Row(
                  children: [
                    SvgIcon(
                      Assets.icons.star.path,
                      color: StyleRepo.yellow.shade600,
                      size: 17,
                    ),
                    const Gap(4),
                    Text(
                      center.rate.toStringAsFixed(1),
                      style: context.textTheme.labelMedium,
                    )
                  ],
                ),
                const Gap(12),
              ],
            ),
            const Gap(8),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: Text(
                center.description,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: context.textTheme.bodySmall!
                    .copyWith(color: StyleRepo.grey),
              ),
            ),
            const Gap(10),
            if (center.distance != null)
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: RichText(
                  text: TextSpan(
                    style: context.textTheme.labelMedium!.copyWith(
                      color: StyleRepo.turquoise,
                    ),
                    children: [
                      WidgetSpan(
                        child: SvgIcon(Assets.icons.location.path,
                            color: StyleRepo.turquoise),
                      ),
                      const TextSpan(text: " "),
                      TextSpan(
                        text: center.distance!.approximateDistance,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ],
                  ),
                ),
              ),
            const Gap(10),
          ],
        ),
      ),
    );
  }
}
