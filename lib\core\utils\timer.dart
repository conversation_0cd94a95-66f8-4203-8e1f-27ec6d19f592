import 'dart:async';

import 'package:get/get.dart';

mixin ResendCodeTimer {
  late Timer _timer;

  final Rx<int> _secondsRemaining = 0.obs;
  int get secondsRemaining => _secondsRemaining.value;
  set secondsRemaining(int value) => _secondsRemaining.value = value;

  void startTimer(int seconds) {
    if (secondsRemaining > 0) {
      cancelTimer();
    }
    secondsRemaining = seconds;
    _timer = Timer.periodic(
      const Duration(seconds: 1),
      (timer) {
        secondsRemaining--;
        if (secondsRemaining == 0) {
          cancelTimer();
        }
      },
    );
  }

  void cancelTimer() {
    _timer.cancel();
    secondsRemaining = 0;
  }
}
