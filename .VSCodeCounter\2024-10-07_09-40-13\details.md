# Details

Date : 2024-10-07 09:40:13

Directory d:\\work\\ixCoders\\current\\go_pal\\go_pal_flutter\\lib

Total : 352 files,  22320 codes, 778 comments, 2219 blanks, all 25317 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [lib/core/config/app_builder.dart](/lib/core/config/app_builder.dart) | Dart | 145 | 20 | 34 | 199 |
| [lib/core/config/defaults.dart](/lib/core/config/defaults.dart) | Dart | 18 | 0 | 8 | 26 |
| [lib/core/config/role.dart](/lib/core/config/role.dart) | Dart | 72 | 4 | 15 | 91 |
| [lib/core/config/role_middleware.dart](/lib/core/config/role_middleware.dart) | Dart | 23 | 0 | 5 | 28 |
| [lib/core/constants/breakpoints.dart](/lib/core/constants/breakpoints.dart) | Dart | 3 | 0 | 1 | 4 |
| [lib/core/constants/controllers_tags.dart](/lib/core/constants/controllers_tags.dart) | Dart | 15 | 0 | 8 | 23 |
| [lib/core/constants/storages_names.dart](/lib/core/constants/storages_names.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/core/demo/data.dart](/lib/core/demo/data.dart) | Dart | 101 | 0 | 9 | 110 |
| [lib/core/demo/media.dart](/lib/core/demo/media.dart) | Dart | 24 | 9 | 5 | 38 |
| [lib/core/localization/codegen_loader.g.dart](/lib/core/localization/codegen_loader.g.dart) | Dart | 484 | 2 | 7 | 493 |
| [lib/core/localization/helper.dart](/lib/core/localization/helper.dart) | Dart | 9 | 1 | 3 | 13 |
| [lib/core/localization/localization.dart](/lib/core/localization/localization.dart) | Dart | 56 | 1 | 10 | 67 |
| [lib/core/localization/strings.dart](/lib/core/localization/strings.dart) | Dart | 206 | 1 | 3 | 210 |
| [lib/core/models/activity/activity.dart](/lib/core/models/activity/activity.dart) | Dart | 47 | 0 | 6 | 53 |
| [lib/core/models/activity/periodic/main_periodic.dart](/lib/core/models/activity/periodic/main_periodic.dart) | Dart | 81 | 0 | 15 | 96 |
| [lib/core/models/activity/periodic/periodic_type.dart](/lib/core/models/activity/periodic/periodic_type.dart) | Dart | 32 | 0 | 4 | 36 |
| [lib/core/models/activity/session.dart](/lib/core/models/activity/session.dart) | Dart | 24 | 0 | 4 | 28 |
| [lib/core/models/age_range.dart](/lib/core/models/age_range.dart) | Dart | 20 | 0 | 4 | 24 |
| [lib/core/models/book/book.dart](/lib/core/models/book/book.dart) | Dart | 75 | 1 | 15 | 91 |
| [lib/core/models/book/status.dart](/lib/core/models/book/status.dart) | Dart | 18 | 0 | 3 | 21 |
| [lib/core/models/category.dart](/lib/core/models/category.dart) | Dart | 24 | 0 | 4 | 28 |
| [lib/core/models/chat/chat_room.dart](/lib/core/models/chat/chat_room.dart) | Dart | 25 | 0 | 4 | 29 |
| [lib/core/models/chat/messages/message.dart](/lib/core/models/chat/messages/message.dart) | Dart | 23 | 0 | 6 | 29 |
| [lib/core/models/chat/messages/messages.dart](/lib/core/models/chat/messages/messages.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/core/models/chat/messages/received_message.dart](/lib/core/models/chat/messages/received_message.dart) | Dart | 84 | 1 | 16 | 101 |
| [lib/core/models/chat/messages/send_message.dart](/lib/core/models/chat/messages/send_message.dart) | Dart | 122 | 0 | 29 | 151 |
| [lib/core/models/chat/messages/types.dart](/lib/core/models/chat/messages/types.dart) | Dart | 37 | 0 | 8 | 45 |
| [lib/core/models/constants/discount.dart](/lib/core/models/constants/discount.dart) | Dart | 15 | 0 | 2 | 17 |
| [lib/core/models/constants/gender.dart](/lib/core/models/constants/gender.dart) | Dart | 49 | 0 | 7 | 56 |
| [lib/core/models/constants/recording_status.dart](/lib/core/models/constants/recording_status.dart) | Dart | 8 | 0 | 2 | 10 |
| [lib/core/models/image.dart](/lib/core/models/image.dart) | Dart | 52 | 0 | 6 | 58 |
| [lib/core/models/notifications/data/main_notification_data.dart](/lib/core/models/notifications/data/main_notification_data.dart) | Dart | 34 | 0 | 8 | 42 |
| [lib/core/models/notifications/notification.dart](/lib/core/models/notifications/notification.dart) | Dart | 35 | 0 | 4 | 39 |
| [lib/core/models/notifications/type.dart](/lib/core/models/notifications/type.dart) | Dart | 28 | 7 | 3 | 38 |
| [lib/core/models/rate.dart](/lib/core/models/rate.dart) | Dart | 9 | 0 | 2 | 11 |
| [lib/core/models/selection.dart](/lib/core/models/selection.dart) | Dart | 17 | 0 | 5 | 22 |
| [lib/core/models/user/center.dart](/lib/core/models/user/center.dart) | Dart | 49 | 0 | 5 | 54 |
| [lib/core/models/user/main_child.dart](/lib/core/models/user/main_child.dart) | Dart | 20 | 0 | 3 | 23 |
| [lib/core/models/user/main_user.dart](/lib/core/models/user/main_user.dart) | Dart | 41 | 0 | 5 | 46 |
| [lib/core/routes.dart](/lib/core/routes.dart) | Dart | 249 | 6 | 12 | 267 |
| [lib/core/services/deep_link.dart](/lib/core/services/deep_link.dart) | Dart | 0 | 83 | 9 | 92 |
| [lib/core/services/device_info.dart](/lib/core/services/device_info.dart) | Dart | 19 | 0 | 3 | 22 |
| [lib/core/services/feedback/controller.dart](/lib/core/services/feedback/controller.dart) | Dart | 49 | 0 | 11 | 60 |
| [lib/core/services/feedback/widgets/dialog.dart](/lib/core/services/feedback/widgets/dialog.dart) | Dart | 43 | 0 | 4 | 47 |
| [lib/core/services/feedback/widgets/feedback_button.dart](/lib/core/services/feedback/widgets/feedback_button.dart) | Dart | 30 | 0 | 4 | 34 |
| [lib/core/services/firebase_messaging/constants/firebase_options.dart](/lib/core/services/firebase_messaging/constants/firebase_options.dart) | Dart | 53 | 12 | 4 | 69 |
| [lib/core/services/firebase_messaging/constants/topics.dart](/lib/core/services/firebase_messaging/constants/topics.dart) | Dart | 0 | 0 | 2 | 2 |
| [lib/core/services/firebase_messaging/firebase_messaging.dart](/lib/core/services/firebase_messaging/firebase_messaging.dart) | Dart | 121 | 3 | 18 | 142 |
| [lib/core/services/firebase_messaging/notifications_actions.dart](/lib/core/services/firebase_messaging/notifications_actions.dart) | Dart | 42 | 1 | 4 | 47 |
| [lib/core/services/firebase_messaging/widgets/fcm_token.dart](/lib/core/services/firebase_messaging/widgets/fcm_token.dart) | Dart | 36 | 0 | 5 | 41 |
| [lib/core/services/local_notifications/local_notification.dart](/lib/core/services/local_notifications/local_notification.dart) | Dart | 94 | 2 | 11 | 107 |
| [lib/core/services/location.dart](/lib/core/services/location.dart) | Dart | 39 | 0 | 6 | 45 |
| [lib/core/services/notification_controller.dart](/lib/core/services/notification_controller.dart) | Dart | 0 | 0 | 2 | 2 |
| [lib/core/services/notifications_click/notification_click.dart](/lib/core/services/notifications_click/notification_click.dart) | Dart | 77 | 3 | 4 | 84 |
| [lib/core/services/notifications_counter/counter.dart](/lib/core/services/notifications_counter/counter.dart) | Dart | 48 | 0 | 8 | 56 |
| [lib/core/services/pagination/controller.dart](/lib/core/services/pagination/controller.dart) | Dart | 133 | 3 | 17 | 153 |
| [lib/core/services/pagination/options/grid_view.dart](/lib/core/services/pagination/options/grid_view.dart) | Dart | 195 | 1 | 8 | 204 |
| [lib/core/services/pagination/options/list_view.dart](/lib/core/services/pagination/options/list_view.dart) | Dart | 176 | 0 | 8 | 184 |
| [lib/core/services/pagination/options/pager.dart](/lib/core/services/pagination/options/pager.dart) | Dart | 93 | 16 | 19 | 128 |
| [lib/core/services/pagination/widgets/empty.dart](/lib/core/services/pagination/widgets/empty.dart) | Dart | 16 | 0 | 5 | 21 |
| [lib/core/services/pagination/widgets/initial_error.dart](/lib/core/services/pagination/widgets/initial_error.dart) | Dart | 82 | 0 | 5 | 87 |
| [lib/core/services/pagination/widgets/initial_loading.dart](/lib/core/services/pagination/widgets/initial_loading.dart) | Dart | 17 | 0 | 5 | 22 |
| [lib/core/services/pagination/widgets/loading.dart](/lib/core/services/pagination/widgets/loading.dart) | Dart | 15 | 0 | 3 | 18 |
| [lib/core/services/pagination/widgets/page_error.dart](/lib/core/services/pagination/widgets/page_error.dart) | Dart | 24 | 0 | 3 | 27 |
| [lib/core/services/player/errors.dart](/lib/core/services/player/errors.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/core/services/player/player.dart](/lib/core/services/player/player.dart) | Dart | 106 | 4 | 19 | 129 |
| [lib/core/services/player/status.dart](/lib/core/services/player/status.dart) | Dart | 11 | 0 | 2 | 13 |
| [lib/core/services/pusher.dart](/lib/core/services/pusher.dart) | Dart | 0 | 89 | 13 | 102 |
| [lib/core/services/register_fcm/service.dart](/lib/core/services/register_fcm/service.dart) | Dart | 49 | 1 | 10 | 60 |
| [lib/core/services/register_fcm/status.dart](/lib/core/services/register_fcm/status.dart) | Dart | 17 | 0 | 2 | 19 |
| [lib/core/services/rest_api/api_service.dart](/lib/core/services/rest_api/api_service.dart) | Dart | 289 | 12 | 19 | 320 |
| [lib/core/services/rest_api/constants/api_error.dart](/lib/core/services/rest_api/constants/api_error.dart) | Dart | 12 | 1 | 13 | 26 |
| [lib/core/services/rest_api/constants/end_points.dart](/lib/core/services/rest_api/constants/end_points.dart) | Dart | 45 | 24 | 14 | 83 |
| [lib/core/services/rest_api/constants/messages.dart](/lib/core/services/rest_api/constants/messages.dart) | Dart | 11 | 0 | 2 | 13 |
| [lib/core/services/rest_api/handlers/error_handler.dart](/lib/core/services/rest_api/handlers/error_handler.dart) | Dart | 75 | 1 | 3 | 79 |
| [lib/core/services/rest_api/handlers/success_handler.dart](/lib/core/services/rest_api/handlers/success_handler.dart) | Dart | 39 | 1 | 5 | 45 |
| [lib/core/services/rest_api/logger/logger.dart](/lib/core/services/rest_api/logger/logger.dart) | Dart | 38 | 0 | 6 | 44 |
| [lib/core/services/rest_api/models/exceptions.dart](/lib/core/services/rest_api/models/exceptions.dart) | Dart | 4 | 0 | 2 | 6 |
| [lib/core/services/rest_api/models/request.dart](/lib/core/services/rest_api/models/request.dart) | Dart | 124 | 18 | 30 | 172 |
| [lib/core/services/rest_api/models/response_model.dart](/lib/core/services/rest_api/models/response_model.dart) | Dart | 24 | 1 | 5 | 30 |
| [lib/core/services/rest_api/rest_api.dart](/lib/core/services/rest_api/rest_api.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/core/services/rest_api/utilitis/parser.dart](/lib/core/services/rest_api/utilitis/parser.dart) | Dart | 56 | 0 | 3 | 59 |
| [lib/core/services/rest_api/utilitis/queue.dart](/lib/core/services/rest_api/utilitis/queue.dart) | Dart | 65 | 6 | 8 | 79 |
| [lib/core/services/rest_api/widgets/request_refresher.dart](/lib/core/services/rest_api/widgets/request_refresher.dart) | Dart | 99 | 1 | 11 | 111 |
| [lib/core/services/state_management/observable_variable.dart](/lib/core/services/state_management/observable_variable.dart) | Dart | 178 | 0 | 38 | 216 |
| [lib/core/services/state_management/variable_status.dart](/lib/core/services/state_management/variable_status.dart) | Dart | 5 | 9 | 10 | 24 |
| [lib/core/style/assets/assets.dart](/lib/core/style/assets/assets.dart) | Dart | 3 | 0 | 0 | 3 |
| [lib/core/style/assets/gen/assets.gen.dart](/lib/core/style/assets/gen/assets.gen.dart) | Dart | 408 | 98 | 118 | 624 |
| [lib/core/style/assets/gen/fonts.gen.dart](/lib/core/style/assets/gen/fonts.gen.dart) | Dart | 4 | 8 | 4 | 16 |
| [lib/core/style/repo.dart](/lib/core/style/repo.dart) | Dart | 48 | 0 | 10 | 58 |
| [lib/core/style/style.dart](/lib/core/style/style.dart) | Dart | 87 | 11 | 4 | 102 |
| [lib/core/style/themes.dart](/lib/core/style/themes.dart) | Dart | 17 | 8 | 5 | 30 |
| [lib/core/style/utils/hex_colors.dart](/lib/core/style/utils/hex_colors.dart) | Dart | 11 | 0 | 3 | 14 |
| [lib/core/style/utils/theme_value.dart](/lib/core/style/utils/theme_value.dart) | Dart | 6 | 0 | 3 | 9 |
| [lib/core/utils/action_stack.dart](/lib/core/utils/action_stack.dart) | Dart | 22 | 0 | 5 | 27 |
| [lib/core/utils/date.dart](/lib/core/utils/date.dart) | Dart | 62 | 30 | 12 | 104 |
| [lib/core/utils/file_utils.dart](/lib/core/utils/file_utils.dart) | Dart | 14 | 0 | 3 | 17 |
| [lib/core/utils/firebase_options.dart](/lib/core/utils/firebase_options.dart) | Dart | 0 | 57 | 5 | 62 |
| [lib/core/utils/image_utils.dart](/lib/core/utils/image_utils.dart) | Dart | 43 | 36 | 14 | 93 |
| [lib/core/utils/list_utils.dart](/lib/core/utils/list_utils.dart) | Dart | 25 | 0 | 3 | 28 |
| [lib/core/utils/num_utils.dart](/lib/core/utils/num_utils.dart) | Dart | 30 | 0 | 6 | 36 |
| [lib/core/utils/responsivity.dart](/lib/core/utils/responsivity.dart) | Dart | 31 | 0 | 4 | 35 |
| [lib/core/utils/string_utiles.dart](/lib/core/utils/string_utiles.dart) | Dart | 39 | 1 | 3 | 43 |
| [lib/core/utils/timer.dart](/lib/core/utils/timer.dart) | Dart | 27 | 0 | 6 | 33 |
| [lib/core/utils/validator.dart](/lib/core/utils/validator.dart) | Dart | 52 | 0 | 6 | 58 |
| [lib/core/widgets/error_widget.dart](/lib/core/widgets/error_widget.dart) | Dart | 76 | 0 | 5 | 81 |
| [lib/core/widgets/guest_bottom_sheet.dart](/lib/core/widgets/guest_bottom_sheet.dart) | Dart | 48 | 0 | 3 | 51 |
| [lib/core/widgets/image.dart](/lib/core/widgets/image.dart) | Dart | 107 | 1 | 11 | 119 |
| [lib/core/widgets/loading.dart](/lib/core/widgets/loading.dart) | Dart | 51 | 0 | 8 | 59 |
| [lib/core/widgets/responsive_view.dart](/lib/core/widgets/responsive_view.dart) | Dart | 26 | 0 | 4 | 30 |
| [lib/core/widgets/shimmer_loading.dart](/lib/core/widgets/shimmer_loading.dart) | Dart | 30 | 0 | 4 | 34 |
| [lib/features/activities/activities/bindings.dart](/lib/features/activities/activities/bindings.dart) | Dart | 10 | 0 | 3 | 13 |
| [lib/features/activities/activities/controller.dart](/lib/features/activities/activities/controller.dart) | Dart | 21 | 0 | 4 | 25 |
| [lib/features/activities/activities/index.dart](/lib/features/activities/activities/index.dart) | Dart | 31 | 0 | 4 | 35 |
| [lib/features/activities/activities/models/nav.dart](/lib/features/activities/activities/models/nav.dart) | Dart | 6 | 0 | 1 | 7 |
| [lib/features/activities/activitiy_details/bindings.dart](/lib/features/activities/activitiy_details/bindings.dart) | Dart | 10 | 0 | 2 | 12 |
| [lib/features/activities/activitiy_details/controller.dart](/lib/features/activities/activitiy_details/controller.dart) | Dart | 332 | 14 | 49 | 395 |
| [lib/features/activities/activitiy_details/index.dart](/lib/features/activities/activitiy_details/index.dart) | Dart | 116 | 4 | 4 | 124 |
| [lib/features/activities/activitiy_details/models/activity.dart](/lib/features/activities/activitiy_details/models/activity.dart) | Dart | 124 | 0 | 7 | 131 |
| [lib/features/activities/activitiy_details/models/price_type.dart](/lib/features/activities/activitiy_details/models/price_type.dart) | Dart | 15 | 0 | 2 | 17 |
| [lib/features/activities/activitiy_details/models/set.dart](/lib/features/activities/activitiy_details/models/set.dart) | Dart | 11 | 0 | 3 | 14 |
| [lib/features/activities/activitiy_details/models/type.dart](/lib/features/activities/activitiy_details/models/type.dart) | Dart | 41 | 0 | 3 | 44 |
| [lib/features/activities/activitiy_details/widgets/about.dart](/lib/features/activities/activitiy_details/widgets/about.dart) | Dart | 47 | 0 | 3 | 50 |
| [lib/features/activities/activitiy_details/widgets/calendar.dart](/lib/features/activities/activitiy_details/widgets/calendar.dart) | Dart | 142 | 0 | 5 | 147 |
| [lib/features/activities/activitiy_details/widgets/categories.dart](/lib/features/activities/activitiy_details/widgets/categories.dart) | Dart | 27 | 0 | 3 | 30 |
| [lib/features/activities/activitiy_details/widgets/header.dart](/lib/features/activities/activitiy_details/widgets/header.dart) | Dart | 82 | 0 | 4 | 86 |
| [lib/features/activities/activitiy_details/widgets/info_column.dart](/lib/features/activities/activitiy_details/widgets/info_column.dart) | Dart | 318 | 0 | 4 | 322 |
| [lib/features/activities/activitiy_details/widgets/open_sessions.dart](/lib/features/activities/activitiy_details/widgets/open_sessions.dart) | Dart | 109 | 0 | 4 | 113 |
| [lib/features/activities/activitiy_details/widgets/organizers.dart](/lib/features/activities/activitiy_details/widgets/organizers.dart) | Dart | 56 | 0 | 3 | 59 |
| [lib/features/activities/activitiy_details/widgets/pictures.dart](/lib/features/activities/activitiy_details/widgets/pictures.dart) | Dart | 87 | 0 | 3 | 90 |
| [lib/features/activities/activitiy_details/widgets/sets.dart](/lib/features/activities/activitiy_details/widgets/sets.dart) | Dart | 167 | 0 | 4 | 171 |
| [lib/features/activities/activitiy_details/widgets/specific_days_sessions.dart](/lib/features/activities/activitiy_details/widgets/specific_days_sessions.dart) | Dart | 85 | 0 | 4 | 89 |
| [lib/features/activities/my_activities/controller.dart](/lib/features/activities/my_activities/controller.dart) | Dart | 162 | 5 | 15 | 182 |
| [lib/features/activities/my_activities/index.dart](/lib/features/activities/my_activities/index.dart) | Dart | 75 | 0 | 4 | 79 |
| [lib/features/activities/my_activities/models/completed_book.dart](/lib/features/activities/my_activities/models/completed_book.dart) | Dart | 23 | 0 | 5 | 28 |
| [lib/features/activities/my_activities/models/favourite_activity.dart](/lib/features/activities/my_activities/models/favourite_activity.dart) | Dart | 8 | 0 | 2 | 10 |
| [lib/features/activities/my_activities/models/tabs.dart](/lib/features/activities/my_activities/models/tabs.dart) | Dart | 21 | 0 | 3 | 24 |
| [lib/features/activities/my_activities/widgets/completed.dart](/lib/features/activities/my_activities/widgets/completed.dart) | Dart | 26 | 0 | 4 | 30 |
| [lib/features/activities/my_activities/widgets/favourites.dart](/lib/features/activities/my_activities/widgets/favourites.dart) | Dart | 34 | 0 | 4 | 38 |
| [lib/features/activities/my_activities/widgets/loading.dart](/lib/features/activities/my_activities/widgets/loading.dart) | Dart | 18 | 0 | 3 | 21 |
| [lib/features/activities/my_activities/widgets/pending.dart](/lib/features/activities/my_activities/widgets/pending.dart) | Dart | 57 | 0 | 4 | 61 |
| [lib/features/activities/my_activities/widgets/progress.dart](/lib/features/activities/my_activities/widgets/progress.dart) | Dart | 56 | 0 | 4 | 60 |
| [lib/features/activities/widgets/activities_list.dart](/lib/features/activities/widgets/activities_list.dart) | Dart | 72 | 0 | 3 | 75 |
| [lib/features/activities/widgets/activity_card.dart](/lib/features/activities/widgets/activity_card.dart) | Dart | 136 | 17 | 3 | 156 |
| [lib/features/activities/widgets/calendar_activitiy_card.dart](/lib/features/activities/widgets/calendar_activitiy_card.dart) | Dart | 102 | 0 | 4 | 106 |
| [lib/features/activities/widgets/favourite_activity_card.dart](/lib/features/activities/widgets/favourite_activity_card.dart) | Dart | 117 | 0 | 4 | 121 |
| [lib/features/activities/widgets/periodic_time_info.dart](/lib/features/activities/widgets/periodic_time_info.dart) | Dart | 178 | 0 | 4 | 182 |
| [lib/features/activities/widgets/seasonal_activity_card.dart](/lib/features/activities/widgets/seasonal_activity_card.dart) | Dart | 50 | 0 | 3 | 53 |
| [lib/features/auth/create_account/bindings.dart](/lib/features/auth/create_account/bindings.dart) | Dart | 10 | 0 | 3 | 13 |
| [lib/features/auth/create_account/controller.dart](/lib/features/auth/create_account/controller.dart) | Dart | 84 | 0 | 15 | 99 |
| [lib/features/auth/create_account/index.dart](/lib/features/auth/create_account/index.dart) | Dart | 71 | 0 | 4 | 75 |
| [lib/features/auth/create_account/models/age_range.dart](/lib/features/auth/create_account/models/age_range.dart) | Dart | 1 | 1 | 1 | 3 |
| [lib/features/auth/create_account/models/nav.dart](/lib/features/auth/create_account/models/nav.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/features/auth/create_account/widgets/children.dart](/lib/features/auth/create_account/widgets/children.dart) | Dart | 86 | 0 | 4 | 90 |
| [lib/features/auth/create_account/widgets/form.dart](/lib/features/auth/create_account/widgets/form.dart) | Dart | 82 | 0 | 4 | 86 |
| [lib/features/auth/create_account/widgets/terms_acception.dart](/lib/features/auth/create_account/widgets/terms_acception.dart) | Dart | 67 | 0 | 4 | 71 |
| [lib/features/auth/login/controller.dart](/lib/features/auth/login/controller.dart) | Dart | 33 | 0 | 6 | 39 |
| [lib/features/auth/login/index.dart](/lib/features/auth/login/index.dart) | Dart | 84 | 0 | 4 | 88 |
| [lib/features/auth/login/widgets/languages.dart](/lib/features/auth/login/widgets/languages.dart) | Dart | 49 | 0 | 3 | 52 |
| [lib/features/auth/signup/bindings.dart](/lib/features/auth/signup/bindings.dart) | Dart | 10 | 0 | 3 | 13 |
| [lib/features/auth/signup/controller.dart](/lib/features/auth/signup/controller.dart) | Dart | 22 | 0 | 6 | 28 |
| [lib/features/auth/signup/index.dart](/lib/features/auth/signup/index.dart) | Dart | 69 | 0 | 4 | 73 |
| [lib/features/auth/signup/models/nav.dart](/lib/features/auth/signup/models/nav.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/features/auth/verification/bindings.dart](/lib/features/auth/verification/bindings.dart) | Dart | 10 | 0 | 3 | 13 |
| [lib/features/auth/verification/controller.dart](/lib/features/auth/verification/controller.dart) | Dart | 75 | 0 | 9 | 84 |
| [lib/features/auth/verification/index.dart](/lib/features/auth/verification/index.dart) | Dart | 90 | 2 | 4 | 96 |
| [lib/features/auth/verification/models/nav.dart](/lib/features/auth/verification/models/nav.dart) | Dart | 4 | 0 | 2 | 6 |
| [lib/features/auth/verification/widgets/code_field.dart](/lib/features/auth/verification/widgets/code_field.dart) | Dart | 35 | 0 | 4 | 39 |
| [lib/features/auth/verification/widgets/code_timer.dart](/lib/features/auth/verification/widgets/code_timer.dart) | Dart | 54 | 0 | 4 | 58 |
| [lib/features/auth/widgets/auth_card.dart](/lib/features/auth/widgets/auth_card.dart) | Dart | 24 | 0 | 3 | 27 |
| [lib/features/auth/widgets/terms_privacy_row.dart](/lib/features/auth/widgets/terms_privacy_row.dart) | Dart | 36 | 0 | 3 | 39 |
| [lib/features/boarding/controller.dart](/lib/features/boarding/controller.dart) | Dart | 35 | 0 | 8 | 43 |
| [lib/features/boarding/index.dart](/lib/features/boarding/index.dart) | Dart | 112 | 0 | 4 | 116 |
| [lib/features/boarding/models/boarding_data.dart](/lib/features/boarding/models/boarding_data.dart) | Dart | 24 | 0 | 4 | 28 |
| [lib/features/books/book_details/bindings.dart](/lib/features/books/book_details/bindings.dart) | Dart | 7 | 0 | 2 | 9 |
| [lib/features/books/book_details/controller.dart](/lib/features/books/book_details/controller.dart) | Dart | 106 | 5 | 24 | 135 |
| [lib/features/books/book_details/index.dart](/lib/features/books/book_details/index.dart) | Dart | 84 | 1 | 4 | 89 |
| [lib/features/books/book_details/widgets/calendar.dart](/lib/features/books/book_details/widgets/calendar.dart) | Dart | 138 | 0 | 4 | 142 |
| [lib/features/books/book_details/widgets/date_time_info.dart](/lib/features/books/book_details/widgets/date_time_info.dart) | Dart | 164 | 0 | 4 | 168 |
| [lib/features/books/book_details/widgets/header.dart](/lib/features/books/book_details/widgets/header.dart) | Dart | 72 | 0 | 3 | 75 |
| [lib/features/books/book_details/widgets/pictures.dart](/lib/features/books/book_details/widgets/pictures.dart) | Dart | 87 | 0 | 3 | 90 |
| [lib/features/books/book_details/widgets/specific_days_sessions.dart](/lib/features/books/book_details/widgets/specific_days_sessions.dart) | Dart | 74 | 0 | 4 | 78 |
| [lib/features/books/widgets/book_card.dart](/lib/features/books/widgets/book_card.dart) | Dart | 277 | 5 | 7 | 289 |
| [lib/features/categories/categories/controller.dart](/lib/features/categories/categories/controller.dart) | Dart | 19 | 0 | 5 | 24 |
| [lib/features/categories/categories/index.dart](/lib/features/categories/categories/index.dart) | Dart | 39 | 0 | 4 | 43 |
| [lib/features/categories/categories/widgets/all_card.dart](/lib/features/categories/categories/widgets/all_card.dart) | Dart | 33 | 0 | 3 | 36 |
| [lib/features/categories/categories/widgets/category_card.dart](/lib/features/categories/categories/widgets/category_card.dart) | Dart | 49 | 0 | 4 | 53 |
| [lib/features/categories/categories/widgets/loading.dart](/lib/features/categories/categories/widgets/loading.dart) | Dart | 18 | 0 | 3 | 21 |
| [lib/features/categories/category/bindings.dart](/lib/features/categories/category/bindings.dart) | Dart | 13 | 0 | 3 | 16 |
| [lib/features/categories/category/controller.dart](/lib/features/categories/category/controller.dart) | Dart | 59 | 0 | 11 | 70 |
| [lib/features/categories/category/index.dart](/lib/features/categories/category/index.dart) | Dart | 67 | 0 | 4 | 71 |
| [lib/features/categories/category/models/nav.dart](/lib/features/categories/category/models/nav.dart) | Dart | 5 | 0 | 2 | 7 |
| [lib/features/categories/category/widgets/sub_categories.dart](/lib/features/categories/category/widgets/sub_categories.dart) | Dart | 65 | 0 | 4 | 69 |
| [lib/features/center/centers/bindings.dart](/lib/features/center/centers/bindings.dart) | Dart | 10 | 0 | 3 | 13 |
| [lib/features/center/centers/controller.dart](/lib/features/center/centers/controller.dart) | Dart | 33 | 0 | 8 | 41 |
| [lib/features/center/centers/index.dart](/lib/features/center/centers/index.dart) | Dart | 39 | 0 | 4 | 43 |
| [lib/features/center/centers/models/nav.dart](/lib/features/center/centers/models/nav.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/features/center/models/center_review.dart](/lib/features/center/models/center_review.dart) | Dart | 8 | 0 | 3 | 11 |
| [lib/features/center/profile/bindings.dart](/lib/features/center/profile/bindings.dart) | Dart | 11 | 0 | 3 | 14 |
| [lib/features/center/profile/controller.dart](/lib/features/center/profile/controller.dart) | Dart | 71 | 0 | 9 | 80 |
| [lib/features/center/profile/index.dart](/lib/features/center/profile/index.dart) | Dart | 85 | 0 | 4 | 89 |
| [lib/features/center/profile/models/center_details.dart](/lib/features/center/profile/models/center_details.dart) | Dart | 64 | 0 | 9 | 73 |
| [lib/features/center/profile/models/nav.dart](/lib/features/center/profile/models/nav.dart) | Dart | 4 | 0 | 2 | 6 |
| [lib/features/center/profile/widgets/about.dart](/lib/features/center/profile/widgets/about.dart) | Dart | 49 | 0 | 3 | 52 |
| [lib/features/center/profile/widgets/branches.dart](/lib/features/center/profile/widgets/branches.dart) | Dart | 48 | 0 | 4 | 52 |
| [lib/features/center/profile/widgets/categories.dart](/lib/features/center/profile/widgets/categories.dart) | Dart | 27 | 0 | 3 | 30 |
| [lib/features/center/profile/widgets/header.dart](/lib/features/center/profile/widgets/header.dart) | Dart | 84 | 0 | 4 | 88 |
| [lib/features/center/profile/widgets/loading.dart](/lib/features/center/profile/widgets/loading.dart) | Dart | 71 | 0 | 4 | 75 |
| [lib/features/center/profile/widgets/pictures.dart](/lib/features/center/profile/widgets/pictures.dart) | Dart | 88 | 0 | 4 | 92 |
| [lib/features/center/profile/widgets/reviews.dart](/lib/features/center/profile/widgets/reviews.dart) | Dart | 61 | 0 | 4 | 65 |
| [lib/features/center/reviews/bindings.dart](/lib/features/center/reviews/bindings.dart) | Dart | 9 | 0 | 3 | 12 |
| [lib/features/center/reviews/controller.dart](/lib/features/center/reviews/controller.dart) | Dart | 18 | 0 | 4 | 22 |
| [lib/features/center/reviews/index.dart](/lib/features/center/reviews/index.dart) | Dart | 27 | 0 | 4 | 31 |
| [lib/features/center/widgets/center_card.dart](/lib/features/center/widgets/center_card.dart) | Dart | 119 | 0 | 3 | 122 |
| [lib/features/center/widgets/review_card.dart](/lib/features/center/widgets/review_card.dart) | Dart | 52 | 0 | 4 | 56 |
| [lib/features/chat/preview/document/bindings.dart](/lib/features/chat/preview/document/bindings.dart) | Dart | 9 | 0 | 4 | 13 |
| [lib/features/chat/preview/document/controller.dart](/lib/features/chat/preview/document/controller.dart) | Dart | 15 | 0 | 6 | 21 |
| [lib/features/chat/preview/document/index.dart](/lib/features/chat/preview/document/index.dart) | Dart | 110 | 0 | 4 | 114 |
| [lib/features/chat/preview/images/bindings.dart](/lib/features/chat/preview/images/bindings.dart) | Dart | 9 | 0 | 2 | 11 |
| [lib/features/chat/preview/images/controller.dart](/lib/features/chat/preview/images/controller.dart) | Dart | 35 | 0 | 11 | 46 |
| [lib/features/chat/preview/images/index.dart](/lib/features/chat/preview/images/index.dart) | Dart | 132 | 0 | 4 | 136 |
| [lib/features/chat/preview/location/controller.dart](/lib/features/chat/preview/location/controller.dart) | Dart | 46 | 0 | 9 | 55 |
| [lib/features/chat/preview/location/index.dart](/lib/features/chat/preview/location/index.dart) | Dart | 74 | 1 | 4 | 79 |
| [lib/features/chat/preview/video/bindings.dart](/lib/features/chat/preview/video/bindings.dart) | Dart | 9 | 0 | 4 | 13 |
| [lib/features/chat/preview/video/controller.dart](/lib/features/chat/preview/video/controller.dart) | Dart | 36 | 0 | 11 | 47 |
| [lib/features/chat/preview/video/index.dart](/lib/features/chat/preview/video/index.dart) | Dart | 111 | 0 | 4 | 115 |
| [lib/features/chat/room/bindings.dart](/lib/features/chat/room/bindings.dart) | Dart | 10 | 0 | 3 | 13 |
| [lib/features/chat/room/controller.dart](/lib/features/chat/room/controller.dart) | Dart | 439 | 24 | 85 | 548 |
| [lib/features/chat/room/index.dart](/lib/features/chat/room/index.dart) | Dart | 72 | 0 | 4 | 76 |
| [lib/features/chat/room/widgets/message.dart](/lib/features/chat/room/widgets/message.dart) | Dart | 115 | 0 | 7 | 122 |
| [lib/features/chat/room/widgets/message_types_popup.dart](/lib/features/chat/room/widgets/message_types_popup.dart) | Dart | 190 | 0 | 4 | 194 |
| [lib/features/chat/room/widgets/messages/location_message.dart](/lib/features/chat/room/widgets/messages/location_message.dart) | Dart | 80 | 0 | 4 | 84 |
| [lib/features/chat/room/widgets/messages/media_message_content.dart](/lib/features/chat/room/widgets/messages/media_message_content.dart) | Dart | 380 | 0 | 3 | 383 |
| [lib/features/chat/room/widgets/messages/text_message_content.dart](/lib/features/chat/room/widgets/messages/text_message_content.dart) | Dart | 21 | 0 | 4 | 25 |
| [lib/features/chat/room/widgets/messages/voice_message_content.dart](/lib/features/chat/room/widgets/messages/voice_message_content.dart) | Dart | 144 | 3 | 5 | 152 |
| [lib/features/chat/room/widgets/sending_bar.dart](/lib/features/chat/room/widgets/sending_bar.dart) | Dart | 238 | 1 | 13 | 252 |
| [lib/features/chat/rooms/controller.dart](/lib/features/chat/rooms/controller.dart) | Dart | 30 | 0 | 5 | 35 |
| [lib/features/chat/rooms/index.dart](/lib/features/chat/rooms/index.dart) | Dart | 31 | 0 | 4 | 35 |
| [lib/features/chat/rooms/widgets/chat_card.dart](/lib/features/chat/rooms/widgets/chat_card.dart) | Dart | 69 | 0 | 3 | 72 |
| [lib/features/chat/rooms/widgets/loading.dart](/lib/features/chat/rooms/widgets/loading.dart) | Dart | 43 | 0 | 3 | 46 |
| [lib/features/create_child/bindings.dart](/lib/features/create_child/bindings.dart) | Dart | 10 | 0 | 3 | 13 |
| [lib/features/create_child/controller.dart](/lib/features/create_child/controller.dart) | Dart | 168 | 8 | 20 | 196 |
| [lib/features/create_child/index.dart](/lib/features/create_child/index.dart) | Dart | 36 | 0 | 4 | 40 |
| [lib/features/create_child/models/child_creation.dart](/lib/features/create_child/models/child_creation.dart) | Dart | 90 | 0 | 7 | 97 |
| [lib/features/create_child/models/nav.dart](/lib/features/create_child/models/nav.dart) | Dart | 6 | 0 | 3 | 9 |
| [lib/features/create_child/widgets/form.dart](/lib/features/create_child/widgets/form.dart) | Dart | 279 | 0 | 5 | 284 |
| [lib/features/create_child/widgets/medical_info.dart](/lib/features/create_child/widgets/medical_info.dart) | Dart | 96 | 0 | 5 | 101 |
| [lib/features/filters/controller.dart](/lib/features/filters/controller.dart) | Dart | 225 | 2 | 24 | 251 |
| [lib/features/filters/index.dart](/lib/features/filters/index.dart) | Dart | 77 | 1 | 4 | 82 |
| [lib/features/filters/models/categories.dart/main_category.dart](/lib/features/filters/models/categories.dart/main_category.dart) | Dart | 25 | 0 | 5 | 30 |
| [lib/features/filters/models/filters_data.dart](/lib/features/filters/models/filters_data.dart) | Dart | 46 | 0 | 5 | 51 |
| [lib/features/filters/models/price_type.dart](/lib/features/filters/models/price_type.dart) | Dart | 23 | 0 | 4 | 27 |
| [lib/features/filters/widgets/age.dart](/lib/features/filters/widgets/age.dart) | Dart | 102 | 0 | 5 | 107 |
| [lib/features/filters/widgets/category.dart](/lib/features/filters/widgets/category.dart) | Dart | 49 | 0 | 4 | 53 |
| [lib/features/filters/widgets/gender.dart](/lib/features/filters/widgets/gender.dart) | Dart | 66 | 0 | 4 | 70 |
| [lib/features/filters/widgets/price_range.dart](/lib/features/filters/widgets/price_range.dart) | Dart | 59 | 0 | 4 | 63 |
| [lib/features/filters/widgets/price_type.dart](/lib/features/filters/widgets/price_type.dart) | Dart | 60 | 0 | 4 | 64 |
| [lib/features/filters/widgets/rating.dart](/lib/features/filters/widgets/rating.dart) | Dart | 84 | 33 | 4 | 121 |
| [lib/features/filters/widgets/special_need.dart](/lib/features/filters/widgets/special_need.dart) | Dart | 59 | 0 | 4 | 63 |
| [lib/features/filters/widgets/subcategories.dart](/lib/features/filters/widgets/subcategories.dart) | Dart | 67 | 0 | 4 | 71 |
| [lib/features/home/<USER>/lib/features/home/<USER>
| [lib/features/home/<USER>/lib/features/home/<USER>
| [lib/features/home/<USER>/ad.dart](/lib/features/home/<USER>/ad.dart) | Dart | 68 | 0 | 9 | 77 |
| [lib/features/home/<USER>/centers_list.dart](/lib/features/home/<USER>/centers_list.dart) | Dart | 63 | 0 | 4 | 67 |
| [lib/features/home/<USER>/seasonal_activities_list.dart](/lib/features/home/<USER>/seasonal_activities_list.dart) | Dart | 63 | 0 | 4 | 67 |
| [lib/features/home/<USER>/slider.dart](/lib/features/home/<USER>/slider.dart) | Dart | 136 | 0 | 4 | 140 |
| [lib/features/main/controller.dart](/lib/features/main/controller.dart) | Dart | 34 | 0 | 4 | 38 |
| [lib/features/main/index.dart](/lib/features/main/index.dart) | Dart | 58 | 0 | 4 | 62 |
| [lib/features/main/models/destinations.dart](/lib/features/main/models/destinations.dart) | Dart | 34 | 0 | 4 | 38 |
| [lib/features/main/widgets/bottom_nav_bar.dart](/lib/features/main/widgets/bottom_nav_bar.dart) | Dart | 24 | 0 | 4 | 28 |
| [lib/features/main/widgets/drawer.dart](/lib/features/main/widgets/drawer.dart) | Dart | 152 | 7 | 4 | 163 |
| [lib/features/main/widgets/drawer_card.dart](/lib/features/main/widgets/drawer_card.dart) | Dart | 72 | 0 | 3 | 75 |
| [lib/features/notifications/controller.dart](/lib/features/notifications/controller.dart) | Dart | 22 | 0 | 4 | 26 |
| [lib/features/notifications/index.dart](/lib/features/notifications/index.dart) | Dart | 32 | 1 | 4 | 37 |
| [lib/features/notifications/widgets/card.dart](/lib/features/notifications/widgets/card.dart) | Dart | 72 | 0 | 4 | 76 |
| [lib/features/profile/change_phone/controller.dart](/lib/features/profile/change_phone/controller.dart) | Dart | 12 | 0 | 5 | 17 |
| [lib/features/profile/change_phone/index.dart](/lib/features/profile/change_phone/index.dart) | Dart | 66 | 1 | 4 | 71 |
| [lib/features/profile/child_profile/bindings.dart](/lib/features/profile/child_profile/bindings.dart) | Dart | 10 | 0 | 3 | 13 |
| [lib/features/profile/child_profile/controller.dart](/lib/features/profile/child_profile/controller.dart) | Dart | 113 | 3 | 20 | 136 |
| [lib/features/profile/child_profile/index.dart](/lib/features/profile/child_profile/index.dart) | Dart | 132 | 0 | 4 | 136 |
| [lib/features/profile/child_profile/models/calendar_session.dart](/lib/features/profile/child_profile/models/calendar_session.dart) | Dart | 20 | 0 | 3 | 23 |
| [lib/features/profile/child_profile/models/child.dart](/lib/features/profile/child_profile/models/child.dart) | Dart | 60 | 0 | 7 | 67 |
| [lib/features/profile/child_profile/models/nav.dart](/lib/features/profile/child_profile/models/nav.dart) | Dart | 4 | 0 | 1 | 5 |
| [lib/features/profile/child_profile/widgets/activities.dart](/lib/features/profile/child_profile/widgets/activities.dart) | Dart | 39 | 0 | 5 | 44 |
| [lib/features/profile/child_profile/widgets/calendar.dart](/lib/features/profile/child_profile/widgets/calendar.dart) | Dart | 137 | 0 | 4 | 141 |
| [lib/features/profile/child_profile/widgets/heading_card.dart](/lib/features/profile/child_profile/widgets/heading_card.dart) | Dart | 77 | 0 | 4 | 81 |
| [lib/features/profile/child_profile/widgets/hobbies.dart](/lib/features/profile/child_profile/widgets/hobbies.dart) | Dart | 41 | 0 | 4 | 45 |
| [lib/features/profile/child_profile/widgets/loading.dart](/lib/features/profile/child_profile/widgets/loading.dart) | Dart | 52 | 0 | 4 | 56 |
| [lib/features/profile/edit_profile/bindings.dart](/lib/features/profile/edit_profile/bindings.dart) | Dart | 10 | 0 | 3 | 13 |
| [lib/features/profile/edit_profile/controller.dart](/lib/features/profile/edit_profile/controller.dart) | Dart | 76 | 4 | 14 | 94 |
| [lib/features/profile/edit_profile/index.dart](/lib/features/profile/edit_profile/index.dart) | Dart | 29 | 0 | 4 | 33 |
| [lib/features/profile/edit_profile/models/nav.dart](/lib/features/profile/edit_profile/models/nav.dart) | Dart | 5 | 0 | 2 | 7 |
| [lib/features/profile/edit_profile/widgets/form.dart](/lib/features/profile/edit_profile/widgets/form.dart) | Dart | 172 | 0 | 4 | 176 |
| [lib/features/profile/my_profile/controller.dart](/lib/features/profile/my_profile/controller.dart) | Dart | 90 | 0 | 13 | 103 |
| [lib/features/profile/my_profile/index.dart](/lib/features/profile/my_profile/index.dart) | Dart | 120 | 1 | 4 | 125 |
| [lib/features/profile/my_profile/models/profile.dart](/lib/features/profile/my_profile/models/profile.dart) | Dart | 15 | 0 | 2 | 17 |
| [lib/features/profile/my_profile/widgets/children.dart](/lib/features/profile/my_profile/widgets/children.dart) | Dart | 101 | 0 | 4 | 105 |
| [lib/features/profile/my_profile/widgets/loading.dart](/lib/features/profile/my_profile/widgets/loading.dart) | Dart | 48 | 0 | 3 | 51 |
| [lib/features/profile/my_profile/widgets/profile_field.dart](/lib/features/profile/my_profile/widgets/profile_field.dart) | Dart | 48 | 0 | 3 | 51 |
| [lib/features/settings/about/controller.dart](/lib/features/settings/about/controller.dart) | Dart | 19 | 0 | 5 | 24 |
| [lib/features/settings/about/index.dart](/lib/features/settings/about/index.dart) | Dart | 22 | 0 | 4 | 26 |
| [lib/features/settings/help/controller.dart](/lib/features/settings/help/controller.dart) | Dart | 19 | 0 | 5 | 24 |
| [lib/features/settings/help/index.dart](/lib/features/settings/help/index.dart) | Dart | 22 | 0 | 4 | 26 |
| [lib/features/settings/privacy/controller.dart](/lib/features/settings/privacy/controller.dart) | Dart | 2 | 0 | 2 | 4 |
| [lib/features/settings/privacy/index.dart](/lib/features/settings/privacy/index.dart) | Dart | 12 | 1 | 4 | 17 |
| [lib/features/settings/support_replies/controller.dart](/lib/features/settings/support_replies/controller.dart) | Dart | 12 | 0 | 3 | 15 |
| [lib/features/settings/support_replies/index.dart](/lib/features/settings/support_replies/index.dart) | Dart | 33 | 1 | 4 | 38 |
| [lib/features/settings/support_replies/models/suppoert_reply.dart](/lib/features/settings/support_replies/models/suppoert_reply.dart) | Dart | 27 | 0 | 4 | 31 |
| [lib/features/settings/support_replies/widgets/question_card.dart](/lib/features/settings/support_replies/widgets/question_card.dart) | Dart | 80 | 0 | 4 | 84 |
| [lib/features/settings/terms/controller.dart](/lib/features/settings/terms/controller.dart) | Dart | 19 | 0 | 5 | 24 |
| [lib/features/settings/terms/index.dart](/lib/features/settings/terms/index.dart) | Dart | 22 | 0 | 4 | 26 |
| [lib/features/settings/usecases/support/controller.dart](/lib/features/settings/usecases/support/controller.dart) | Dart | 45 | 0 | 9 | 54 |
| [lib/features/settings/usecases/support/index.dart](/lib/features/settings/usecases/support/index.dart) | Dart | 89 | 1 | 4 | 94 |
| [lib/features/settings/usecases/support/widgets/support_fab.dart](/lib/features/settings/usecases/support/widgets/support_fab.dart) | Dart | 28 | 0 | 5 | 33 |
| [lib/features/splash_screen/controller.dart](/lib/features/splash_screen/controller.dart) | Dart | 14 | 2 | 5 | 21 |
| [lib/features/splash_screen/index.dart](/lib/features/splash_screen/index.dart) | Dart | 22 | 1 | 3 | 26 |
| [lib/features/usecases/book_custom_session/controller.dart](/lib/features/usecases/book_custom_session/controller.dart) | Dart | 101 | 8 | 9 | 118 |
| [lib/features/usecases/book_custom_session/index.dart](/lib/features/usecases/book_custom_session/index.dart) | Dart | 123 | 0 | 6 | 129 |
| [lib/features/usecases/book_custom_session/models/result.dart](/lib/features/usecases/book_custom_session/models/result.dart) | Dart | 7 | 0 | 3 | 10 |
| [lib/features/usecases/children_selection/controller.dart](/lib/features/usecases/children_selection/controller.dart) | Dart | 81 | 1 | 16 | 98 |
| [lib/features/usecases/children_selection/widgets/children_selection_dialog.dart](/lib/features/usecases/children_selection/widgets/children_selection_dialog.dart) | Dart | 180 | 0 | 7 | 187 |
| [lib/features/usecases/confirmation/index.dart](/lib/features/usecases/confirmation/index.dart) | Dart | 102 | 0 | 5 | 107 |
| [lib/features/usecases/gender/choose_gender.dart](/lib/features/usecases/gender/choose_gender.dart) | Dart | 133 | 0 | 6 | 139 |
| [lib/features/usecases/gender/widgets/gender_card.dart](/lib/features/usecases/gender/widgets/gender_card.dart) | Dart | 96 | 0 | 5 | 101 |
| [lib/features/usecases/gender/widgets/gender_tile.dart](/lib/features/usecases/gender/widgets/gender_tile.dart) | Dart | 166 | 0 | 5 | 171 |
| [lib/features/usecases/phone_field/phone_field.dart](/lib/features/usecases/phone_field/phone_field.dart) | Dart | 80 | 0 | 5 | 85 |
| [lib/features/usecases/pictures/index.dart](/lib/features/usecases/pictures/index.dart) | Dart | 93 | 0 | 6 | 99 |
| [lib/features/usecases/players/video/controller.dart](/lib/features/usecases/players/video/controller.dart) | Dart | 49 | 0 | 9 | 58 |
| [lib/features/usecases/players/video/index.dart](/lib/features/usecases/players/video/index.dart) | Dart | 40 | 1 | 5 | 46 |
| [lib/features/usecases/players/videos_duration_end.dart](/lib/features/usecases/players/videos_duration_end.dart) | Dart | 17 | 0 | 2 | 19 |
| [lib/features/usecases/rating/controller.dart](/lib/features/usecases/rating/controller.dart) | Dart | 31 | 0 | 5 | 36 |
| [lib/features/usecases/rating/index.dart](/lib/features/usecases/rating/index.dart) | Dart | 106 | 0 | 6 | 112 |
| [lib/features/usecases/toast/status.dart](/lib/features/usecases/toast/status.dart) | Dart | 1 | 0 | 1 | 2 |
| [lib/features/usecases/toast/toast.dart](/lib/features/usecases/toast/toast.dart) | Dart | 38 | 0 | 3 | 41 |
| [lib/features/usecases/toast/widgets/toast.dart](/lib/features/usecases/toast/widgets/toast.dart) | Dart | 55 | 0 | 5 | 60 |
| [lib/features/widgets/available_bubble_cilpper.dart](/lib/features/widgets/available_bubble_cilpper.dart) | Dart | 68 | 1 | 7 | 76 |
| [lib/features/widgets/backgrounds/auth_bg.dart](/lib/features/widgets/backgrounds/auth_bg.dart) | Dart | 40 | 0 | 3 | 43 |
| [lib/features/widgets/coming_soon.dart](/lib/features/widgets/coming_soon.dart) | Dart | 17 | 0 | 3 | 20 |
| [lib/features/widgets/general_componenets/app_bars/general_app_bar.dart](/lib/features/widgets/general_componenets/app_bars/general_app_bar.dart) | Dart | 71 | 8 | 12 | 91 |
| [lib/features/widgets/general_componenets/buttons/elevated_button.dart](/lib/features/widgets/general_componenets/buttons/elevated_button.dart) | Dart | 93 | 1 | 7 | 101 |
| [lib/features/widgets/general_componenets/buttons/outlined_button.dart](/lib/features/widgets/general_componenets/buttons/outlined_button.dart) | Dart | 86 | 1 | 7 | 94 |
| [lib/features/widgets/general_componenets/dropdown.dart](/lib/features/widgets/general_componenets/dropdown.dart) | Dart | 27 | 0 | 3 | 30 |
| [lib/features/widgets/general_componenets/form_field_error.dart](/lib/features/widgets/general_componenets/form_field_error.dart) | Dart | 21 | 0 | 4 | 25 |
| [lib/features/widgets/general_componenets/pages/general_page.dart](/lib/features/widgets/general_componenets/pages/general_page.dart) | Dart | 41 | 0 | 5 | 46 |
| [lib/features/widgets/general_componenets/pages/rounded_page.dart](/lib/features/widgets/general_componenets/pages/rounded_page.dart) | Dart | 104 | 0 | 5 | 109 |
| [lib/features/widgets/general_componenets/svg_icon.dart](/lib/features/widgets/general_componenets/svg_icon.dart) | Dart | 20 | 0 | 3 | 23 |
| [lib/features/widgets/grid_cards_loading.dart](/lib/features/widgets/grid_cards_loading.dart) | Dart | 19 | 0 | 3 | 22 |
| [lib/features/widgets/horizontal_list.dart](/lib/features/widgets/horizontal_list.dart) | Dart | 73 | 0 | 6 | 79 |
| [lib/features/widgets/location_row.dart](/lib/features/widgets/location_row.dart) | Dart | 35 | 0 | 4 | 39 |
| [lib/features/widgets/provider_image.dart](/lib/features/widgets/provider_image.dart) | Dart | 47 | 0 | 4 | 51 |
| [lib/main.dart](/lib/main.dart) | Dart | 89 | 1 | 13 | 103 |
| [lib/test/test_360.dart](/lib/test/test_360.dart) | Dart | 18 | 0 | 3 | 21 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)