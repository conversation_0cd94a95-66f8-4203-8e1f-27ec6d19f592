const Map<List<int>, double> videoFinish = {
  [0, 300]: 0.97,
  [301, 600]: 0.95,
  [601, 99999999999]: 0.92,
};

bool isMediaCloseToEnd(int currentSecond, int videoDuration) {
  bool isFinished = false;
  for (var range in videoFinish.keys) {
    if (videoDuration >= range.first && videoDuration <= range.last) {
      if ((currentSecond / videoDuration) >= videoFinish[range]!) {
        isFinished = true;
        continue;
      }
    }
  }
  return isFinished;
}
