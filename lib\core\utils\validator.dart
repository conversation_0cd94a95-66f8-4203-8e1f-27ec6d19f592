import 'package:easy_localization/easy_localization.dart';
import 'package:gopal/core/localization/strings.dart';

abstract class Validator {
  static String? saPhoneNumber(String? number) {
    if (number == null || number.isEmpty) {
      return LocaleKeys.this_field_is_required.tr();
    }
    RegExp saPhoneNumberRegExp = RegExp(r'^(\+966|0)?5\d{8}$');
    if (!saPhoneNumberRegExp.hasMatch(number)) {
      return LocaleKeys.phone_number_is_not_valid.tr();
    }
    return null;
  }

  static String? emailValidation(String? email) {
    email = email?.trim();
    bool valid = RegExp(
            r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$')
        .hasMatch(email!);
    return valid == false ? LocaleKeys.email_is_not_valid.tr() : null;
  }

  static String? optionalEmailValidation(String? email) {
    if (email == null || email.isEmpty) {
      return null;
    }
    email = email.trim();
    bool valid = RegExp(
            r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$')
        .hasMatch(email);
    return valid == false ? LocaleKeys.email_is_not_valid.tr() : null;
  }

  static String? notNullValidation(str) {
    if (str == null) {
      return LocaleKeys.this_field_is_required.tr();
    }
    if (str is String && str.trim().isEmpty) {
      return LocaleKeys.this_field_is_required.tr();
    }
    return null;
  }

  static String? lengthValidation(String? str, {int min = 3, int max = 32}) {
    if (str == null) {
      throw "";
    }
    if (str.length < min) {
      return LocaleKeys.very_short.tr();
    }
    if (str.length > max) {
      return LocaleKeys.very_long.tr();
    }
    return null;
  }
}
