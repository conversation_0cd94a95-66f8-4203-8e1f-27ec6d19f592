import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/widgets/shimmer_loading.dart';

class MyProfileLoading extends StatelessWidget {
  const MyProfileLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Gap(16),
        Center(
          child: ShimmerWidget.card(
            height: 100,
            width: 100,
            borderRadius: BorderRadius.circular(50),
          ),
        ),
        const Gap(12),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: ShimmerWidget.card(
            height: 30,
            width: Get.width * .6,
            borderRadius: BorderRadius.circular(5),
          ),
        ),
        const Gap(36),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: ShimmerWidget.card(
            height: 50,
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        const Gap(16),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: ShimmerWidget.card(
            height: 50,
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      ],
    );
  }
}
