import 'package:blur/blur.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/models/book/book.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:table_calendar/table_calendar.dart';

import '../controller.dart';

class BookCalendar extends StatelessWidget {
  final BookedActivity book;
  const BookCalendar({super.key, required this.book});

  @override
  Widget build(BuildContext context) {
    BookDetailsPageController controller = Get.find();
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: StyleRepo.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: StyleRepo.elevation_3,
      ),
      child: Obx(() {
        Widget child = IgnorePointer(
          ignoring: !controller.calendarSessions.hasData,
          child: Obx(() {
            controller.selectedDate;
            return TableCalendar(
              availableGestures: AvailableGestures.none,
              focusedDay: controller.selectedDate,
              weekNumbersVisible: false,
              locale: EasyLocalization.of(context)!.currentLocale!.languageCode,
              onDaySelected: (date, _) => controller.selectedDate = date,
              onPageChanged: (date) => controller.onMonthChanged(date),
              selectedDayPredicate:
                  (date) =>
                      date.year == controller.selectedDate.year &&
                      date.month == controller.selectedDate.month &&
                      date.day == controller.selectedDate.day,
              firstDay: DateTime.now(),
              lastDay: DateTime.now().add(365.days),
              headerStyle: const HeaderStyle(
                titleCentered: true,
                formatButtonVisible: false,
              ),
              calendarBuilders: CalendarBuilders(
                todayBuilder: (context, date, _) {
                  return Container(
                    margin: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: StyleRepo.turquoise.withValues(alpha: .5),
                      shape: BoxShape.circle,
                    ),
                    child: Center(child: Text(date.day.toString())),
                  );
                },
                defaultBuilder: (context, date, _) {
                  if (controller.highlightedDays.contains(date.day)) {
                    return Container(
                      margin: const EdgeInsets.all(4),
                      height: double.infinity,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        color: StyleRepo.grey.withValues(alpha: .5),
                        shape: BoxShape.circle,
                      ),
                      child: Center(child: Text(date.day.toString())),
                    );
                  } else {
                    return null;
                  }
                },
                selectedBuilder: (context, date, _) {
                  return Container(
                    margin: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: StyleRepo.blueViolet,
                      borderRadius: BorderRadius.circular(5),
                    ),
                    child: Center(
                      child: Text(
                        date.day.toString(),
                        style: const TextStyle(
                          color: StyleRepo.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  );
                },
              ),
            );
          }),
        );
        if (controller.calendarSessions.loading) {
          return Blur(borderRadius: BorderRadius.circular(20), child: child);
        }
        if (controller.calendarSessions.hasError) {
          return Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 24),
            width: double.infinity,
            decoration: BoxDecoration(
              color: StyleRepo.grey.shade300,
              borderRadius: BorderRadius.circular(15),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  controller.calendarSessions.error!,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: StyleRepo.red,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                IconButton(
                  onPressed: () => controller.refreshCalendar(),
                  icon: const Icon(Icons.refresh),
                ),
              ],
            ),
          );
        } else {
          return child;
        }
      }),
    );
  }
}
