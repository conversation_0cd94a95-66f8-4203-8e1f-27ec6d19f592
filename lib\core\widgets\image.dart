// ignore_for_file: constant_identifier_names

import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/utils/url_validator.dart';
import 'package:shimmer/shimmer.dart';

class AppImage extends StatelessWidget {
  final String path;
  final ImageType type;
  final BoxFit fit;
  final Widget errorWidget;
  final Widget loadingWidget;
  final double? height;
  final double? width;
  final BoxDecoration? decoration;
  final EdgeInsets? margin;

  const AppImage({
    super.key,
    required this.path,
    required this.type,
    this.fit = BoxFit.cover,
    this.errorWidget = const ImageError(),
    this.loadingWidget = const ImageLoading(),
    this.height,
    this.width,
    this.margin,
    this.decoration,
  });

  bool _isValidUrl(String url) {
    return UrlValidator.isValidUrl(url);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      margin: margin,
      decoration: decoration,
      child: LayoutBuilder(
        builder: (context, constraints) {
          BorderRadiusGeometry effectiveBorderRadius =
              decoration?.borderRadius ?? BorderRadius.zero;
          if (decoration != null && decoration!.shape == BoxShape.circle) {
            effectiveBorderRadius = BorderRadius.circular(
              constraints.maxHeight / 2,
            );
          }
          return ClipRRect(
            borderRadius: effectiveBorderRadius,
            child: switch (type) {
              ImageType.CachedNetwork =>
                _isValidUrl(path)
                    ? CachedNetworkImage(
                      imageUrl: path,
                      fit: fit,
                      progressIndicatorBuilder:
                          (context, url, downloadProgress) => loadingWidget,
                      errorWidget: (context, e, i) => errorWidget,
                    )
                    : errorWidget,
              ImageType.Network =>
                _isValidUrl(path)
                    ? Image.network(
                      path,
                      errorBuilder: (context, _, i) => errorWidget,
                      loadingBuilder:
                          (
                            BuildContext context,
                            Widget child,
                            ImageChunkEvent? loadingProgress,
                          ) => loadingWidget,
                      fit: fit,
                    )
                    : errorWidget,
              ImageType.File => Image.file(
                File(path),
                fit: fit,
                errorBuilder: (context, _, i) => errorWidget,
              ),
              ImageType.Asset => Image.asset(
                path,
                errorBuilder: (context, _, i) => errorWidget,
                fit: fit,
              ),
            },
          );
        },
      ),
    );
  }
}

enum ImageType { Network, File, CachedNetwork, Asset }

class ImageError extends StatelessWidget {
  const ImageError({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(color: StyleRepo.grey.shade200),
      child: Center(child: Assets.images.sideBarBackground.image()),
    );
  }
}

class ImageLoading extends StatelessWidget {
  const ImageLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer.fromColors(
      baseColor: StyleRepo.grey[300]!,
      highlightColor: StyleRepo.grey[100]!,
      child: Container(
        color: StyleRepo.white,
        height: double.infinity,
        width: double.infinity,
      ),
    );
  }
}
