import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/models/user/center.dart';
import 'package:gopal/core/services/pagination/options/grid_view.dart';
import 'package:gopal/features/widgets/general_componenets/pages/general_page.dart';
import 'package:gopal/features/widgets/list_cards_loading.dart';

import '../widgets/center_card.dart';
import 'controller.dart';

class CentersPage extends GetView<CentersPageController> {
  const CentersPage({super.key});

  @override
  Widget build(BuildContext context) {
    return GeneralPage(
      titleText: controller.nav.title,
      withBackground: true,
      child: Obx(
        () {
          if (controller.isInitialized) {
            return GridViewPagination.alignedCount(
              tag: ControllersTags.centers_pager,
              fetchApi: controller.loadData,
              fromJson: CenterModel.fromJson,
              crossAxisCount: 2,
              mainAxisSpacing: 10,
              crossAxisSpacing: 10,
              padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
              initialLoading: const ListCardsLoading(),
              itemBuilder: (context, index, center) =>
                  CenterCard(center: center),
            );
          } else {
            return const ListCardsLoading();
          }
        },
      ),
    );
  }
}
