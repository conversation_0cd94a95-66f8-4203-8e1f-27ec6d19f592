import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/style/repo.dart';

import '../controller.dart';

class TermsAcception extends GetView<CreateAccountPageController> {
  const TermsAcception({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Obx(
          () => Checkbox(
            value: controller.isTermsAccepted,
            onChanged: (value) => controller.isTermsAccepted = value!,
          ),
        ),
        const Gap(4),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: context.textTheme.bodyMedium,
              children: [
                TextSpan(
                  text: "${LocaleKeys.accept_terms_message_1.tr()} ",
                ),
                TextSpan(
                  text: LocaleKeys.accept_terms_message_2.tr(),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () => Nav.to(Pages.terms),
                  style: const TextStyle(
                    color: StyleRepo.blueViolet,
                    fontWeight: FontWeight.w700,
                    decoration: TextDecoration.underline,
                  ),
                ),
                TextSpan(
                  text: "${LocaleKeys.comma.tr()} ",
                ),
                TextSpan(
                  text: "${LocaleKeys.accept_terms_message_3.tr()} ",
                ),
                TextSpan(
                  text: LocaleKeys.accept_terms_message_4.tr(),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () => Nav.to(Pages.privacy_policy),
                  style: const TextStyle(
                    color: StyleRepo.blueViolet,
                    fontWeight: FontWeight.w700,
                    decoration: TextDecoration.underline,
                  ),
                ),
                TextSpan(
                  text: " ${LocaleKeys.accept_terms_message_5.tr()}",
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
