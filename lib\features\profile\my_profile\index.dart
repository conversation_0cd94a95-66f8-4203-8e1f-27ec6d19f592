import 'package:easy_localization/easy_localization.dart' hide TextDirection;
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/localization.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/features/profile/my_profile/widgets/profile_field.dart';
import 'package:gopal/features/settings/usecases/points/index.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';
import 'package:gopal/features/widgets/general_componenets/pages/rounded_page.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import '../../../core/widgets/error_widget.dart';
import 'controller.dart';
import 'widgets/children.dart';
import 'widgets/loading.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    ProfilePageController controller = Get.put(ProfilePageController());
    return RoundedPage(
      title: Text(LocaleKeys.profile.tr()),
      child: RefreshIndicator(
        onRefresh: () async => await controller.refreshData(),
        child: Obx(() {
          if (controller.profile.loading) {
            return const MyProfileLoading();
          } else if (controller.profile.hasError) {
            return AppErrorWidget(error: controller.profile.error!);
          } else {
            return ListView(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              children: [
                Center(
                  child: AppImage(
                    path: controller.profile.value!.image?.medium ?? "",
                    type: ImageType.CachedNetwork,
                    height: 100,
                    width: 100,
                    decoration: const BoxDecoration(shape: BoxShape.circle),
                  ),
                ),
                const Gap(8),
                Text(
                  controller.profile.value!.name,
                  textAlign: TextAlign.center,
                  style: context.textTheme.titleLarge!.copyWith(
                    color: StyleRepo.grey.shade600,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      LocaleKeys.n_points.plural(
                        controller.profile.value!.points,
                        args: ["${controller.profile.value!.points}"],
                      ),
                      style: context.textTheme.titleMedium!.copyWith(
                        color: StyleRepo.berkeleyBlue,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    IconButton(
                      onPressed:
                          () => Get.bottomSheet(
                            const PointsGuidBottomSheet(),
                            isScrollControlled: true,
                          ),
                      icon: const Icon(Icons.info_outline),
                    ),
                  ],
                ),
                if (controller.profile.value!.email != null)
                  Text(
                    controller.profile.value!.email!,
                    style: context.textTheme.bodyMedium!.copyWith(
                      color: StyleRepo.grey,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                const Gap(12),
                ProfielField(
                  title: LocaleKeys.number.tr(),
                  trailing: InkWell(
                    onTap: () => Nav.to(Pages.change_phone),
                    borderRadius: BorderRadius.circular(12),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: StyleRepo.turquoise.withValues(alpha: .15),
                        border: Border.all(color: StyleRepo.turquoise),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: SvgIcon(Assets.icons.edit.path, size: 20),
                    ),
                  ),
                  child: Row(
                    children: [
                      Text(AppLocalization.ar.flagEmoji),
                      const Gap(8),
                      Directionality(
                        textDirection: TextDirection.ltr,
                        child: Text(controller.profile.value!.phone),
                      ),
                    ],
                  ),
                ),
                const Gap(16),
                const ProfileChildren(),
                const Gap(16),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: Get.width * .15),
                  child: AppElevatedButton(
                    onTap: () => controller.editProfile(),
                    child: Text(LocaleKeys.edit_profile.tr()),
                  ),
                ),
              ],
            );
          }
        }),
      ),
    );
  }
}
