// ignore_for_file: invalid_use_of_protected_member

import 'dart:math';

import 'package:get/get.dart';
import 'package:gopal/core/models/activity/constants/type.dart';
import 'package:gopal/core/models/activity/session.dart';
import 'package:gopal/core/models/book/book.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';

import '../../../core/services/rest_api/rest_api.dart';

class BookDetailsPageController extends GetxController {
  final int id;

  BookDetailsPageController(this.id) {
    bookRequest = Request(
      endPoint: EndPoints.book_details(id),
      fromJson: BookedActivity.fromJsonTypes,
    );
  }

  BookedActivity? get book {
    try {
      return (bookRequest.response!.data as BookedActivity);
    } catch (_) {
      return null;
    }
  }

  late Request<BookedActivity> bookRequest;

  loadData() async {
    await bookRequest.perform();
    if (!bookRequest.response!.success) return;

    if (book!.type == ActivityType.specific_days) {
      loadSpecificSessions(selectedDate.year, selectedDate.month);
    }
  }

  Future refreshData() async {
    bookRequest.reset();
    await loadData();
    _selectedDate.value = DateTime.now();
  }

  @override
  void onInit() {
    loadData();
    super.onInit();
  }

  @override
  void onClose() {
    bookRequest.stop();
    super.onClose();
  }

  //SECTION - Calendar
  ObsVar<Map<int, List<ActivitySession>>> calendarSessions = ObsVar(null);

  final Rx<DateTime> _selectedDate = DateTime.now().obs;
  DateTime get selectedDate => _selectedDate.value;
  set selectedDate(DateTime value) {
    _selectedDate.value = value;
    if (value.month != _selectedDate.value.month) {
      onMonthChanged(value, withSelectionDate: false);
    }
  }

  onMonthChanged(DateTime date, {bool withSelectionDate = true}) {
    if (withSelectionDate) {
      _selectedDate.value = date;
    }
    if (book!.type == ActivityType.specific_days) {
      loadSpecificSessions(date.year, date.month);
    }
  }

  refreshCalendar() {
    onMonthChanged(selectedDate, withSelectionDate: false);
  }

  RxSet<int> highlightedDays = RxSet();

  //!SECTION

  //SECTION - specific days sessions
  Request<ActivitySession>? specificSessionsRequest;

  loadSpecificSessions(int year, int month) async {
    calendarSessions.reset();
    specificSessionsRequest?.stop();
    specificSessionsRequest?.reset();
    highlightedDays.value = {};

    specificSessionsRequest = Request(
      endPoint: EndPoints.specific_session_values,
      params: {
        "sub_activity_id": book?.activity.id,
        "year": selectedDate.year,
        "month": selectedDate.month,
      },
    );

    ResponseModel response = await specificSessionsRequest!.perform();
    if (response.success) {
      List<ActivitySession> temp = response.data
          .map<ActivitySession>((e) => ActivitySession.fromJson(e))
          .toList();
      Map<int, List<ActivitySession>> map = {};
      for (var session in temp) {
        if (map[session.date.day] == null) {
          map[session.date.day] = [];
        }
        map[session.date.day]!.add(session);
      }
      highlightedDays.value = map.keys.toSet();
      calendarSessions.value = map;
      int firstDay = calendarSessions.value!.keys.toList().reduce(min);
      if (firstDay < DateTime.now().day) {
        firstDay = DateTime.now().day;
      }
      selectedDate = DateTime(selectedDate.year, selectedDate.month, firstDay);
    } else if (response.errorType is CANCELED) {
      return;
    } else {
      calendarSessions.error = response.message;
    }
  }

  //!SECTION
}
