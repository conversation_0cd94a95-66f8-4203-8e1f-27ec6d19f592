import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:url_launcher/url_launcher_string.dart';

class AboutSection extends StatelessWidget {
  const AboutSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: InkWell(
            onTap: () => launchUrlString("https://www.gopalsa.com"),
            child: Assets.images.about.image(),
          ),
        ),
        const Gap(12),
        Text(
          tr(LocaleKeys.gopal_home_message),
          style: context.textTheme.titleMedium!.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const Gap(12),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            InkWell(
              onTap: () => launchUrlString(
                  "https://www.facebook.com/share/15LqkPjp6H/?mibextid=LQQJ4d"),
              child: Assets.icons.facebook.svg(height: 50),
            ),
            const Gap(8),
            InkWell(
              onTap: () => launchUrlString(
                  "https://www.instagram.com/gopal.ksaa?igsh=eTJ3MDlxaHBjMjgy"),
              child: Assets.icons.instagram.svg(height: 50),
            ),
            const Gap(8),
            InkWell(
              onTap: () => launchUrlString(
                  "https://www.tiktok.com/@gopal.ksa?_t=ZS-8vUxosY4esK&_r=1"),
              child: const CircleAvatar(
                radius: 25,
                backgroundColor: StyleRepo.blueViolet,
                child: Icon(
                  Icons.tiktok,
                  color: StyleRepo.white,
                  size: 35,
                ),
              ),
            ),
            const Gap(8),
            InkWell(
              onTap: () => launchUrlString(
                  "https://www.snapchat.com/add/gopal.ksaa?share_id=M8D1fDEqPNE&locale=en-US"),
              child: const CircleAvatar(
                radius: 25,
                backgroundColor: StyleRepo.blueViolet,
                child: Icon(
                  Icons.snapchat,
                  color: StyleRepo.white,
                  size: 35,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
