import 'dart:developer';
import 'dart:isolate';

import '../models/exceptions.dart';

abstract class Parser {
  static dynamic parsingData<T>(
    var data,
    T Function(Map<String, dynamic> json) fromJson,
  ) {
    if (data is List) {
      List<T> parsed = [];
      try {
        for (var json in data) {
          parsed.add(fromJson(json));
        }
        log(parsed.runtimeType.toString());
        return parsed;
      } on TypeError catch (e) {
        String error =
            "${e.toString()}\n${e.stackTrace!.toString().split('\n').first}";
        log(error, name: "PARSER");

        throw ModellingException(error);
      } catch (e) {
        rethrow;
      }
    } else if (data is Map<String, dynamic>) {
      try {
        return fromJson(data);
      } on TypeError catch (e) {
        String error =
            "${e.toString()}\n${e.stackTrace!.toString().split('\n').first.replaceAll("#0", "").trim()}";
        log(error, name: "<PERSON>RSER");

        throw ModellingException(error);
      } catch (e) {
        rethrow;
      }
    } else {
      return null;
    }
  }

  static Future<dynamic> parsingDataIsolate<T>(
    var data,
    T Function(Map<String, dynamic> json) fromJson,
  ) async {
    return await Isolate.run(() => parsingData<T>(data, fromJson));
  }
}
