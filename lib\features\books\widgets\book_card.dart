import 'package:easy_localization/easy_localization.dart' hide TextDirection;
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/activity/constants/type.dart';
import 'package:gopal/core/models/book/book.dart';
import 'package:gopal/core/models/book/payment_status.dart';
import 'package:gopal/core/models/book/status.dart';
import 'package:gopal/core/models/rate.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/services/rest_api/rest_api.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/activities/my_activities/models/completed_book.dart';
import 'package:gopal/features/usecases/rating/index.dart';
import 'package:gopal/features/usecases/toast/toast.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/outlined_button.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';
import 'package:gopal/features/widgets/location_row.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:url_launcher/url_launcher_string.dart';

import '../../../core/style/assets/gen/assets.gen.dart';
import '../../activities/widgets/periodic_time_info.dart';

class BookCard extends StatelessWidget {
  final BookedActivity bookedActivity;
  final Widget? trailing;
  const BookCard({super.key, required this.bookedActivity, this.trailing});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => Nav.to(Pages.book, arguments: bookedActivity.id),
      child: Badge(
        backgroundColor: StyleRepo.blueViolet.withValues(alpha: .5),
        alignment: AlignmentDirectional.topEnd,
        largeSize: 25,
        offset: Offset(
          50 * (Directionality.of(context) == TextDirection.ltr ? -1 : 0),
          0,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 8),
        label: Text(
          "ID: ${bookedActivity.bookId}",
          style: context.textTheme.bodyMedium!.copyWith(
            color: StyleRepo.white,
            fontWeight: FontWeight.w700,
          ),
        ),
        child: Container(
          margin: const EdgeInsets.only(top: 20),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          decoration: BoxDecoration(
            color: StyleRepo.white,
            boxShadow: StyleRepo.elevation_2,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Text(
                      bookedActivity.child.firstName,
                      style: context.textTheme.bodyLarge!.copyWith(
                        color: StyleRepo.blueViolet,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                  ),
                  if (trailing != null) const Gap(12),
                  if (trailing != null) trailing!,
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    bookedActivity.activity.name,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: context.textTheme.bodyLarge!.copyWith(
                      color: StyleRepo.berkeleyBlue,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                  const Gap(6),
                  if (bookedActivity.status == BookStatus.pending &&
                      bookedActivity.status == BookStatus.in_progress)
                    _BookDateInfo(bookedActivity: bookedActivity),
                ],
              ),
              Divider(color: StyleRepo.grey.shade200),
              if (bookedActivity.nextSession != null)
                RichText(
                  text: TextSpan(
                    style: context.textTheme.bodyMedium,
                    children: [
                      TextSpan(
                        text: "${LocaleKeys.next_session.tr()}: ",
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                      TextSpan(
                        text: DateFormat.MMMMd(
                          EasyLocalization.of(
                            context,
                          )!.currentLocale!.languageCode,
                        ).format(bookedActivity.nextSession!),
                      ),
                      const WidgetSpan(child: SizedBox(width: 6)),
                      TextSpan(
                        text: TimeOfDay.fromDateTime(
                          bookedActivity.nextSession!,
                        ).format(context),
                      ),
                    ],
                  ),
                ),
              if (bookedActivity.nextSession != null)
                Divider(color: StyleRepo.grey.shade200),
              Row(
                children: [
                  AppImage(
                    path: bookedActivity.activity.center.profileImage.small,
                    type: ImageType.CachedNetwork,
                    height: 40,
                    width: 40,
                    decoration: const BoxDecoration(shape: BoxShape.circle),
                  ),
                  const Gap(12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          bookedActivity.activity.center.name,
                          style: context.textTheme.bodyLarge!.copyWith(
                            color: StyleRepo.blueViolet,
                            fontWeight: FontWeight.w700,
                          ),
                        ),
                        LocationRow(branch: bookedActivity.branch),
                      ],
                    ),
                  ),
                ],
              ),
              Divider(color: StyleRepo.grey.shade200),
              if (bookedActivity.status == BookStatus.in_progress ||
                  bookedActivity.status == BookStatus.pending)
                PaymentInfo(bookedActivity: bookedActivity),
              if (bookedActivity.status == BookStatus.completed)
                CompletedBookInfo(bookedActivity: bookedActivity),
              if (bookedActivity.status == BookStatus.rejected)
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Assets.icons.rejection.svg(),
                    const Gap(12),
                    Text(
                      "${LocaleKeys.rejected.tr()}: ",
                      style: context.textTheme.bodyMedium!.copyWith(
                        color: StyleRepo.red,
                        fontWeight: FontWeight.w700,
                      ),
                    ),
                    const Gap(12),
                    Expanded(child: Text(bookedActivity.reason ?? "")),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class PaymentInfo extends StatelessWidget {
  final BookedActivity bookedActivity;
  const PaymentInfo({super.key, required this.bookedActivity});

  @override
  Widget build(BuildContext context) {
    if (bookedActivity.paymentStatus == PaymentStatus.unpaid) {
      return AppOutlinedButton(
        height: 40,
        onTap: () async {
          Loading.show();
          ResponseModel response =
              await Request(
                endPoint: EndPoints.checkout_payment,
                method: RequestMethod.Post,
                body: {"book_transaction_id": bookedActivity.id},
              ).perform();
          Loading.dispose();
          if (response.success) {
            if (response.data[0]['redirect_url'] != null) {
              launchUrl(
                Uri.parse(response.data[0]['redirect_url']),
                mode: LaunchMode.externalApplication,
              );
            }
          } else {
            Toast.show(message: response.message);
          }
        },
        child: Text(tr(LocaleKeys.go_to_payment)),
      );
    } else if (bookedActivity.paymentStatus == PaymentStatus.hold) {
      return Row(
        children: [
          SvgIcon(Assets.icons.holding.path, color: StyleRepo.grey),
          const Gap(8),
          Text(
            tr(LocaleKeys.holding),
            style: const TextStyle(
              color: StyleRepo.grey,
              decoration: TextDecoration.underline,
            ),
          ),
        ],
      );
    } else if (bookedActivity.invoice != null) {
      return Align(
        alignment: AlignmentDirectional.centerEnd,
        child: TextButton(
          onPressed: () => launchUrlString(bookedActivity.invoice!),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                tr(LocaleKeys.invoice),
                style: const TextStyle(
                  color: StyleRepo.blueViolet,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Gap(8),
              const Icon(
                Icons.file_download_outlined,
                color: StyleRepo.blueViolet,
              ),
            ],
          ),
        ),
      );
    }
    return const SizedBox();
  }
}

class CompletedBookInfo extends StatelessWidget {
  const CompletedBookInfo({super.key, required this.bookedActivity});

  final BookedActivity bookedActivity;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        if ((bookedActivity as CompletedBook).certificate != null)
          Expanded(
            child: InkWell(
              onTap: () {
                launchUrl(
                  Uri.file((bookedActivity as CompletedBook).certificate!),
                  mode: LaunchMode.externalApplication,
                );
              },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Assets.icons.certificate.svg(),
                  const Gap(12),
                  Text(
                    LocaleKeys.certificate.tr(),
                    style: context.textTheme.bodyMedium!.copyWith(
                      color: StyleRepo.blueViolet,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ],
              ),
            ),
          ),
        Expanded(
          child: InkWell(
            onTap: () async {
              Review? result = await RateDialog.open(
                bookId: bookedActivity.id,
                center: bookedActivity.activity.center,
                rate: (bookedActivity as CompletedBook).rate,
              );

              if (result != null) {
                (bookedActivity as CompletedBook).addRate(result);
              }
            },
            child: Obx(
              () =>
                  (bookedActivity as CompletedBook).isRated
                      ? Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SvgIcon(
                            Assets.icons.star.path,
                            color: StyleRepo.yellow.shade600,
                            size: 23,
                          ),
                          const Gap(12),
                          Text(
                            "${(bookedActivity as CompletedBook).rate!.rate}",
                            style: context.textTheme.bodyMedium!.copyWith(
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      )
                      : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          SvgIcon(
                            Assets.icons.star.path,
                            color: StyleRepo.yellow.shade600,
                            size: 23,
                          ),
                          const Gap(12),
                          Text(
                            LocaleKeys.click_to_rate.tr(),
                            style: context.textTheme.bodyMedium!.copyWith(
                              fontWeight: FontWeight.w600,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ],
                      ),
            ),
          ),
        ),
      ],
    );
  }
}

class _BookDateInfo extends StatelessWidget {
  final BookedActivity bookedActivity;
  const _BookDateInfo({required this.bookedActivity});

  @override
  Widget build(BuildContext context) {
    // Numbered sessions one session
    if (bookedActivity.type == ActivityType.numbered_session &&
        bookedActivity.sessionsCount == 1) {
      return Text(
        bookedActivity.sessions.first.displayText(context),
        style: context.textTheme.bodyMedium!.copyWith(
          color: StyleRepo.grey.shade700,
        ),
      );
    }
    // Numbered sessions more than one session
    else if (bookedActivity.type == ActivityType.numbered_session &&
        bookedActivity.sessionsCount != 1) {
      return Text(
        LocaleKeys.number_of_sessions.plural(
          bookedActivity.sessionsCount!,
          args: ["${bookedActivity.sessionsCount}"],
        ),
      );
    }
    // Open session
    else if (bookedActivity.type == ActivityType.open_sessions) {
      return Text(bookedActivity.sessions.first.displayText(context));
    }
    // Custom session
    else if (bookedActivity.type == ActivityType.custom_session) {
      return Text(bookedActivity.sessions.first.displayText(context));
    }
    // Periodic
    else if (bookedActivity.type == ActivityType.periodic_days) {
      return PeriodicTimeInfo(periodic: bookedActivity.periodic!);
    }
    return const SizedBox();
  }
}
