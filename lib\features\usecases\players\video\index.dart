import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import 'controller.dart';

class VideoPlayerWidget extends StatelessWidget {
  final String video;
  final int? verseId;
  final Function()? onCloseToEnd;
  final bool autoPlay;
  final bool withAppBar;

  const VideoPlayerWidget({
    super.key,
    required this.video,
    this.verseId,
    this.onCloseToEnd,
    this.autoPlay = true,
    this.withAppBar = true,
  });

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    VideoPlayerWidgetController controller = Get.put(
        VideoPlayerWidgetController(video, onCloseToEnd, autoPlay),
        tag: video);
    return Scaffold(
      appBar: withAppBar ? AppBar() : null,
      backgroundColor: Colors.black,
      body: Obx(() {
        if (controller.isInitialized) {
          return Chewie(
            controller: controller.chewieController,
          );
        } else {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }
      }),
    );
  }
}
