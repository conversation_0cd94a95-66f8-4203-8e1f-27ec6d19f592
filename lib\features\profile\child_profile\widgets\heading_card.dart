import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/utils/date.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/outlined_button.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import '../controller.dart';
import '../models/child.dart';

class ChildProfileHeadingCard extends GetView<ChildProfilePageController> {
  final ChildDetails child;
  const ChildProfileHeadingCard(this.child, {super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Stack(
        children: [
          Container(
            margin: const EdgeInsets.only(top: 50),
            padding: const EdgeInsets.fromLTRB(12, 50, 12, 12),
            decoration: BoxDecoration(
              color: StyleRepo.white,
              borderRadius: BorderRadius.circular(20),
              boxShadow: StyleRepo.elevation_3,
            ),
            child: Column(
              children: [
                Text(
                  "${child.firstName} ${child.lastName}",
                  style: context.textTheme.titleLarge!.copyWith(
                    fontWeight: FontWeight.w600,
                    color: StyleRepo.berkeleyBlue,
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgIcon(Assets.icons.targetAge.path),
                    const Gap(8),
                    Text(
                      "${LocaleKeys.age.tr()}: ${child.birthdate.age}",
                      style: context.textTheme.bodyLarge,
                    ),
                  ],
                ),
                const Gap(12),
                AppOutlinedButton(
                  onTap: () => controller.editChildProfile(child),
                  height: 30,
                  width: 150,
                  child: Text(
                    LocaleKeys.edit_child.tr(),
                    style: const TextStyle(fontSize: 13),
                  ),
                )
              ],
            ),
          ),
          Align(
            alignment: Alignment.topCenter,
            child: AppImage(
              path: child.image?.small ?? "",
              type: ImageType.CachedNetwork,
              height: 90,
              width: 90,
              decoration: const BoxDecoration(shape: BoxShape.circle),
            ),
          ),
        ],
      ),
    );
  }
}
