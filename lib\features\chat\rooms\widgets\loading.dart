import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/widgets/shimmer_loading.dart';

class ChatRoomsLoading extends StatelessWidget {
  const ChatRoomsLoading({super.key});

  @override
  Widget build(BuildContext context) {
    return ListView.separated(
      itemCount: 12,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      separatorBuilder: (_, __) => const Divider(),
      itemBuilder: (_, __) => Row(
        children: [
          ShimmerWidget.card(
            height: 50,
            width: 50,
            borderRadius: BorderRadius.circular(25),
          ),
          const Gap(12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ShimmerWidget.card(
                  height: 25,
                  width: Get.width * .3,
                  borderRadius: BorderRadius.circular(5),
                ),
                const Gap(8),
                ShimmerWidget.card(
                  height: 15,
                  width: Get.width * .5,
                  borderRadius: BorderRadius.circular(5),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
