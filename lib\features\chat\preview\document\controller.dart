import 'package:flutter/material.dart';
import 'package:get/get.dart';

class DocumentPreviewPageController extends GetxController {
  final String path;

  DocumentPreviewPageController({required this.path});

  TextEditingController textController = TextEditingController();

  @override
  void onClose() {
    textController.dispose();
    super.onClose();
  }

  sendDocument() {
    Get.back(result: (path, textController.text));
  }
}
