import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/outlined_button.dart';

class PointsSelectionDialog extends StatelessWidget {
  final int maxPoints;
  final double pointValue;
  final int myPoints;

  PointsSelectionDialog(
      {super.key,
      required this.maxPoints,
      required this.pointValue,
      required this.myPoints});

  final Rx<int> _counter = 10.obs;
  int get counter => _counter.value;
  set counter(int value) {
    if (value < 0 || value > maxPoints) return;
    _counter.value = value;
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.width * .1),
        child: Material(
          color: Colors.transparent,
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                padding: const EdgeInsets.fromLTRB(16, 40, 16, 16),
                margin: const EdgeInsets.only(top: 50),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(35),
                  color: StyleRepo.white,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      LocaleKeys.redeem_points.tr(),
                      style: context.textTheme.titleMedium!.copyWith(
                        color: StyleRepo.grey.shade700,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Gap(12),
                    Text(
                      LocaleKeys.redeem_n_points_to_m_unit.tr(args: [
                        "10",
                        "${pointValue * 10}",
                        tr(LocaleKeys.s_a_r)
                      ]),
                      style: context.textTheme.bodyMedium!.copyWith(
                        color: StyleRepo.grey.shade700,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Gap(12),
                    Text(
                      LocaleKeys.you_have_x_and_you_can_redeem_y.tr(args: [
                        LocaleKeys.n_points.plural(myPoints),
                        LocaleKeys.n_points.plural((maxPoints ~/ 10) * 10),
                      ]),
                      style: context.textTheme.bodyMedium!.copyWith(
                        color: StyleRepo.grey.shade700,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Gap(12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        IconButton.filled(
                          onPressed: () => counter -= 10,
                          icon: const Icon(Icons.remove),
                        ),
                        SizedBox(
                          width: 50,
                          child: Obx(() => Text(
                                counter.toString(),
                                textAlign: TextAlign.center,
                              )),
                        ),
                        IconButton.filled(
                          onPressed: () => counter += 10,
                          icon: const Icon(Icons.add),
                        ),
                      ],
                    ),
                    const Gap(12),
                    Row(
                      children: [
                        Expanded(
                          child: AppOutlinedButton(
                            onTap: () => Get.back(result: 0),
                            child: Text(tr(LocaleKeys.skip)),
                          ),
                        ),
                        const Gap(12),
                        Expanded(
                          child: AppElevatedButton(
                            onTap: () => Get.back(result: counter),
                            child: Text(tr(LocaleKeys.confirm)),
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
              Assets.icons.profileSticker.svg(),
            ],
          ),
        ),
      ),
    );
  }
}
