import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/repo.dart';

import 'controller.dart';
import 'models/boarding_data.dart';

class BoardingPage extends StatelessWidget {
  const BoardingPage({super.key});

  @override
  Widget build(BuildContext context) {
    BoardingPageController controller = Get.put(BoardingPageController());
    return Scaffold(
      body: Stack(
        children: [
          SizedBox(
            width: Get.width,
            child: PageView.builder(
              controller: controller.pageController,
              itemCount: BoardingData.boarding.length,
              onPageChanged: (page) => controller.currentPage = page,
              itemBuilder: (context, index) {
                return Image.asset(
                  BoardingData.boarding[index].image,
                  fit: BoxFit.cover,
                );
              },
            ),
          ),
          Positioned(
            bottom: 0,
            right: 0,
            left: 0,
            child: Container(
              height: 250,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 24),
              decoration: const BoxDecoration(
                color: StyleRepo.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(40),
                  topRight: Radius.circular(40),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Obx(
                      () => AnimatedSwitcher(
                        duration: 400.milliseconds,
                        transitionBuilder: (child, animation) =>
                            ScaleTransition(
                          scale: animation,
                          child: FadeTransition(
                            opacity: animation,
                            child: child,
                          ),
                        ),
                        child: FittedBox(
                          fit: BoxFit.contain,
                          child: Text(
                            BoardingData.boarding[controller.currentPage].title
                                .tr(),
                            key: ValueKey(BoardingData
                                .boarding[controller.currentPage].title),
                            textAlign: TextAlign.center,
                            style: context.textTheme.headlineMedium!.copyWith(
                              color: StyleRepo.berkeleyBlue,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        height: 10,
                        child: ListView.separated(
                          shrinkWrap: true,
                          scrollDirection: Axis.horizontal,
                          itemCount: 3,
                          separatorBuilder: (_, __) => const Gap(8),
                          itemBuilder: (context, index) => Obx(
                            () => AnimatedContainer(
                              duration: 300.milliseconds,
                              height: 10,
                              width: index == controller.currentPage ? 100 : 10,
                              decoration: BoxDecoration(
                                color: index == controller.currentPage
                                    ? StyleRepo.blueViolet
                                    : StyleRepo.grey.shade300,
                                borderRadius: BorderRadius.circular(5),
                              ),
                            ),
                          ),
                        ),
                      ),
                      const Gap(12),
                      TextButton(
                        onPressed: () => controller.next(),
                        child: Text(LocaleKeys.next.tr()),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
