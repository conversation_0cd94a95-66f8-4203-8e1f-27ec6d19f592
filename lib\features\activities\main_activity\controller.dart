import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:gopal/core/services/rest_api/rest_api.dart';

import '../usecases/booking/booking.dart';
import 'models/nav.dart';

class MainActivityPageController extends GetxController with Booking {
  final MainActivityPageNav nav;

  MainActivityPageController(this.nav);

  Future<ResponseModel> fetchSubactivities(int page, CancelToken cancel) async {
    return await Request(
      endPoint: EndPoints.sub_activities,
      params: {"activity_id": nav.id, "page": page},
      cancelToken: cancel,
    ).perform();
  }
}
