import 'package:easy_localization/easy_localization.dart';
import 'package:gopal/core/localization/strings.dart';

enum MyActivitiesTabs {
  pending,
  progress,
  completed,
  favourite,
  ;

  String get text {
    switch (this) {
      case pending:
        return LocaleKeys.pending.tr();
      case progress:
        return LocaleKeys.progress.tr();
      case completed:
        return LocaleKeys.completed.tr();
      case favourite:
        return LocaleKeys.favourite.tr();
    }
  }
}
