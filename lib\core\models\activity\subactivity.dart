import '../constants/discount.dart';
import '../user/center.dart';

class SubActivity {
  late int id;
  late int capacity;
  late DiscountType? discountType;
  late num? discount;
  late String name;
  late String description;
  late CenterModel center;
  late bool isAvailable;

  bool get hasDiscount => discount != null && discountType != null;

  SubActivity({
    required this.id,
    required this.capacity,
    required this.name,
    required this.description,
    required this.center,
    required this.isAvailable,
    this.discount,
    this.discountType,
  });

  SubActivity.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    capacity = json["capacity"] ?? 0;
    discountType = json["discount_type"] != null
        ? DiscountType.fromString(json["discount_type"])
        : null;
    discount = json["discount"]?.toDouble();
    name = json["name"] ?? "";
    description = json["description"] ?? "";
    center = CenterModel.fromJson(json["provider_branch"]["provider"]);
    isAvailable = json['is_available'] ?? false;
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "capacity": capacity,
        "has_discount": hasDiscount,
        "name": name,
        "description": description,
        "provider_branch": center.toJson(),
      };
}
