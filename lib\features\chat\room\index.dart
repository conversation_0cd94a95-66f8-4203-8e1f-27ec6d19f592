import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/services/pagination/options/list_view.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';

import '../../../core/models/chat/messages/messages.dart';
import 'controller.dart';
import 'widgets/message.dart';
import 'widgets/sending_bar.dart';

class ChatRoomPage extends GetView<ChatRoomPageController> {
  const ChatRoomPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: StyleRepo.white,
        title: Row(
          children: [
            AppImage(
              path: controller.chatRoom.image.small,
              type: ImageType.CachedNetwork,
              width: 40,
              height: 40,
              decoration: const BoxDecoration(shape: BoxShape.circle),
            ),
            const Gap(8),
            Expanded(
              child: Text(
                controller.chatRoom.name,
                style: context.textTheme.titleMedium!.copyWith(
                  color: StyleRepo.berkeleyBlue,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          image: DecorationImage(
            image: AssetImage(
              Assets.images.chatBackground.path,
            ),
            fit: BoxFit.cover,
          ),
        ),
        child: Column(
          children: [
            Expanded(
              child: ListViewPagination<Message>.separated(
                tag: ControllersTags.messages_pager,
                fetchApi: controller.fetchMessages,
                fromJson: ReceivedMessage.fromJson,
                reverse: true,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                separatorBuilder: (_, __) => const Gap(12),
                itemBuilder: (context, index, message) =>
                    MessageBubble(message: message),
              ),
            ),
            const SendingBar(),
          ],
        ),
      ),
    );
  }
}
