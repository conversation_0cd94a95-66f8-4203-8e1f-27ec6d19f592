import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/features/widgets/general_componenets/app_bars/general_app_bar.dart';

import '../profile/models/nav.dart';
import 'controller.dart';

class CentersOnMapPage extends StatelessWidget {
  const CentersOnMapPage({super.key});

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    CentersOnMapPageController controller =
        Get.put(CentersOnMapPageController());
    return Scaffold(
      appBar: GeneralAppBar(title: Text(tr(LocaleKeys.discover_centers))),
      body: controller.centersRequest.listBuilder(
        withRefresh: true,
        builder: (context, centers) {
          Set<Marker> markers = {};
          for (var center in centers) {
            for (var location in center.locations) {
              markers.add(
                Marker(
                  markerId: MarkerId(location.toString()),
                  position: location,
                  infoWindow: InfoWindow(
                    title: center.name,
                    onTap: () => Nav.to(Pages.center,
                        arguments: CenterProfilePageNav(center.id)),
                  ),
                ),
              );
            }
          }
          return GoogleMap(
            markers: markers,
            initialCameraPosition: CameraPosition(
              target: controller.location ?? markers.first.position,
              zoom: 12,
            ),
            myLocationEnabled: true,
            myLocationButtonEnabled: true,
          );
        },
      ),
    );
  }
}
