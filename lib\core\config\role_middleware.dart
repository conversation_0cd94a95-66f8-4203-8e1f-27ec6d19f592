import 'package:get/get.dart';

import '../widgets/guest_bottom_sheet.dart';
import 'app_builder.dart';
import 'role.dart';

abstract class RoleMiddleware {
  static bool get checkIfGuest {
    AppBuilder appBuilder = Get.find();
    return appBuilder.role is Guest;
  }

  static bool get guestForbidden {
    if (checkIfGuest) {
      Get.bottomSheet(const GuestBottomSheet(), isScrollControlled: true);
      return true;
    } else {
      return false;
    }
  }

  static bool get checkIfNotRegistered {
    AppBuilder appBuilder = Get.find();
    return appBuilder.role is UnregisteredUser;
  }
}

class BlockGuest extends GetMiddleware {
  @override
  GetPage? onPageCalled(GetPage? page) =>
      RoleMiddleware.guestForbidden ? null : page;
}

class TransformToGuest extends GetMiddleware {
  @override
  GetPage? onPageCalled(GetPage? page) {
    if (RoleMiddleware.checkIfNotRegistered) {
      Get.find<AppBuilder>().setRole(const Guest());
    }
    return page;
  }
}
