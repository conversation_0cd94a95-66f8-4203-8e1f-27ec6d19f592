import 'package:country_picker/country_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/utils/validator.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

class PhoneNumberField extends StatelessWidget {
  final Function(Country country) onCountrySelection;
  final String initCountry;
  final TextEditingController controller;
  final GlobalKey<FormState> formKey;

  PhoneNumberField({
    super.key,
    required this.onCountrySelection,
    this.initCountry = "SA",
    required this.controller,
    required this.formKey,
  }) {
    _selectedCountry = Country.parse(initCountry).obs;
  }

  late final Rx<Country> _selectedCountry;
  Country get selectedCountry => _selectedCountry.value;
  set selectedCountry(Country value) => _selectedCountry.value = value;

  @override
  Widget build(BuildContext context) {
    return Form(
      key: form<PERSON><PERSON>,
      child: Directionality(
        textDirection: TextDirection.ltr,
        child: TextForm<PERSON>ield(
          controller: controller,
          keyboardType: const TextInputType.numberWithOptions(
              signed: false, decimal: true),
          inputFormatters: <TextInputFormatter>[
            FilteringTextInputFormatter.digitsOnly
          ],
          validator: Validator.saPhoneNumber,
          decoration: InputDecoration(
            prefixIcon: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                InkWell(
                  onTap: () {
                    showCountryPicker(
                      context: context,
                      showPhoneCode: true,
                      onSelect: (country) {
                        selectedCountry = country;
                        onCountrySelection(country);
                      },
                    );
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Row(
                      children: [
                        Obx(
                          () => Text(
                            selectedCountry.flagEmoji,
                            style: const TextStyle(fontSize: 24),
                          ),
                        ),
                        const Gap(10),
                        SvgIcon(Assets.icons.arrowDown.path),
                      ],
                    ),
                  ),
                ),
                SvgIcon(Assets.icons.phone.path),
                const Gap(10),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
