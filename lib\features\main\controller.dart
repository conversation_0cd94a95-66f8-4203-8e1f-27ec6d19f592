import 'package:get/get.dart';
import 'package:gopal/core/config/role_middleware.dart';
import 'package:gopal/features/main/models/destinations.dart';

import '../categories/categories/controller.dart';
import '../activities/my_activities/controller.dart';
import '../profile/my_profile/controller.dart';

class MainPageController extends GetxController {
  final Rx<MainDestinations> _currentDestination = MainDestinations.home.obs;
  MainDestinations get currentDestination => _currentDestination.value;
  set currentDestination(MainDestinations value) {
    if (value == MainDestinations.my_activities ||
        value == MainDestinations.profile) {
      if (RoleMiddleware.guestForbidden) return;
    }
    var prev = currentDestination;
    if (prev != value) {
      if (prev == MainDestinations.categories) {
        Get.delete<CategoriesPageController>();
      } else if (prev == MainDestinations.my_activities) {
        Get.delete<MyActivitiesPageController>();
      } else if (prev == MainDestinations.profile) {
        Get.delete<ProfilePageController>();
      }
    }
    _currentDestination.value = value;
  }

  @override
  void onInit() {
    if (Get.arguments != null && Get.arguments['destination'] != null) {
      currentDestination = Get.arguments['destination'];
    }
    super.onInit();
  }
}
