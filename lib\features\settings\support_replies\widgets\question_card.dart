import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import '../models/suppoert_reply.dart';

class QuestionCard extends StatelessWidget {
  final SupportReplyModel reply;
  const QuestionCard({super.key, required this.reply});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: StyleRepo.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: StyleRepo.elevation,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    SvgIcon(
                      Assets.icons.calenderOutlined.path,
                    ),
                    const Gap(4),
                    Text(
                      DateFormat.yMd(EasyLocalization.of(context)!
                              .currentLocale!
                              .languageCode)
                          .format(reply.createdAt),
                    ),
                  ],
                ),
                const Gap(12),
                Text(reply.question),
              ],
            ),
          ),
          if (reply.answer != null) const Divider(),
          if (reply.answer != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AppImage(
                        path: Assets.images.logo.path,
                        type: ImageType.Asset,
                        width: 30,
                        height: 30,
                      ),
                      const Gap(12),
                      Text(DateFormat.yMd(EasyLocalization.of(context)!
                              .currentLocale!
                              .languageCode)
                          .format(reply.updatedAt!)),
                    ],
                  ),
                  const Gap(12),
                  Text(reply.answer ?? "test")
                ],
              ),
            ),
        ],
      ),
    );
  }
}
