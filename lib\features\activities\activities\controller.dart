import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:gopal/core/services/rest_api/rest_api.dart';

import 'models/nav.dart';

class ActivitiesPageController extends GetxController {
  final ActivitiesPageNav nav;
  ActivitiesPageController(this.nav);

  Future<ResponseModel> loadData(int page, CancelToken cancel) async {
    Request request = Request(
      endPoint: nav.endPoint!,
      params: nav.params,
      cancelToken: cancel,
    );
    if (request.params != null) {
      request.params!["page"] = page;
    } else {
      request.params = {"page": page};
    }
    return await request.perform();
  }
}
