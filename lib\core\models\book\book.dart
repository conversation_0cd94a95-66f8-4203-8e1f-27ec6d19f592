// ignore_for_file: library_private_types_in_public_api

import 'package:gopal/core/models/activity/constants/type.dart';
import 'package:gopal/core/models/activity/session.dart';
import 'package:gopal/core/models/book/status.dart';
import 'package:gopal/features/activities/my_activities/models/completed_book.dart';
import 'package:gopal/features/center/profile/models/center_details.dart';

import '../activity/subactivity.dart';
import '../activity/periodic/main_periodic.dart';
import '../image.dart';
import '../user/main_child.dart';
import 'payment_status.dart';

class BookedActivity {
  late int id;
  late String bookId;
  late _BookChild child;
  late CenterBranch branch;
  late SubActivity activity;
  late ActivityType type;
  late BookStatus status;
  late List<ActivitySession> sessions;
  String? qrContent;
  int? sessionsCount;
  MainPeriodicModel? periodic;
  DateTime? nextSession;
  String? reason;
  PaymentStatus? paymentStatus;
  String? invoice;

  BookedActivity.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    bookId = json['book_number'] ?? "98765";
    child = _BookChild.fromJson(json['child']);

    qrContent = json['qr_content'];

    status = BookStatus.fromString(json['status']);
    type = ActivityType.fromString(json['bookable_type']);

    if (json['bookable']?['sub_activity'] != null) {
      activity = SubActivity.fromJson(json['bookable']['sub_activity']);
      branch = CenterBranch.fromJson(
        json['bookable']['sub_activity']['provider_branch'],
      );
    } else {
      activity = SubActivity.fromJson(json['bookable']);
      branch = CenterBranch.fromJson(json['bookable']['provider_branch']);
    }

    sessionsCount = json['bookable']['number_of_sessions'];
    sessions = [];
    if (json['bookable']['sessions'] != null) {
      sessions =
          json['bookable']['sessions']
              .map<ActivitySession>((e) => ActivitySession.fromJson(e))
              .toList();
    } else if (json['session_values'] != null) {
      sessions =
          json['session_values']
              .map<ActivitySession>((e) => ActivitySession.fromJson(e))
              .toList();
    } else if (json['customSession'] != null) {
      sessions =
          json['customSession']['sessions']
              .map<ActivitySession>((e) => ActivitySession.fromJson(e))
              .toList();
    }

    if (type == ActivityType.custom_session && sessions.length > 1) {
      ActivitySession second = sessions.removeAt(1);
      sessions.first.to = second.to;
    }

    if (type == ActivityType.periodic_days) {
      periodic = MainPeriodicModel.parseTypes(json['bookable']);
    }

    if (status == BookStatus.in_progress) {
      nextSession = DateTime.tryParse(json['next_session'] ?? "");
    }

    reason = json['reason'];

    paymentStatus = PaymentStatus.tryParse(json['payment_status']);
    invoice = json['pdf_path'];
  }

  factory BookedActivity.fromJsonTypes(Map<String, dynamic> json) {
    if (BookStatus.fromString(json['status']) == BookStatus.completed) {
      return CompletedBook.fromJson(json);
    } else {
      return BookedActivity.fromJson(json);
    }
  }
}

class _BookChild extends MainChild {
  late ImageModel image;

  _BookChild.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    try {
      image = ImageModel.fromJson(json['media']['profile'].first);
    } catch (_) {
      image = ImageModel.empty();
    }
  }
}
