import 'dart:developer';

import 'package:app_links/app_links.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../features/center/profile/models/nav.dart';
import '../../routes.dart';

linkListener() {
  AppLinks().uriLinkStream.listen(
    (link) => DeepLinks.linkHandler(link, false),
    onError: (e) {
      log('$e');
    },
  );
}

enum ShareType {
  subactivity,
  provider;

  static ShareType fromString(String s) {
    for (ShareType type in ShareType.values) {
      if (type.name == s) {
        return type;
      }
    }
    throw "";
  }
}

class DeepLinks {
  static Uri deepLinkBasaeUrl = Uri(
    scheme: "https",
    host: "gopalsa.com",
    path: "app",
  );

  static String subactivityLink(int id) {
    Uri link = Uri(
      scheme: deepLinkBasaeUrl.scheme,
      host: deepLinkBasaeUrl.host,
      path: "${deepLinkBasaeUrl.path}/${ShareType.subactivity.name}",
      queryParameters: {"id": "$id"},
    );

    return 'Press this link and see this activity on GoPal\n\n$link';
  }

  static String centerLink(int id) {
    Uri link = Uri(
      scheme: deepLinkBasaeUrl.scheme,
      host: deepLinkBasaeUrl.host,
      path: "${deepLinkBasaeUrl.path}/${ShareType.provider.name}",
      queryParameters: {"id": "$id"},
    );

    return 'Press this link and see this AMAZING center on GoPal\n\n$link';
  }

  static Future<bool> initialize() async {
    Uri? link = await AppLinks().getInitialLink();
    linkListener();
    if (link != null) {
      log('open app by $link');
      return linkHandler(link, true);
    } else {
      return false;
    }
  }

  static bool canHandleLink(Uri? link) =>
      link != null &&
      ShareType.values.map((e) => e.name).contains(link.pathSegments[1]);

  static bool linkHandler(Uri? link, bool terminated) {
    if (canHandleLink(link)) {
      switch (ShareType.fromString(link!.pathSegments[1])) {
        case ShareType.subactivity:
          var params = link.queryParameters;
          if (!params.containsKey("id") ||
              int.tryParse(params['id']!) == null) {
            return false;
          }
          Pages page = Pages.activity_details;
          final nav = int.parse(params['id']!);

          if (terminated) {
            Nav.offUntil(page, (_) => false, arguments: nav);
          } else {
            Nav.to(page, arguments: nav, preventDuplicates: false);
          }
          break;
        case ShareType.provider:
          var params = link.queryParameters;
          if (!params.containsKey("id") ||
              int.tryParse(params['id']!) == null) {
            return false;
          }
          Pages page = Pages.center;
          final nav = CenterProfilePageNav(int.parse(params['id']!));

          if (terminated) {
            Nav.offUntil(page, (_) => false, arguments: nav);
          } else {
            Nav.to(page, arguments: nav, preventDuplicates: false);
          }
          break;
      }
      return true;
    } else {
      return false;
    }
  }

  static bool pageBack(BuildContext context) {
    if (ModalRoute.of(context)?.impliesAppBarDismissal ?? false) {
      Get.back();
    } else {
      Nav.offUntil(Pages.home, (_) => false);
    }
    return false;
  }
}
