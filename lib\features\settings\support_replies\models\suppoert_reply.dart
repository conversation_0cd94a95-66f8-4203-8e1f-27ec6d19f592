class SupportReplyModel {
  int id;
  String question;
  DateTime createdAt;
  DateTime? updatedAt;
  String? answer;

  SupportReplyModel({
    required this.id,
    required this.question,
    required this.createdAt,
    this.updatedAt,
    this.answer,
  });

  factory SupportReplyModel.fromJson(Map<String, dynamic> json) =>
      SupportReplyModel(
        id: json["id"],
        question: json["question"],
        answer: json["answer"],
        createdAt: DateTime.parse(json['created_at']),
        updatedAt: DateTime.parse(json['updated_at']),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "question": question,
        "answer": answer,
      };
}
