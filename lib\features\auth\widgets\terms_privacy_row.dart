import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/routes.dart';

class TermsPrivacyRow extends StatelessWidget {
  const TermsPrivacyRow({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: 16,
        vertical: MediaQuery.of(context).viewPadding.bottom + 16,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          InkWell(
            onTap: () => Nav.to(Pages.terms),
            child: Text(
              LocaleKeys.terms_and_conditions.tr(),
              style: const TextStyle(decoration: TextDecoration.underline),
            ),
          ),
          InkWell(
            onTap: () => Nav.to(Pages.privacy_policy),
            child: Text(
              LocaleKeys.privacy_policy.tr(),
              style: const TextStyle(decoration: TextDecoration.underline),
            ),
          ),
        ],
      ),
    );
  }
}
