import 'package:feedback/feedback.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../controller.dart';

class FeedbackButton extends StatelessWidget {
  const FeedbackButton({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.put(FeedBackController(context));
    return Obx(
      () => Positioned(
        top: controller.offset.dy,
        left: controller.offset.dx,
        child: Draggable(
          feedback: const SizedBox(),
          onDragUpdate: (details) {
            controller.offset += details.delta;
          },
          child: FloatingActionButton(
            onPressed: () {
              BetterFeedback.of(context)
                  .show((UserFeedback feedback) => controller.submit(feedback));
            },
            child: const Icon(Icons.edit),
          ),
        ),
      ),
    );
  }
}
