import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';

class PersonsSelectionDialog extends StatelessWidget {
  final int minPersons, maxPersons;

  PersonsSelectionDialog({
    super.key,
    required this.minPersons,
    required this.maxPersons,
  }) {
    _counter = minPersons.obs;
  }

  late final Rx<int> _counter;
  int get counter => _counter.value;
  set counter(int value) {
    if (value < minPersons || value > maxPersons) return;
    _counter.value = value;
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: Get.width * .1),
        child: Material(
          color: Colors.transparent,
          child: Stack(
            alignment: Alignment.topCenter,
            children: [
              Container(
                padding: const EdgeInsets.fromLTRB(16, 40, 16, 16),
                margin: const EdgeInsets.only(top: 50),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(35),
                  color: StyleRepo.white,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      LocaleKeys.select_persons_count.tr(),
                      style: context.textTheme.titleMedium!.copyWith(
                        color: StyleRepo.grey.shade700,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Gap(12),
                    Text(
                      LocaleKeys
                          .select_the_number_of_people_participating_in_the_activity
                          .tr(),
                      style: context.textTheme.bodyMedium!.copyWith(
                        color: StyleRepo.grey.shade700,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const Gap(12),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        IconButton.filled(
                          onPressed: () => counter--,
                          icon: const Icon(Icons.remove),
                        ),
                        SizedBox(
                          width: 50,
                          child: Obx(() => Text(
                                counter.toString(),
                                textAlign: TextAlign.center,
                              )),
                        ),
                        IconButton.filled(
                          onPressed: () => counter++,
                          icon: const Icon(Icons.add),
                        ),
                      ],
                    ),
                    const Gap(12),
                    AppElevatedButton(
                      onTap: () => Get.back(result: counter),
                      child: Text(tr(LocaleKeys.confirm)),
                    ),
                  ],
                ),
              ),
              Assets.icons.profileSticker.svg(),
            ],
          ),
        ),
      ),
    );
  }
}
