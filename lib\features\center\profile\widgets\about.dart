import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:readmore/readmore.dart';

class AboutCenter extends StatelessWidget {
  final String description;
  const AboutCenter({super.key, required this.description});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: StyleRepo.white,
        boxShadow: StyleRepo.elevation_3,
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleKeys.about_center.tr(),
            style: context.textTheme.titleMedium,
          ),
          const Gap(8),
          ReadMoreText(
            description,
            delimiter: "...",
            semanticsLabel: " ",
            preDataText: "",
            style: TextStyle(color: StyleRepo.grey.shade700),
            moreStyle: context.textTheme.bodyMedium!.copyWith(
              color: StyleRepo.turquoise.withValues(alpha: .5),
              fontWeight: FontWeight.w600,
            ),
            lessStyle: context.textTheme.bodyMedium!.copyWith(
              color: StyleRepo.turquoise.withValues(alpha: .5),
              fontWeight: FontWeight.w600,
            ),
            trimMode: TrimMode.Line,
            trimLines: 3,
            trimCollapsedText: LocaleKeys.read_more.tr(),
            trimExpandedText: " ${LocaleKeys.read_less.tr()}",
          ),
        ],
      ),
    );
  }
}
