import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/models/chat/chat_room.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:timeago/timeago.dart' as timeago;

class ChatCard extends StatelessWidget {
  final ChatRoom chatRoom;
  const ChatCard({super.key, required this.chatRoom});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () => Nav.to(Pages.chat_room, arguments: chatRoom),
      child: Row(
        children: [
          AppImage(
            path: chatRoom.image.small,
            type: ImageType.CachedNetwork,
            height: 50,
            width: 50,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
            ),
          ),
          const Gap(12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        chatRoom.name,
                        style: context.textTheme.titleMedium!.copyWith(
                          color: StyleRepo.berkeleyBlue,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const Gap(8),
                    Text(
                      timeago.format(
                        chatRoom.date,
                        locale: EasyLocalization.of(context)!
                            .currentLocale!
                            .languageCode,
                      ),
                    )
                  ],
                ),
                const Gap(8),
                Text(
                  chatRoom.messagePreview,
                  style: context.textTheme.labelMedium!.copyWith(
                    color: StyleRepo.grey,
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
