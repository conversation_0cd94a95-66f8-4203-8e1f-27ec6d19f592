import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/services/pagination/options/list_view.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/features/activities/models/sub_activity_data.dart';
import 'package:gopal/features/widgets/general_componenets/pages/rounded_page.dart';

import 'controller.dart';
import 'widgets/card.dart';

class MainActivityPage extends GetView<MainActivityPageController> {
  const MainActivityPage({super.key});

  @override
  Widget build(BuildContext context) {
    return RoundedPage(
      title: Text(tr(LocaleKeys.activity_summary)),
      child: ListViewPagination.separated(
        tag: ControllersTags.subactivities_pager,
        fetchApi: controller.fetchSubactivities,
        fromJson: SubActivityData.from<PERSON>son,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        separatorBuilder: (_, __) => const Gap(12),
        emptyWidget: Center(child: Assets.lotties.comingSoon.lottie()),
        itemBuilder: (context, index, subactivity) =>
            SubActivityCard(subActivity: subactivity),
      ),
    );
    // return RoundedPage(
    //   title: Text(tr(LocaleKeys.activity_summary)),
    //   child: controller.subActivities.listBuilder(
    //     withRefresh: true,
    //     builder: (context, subActivities) {
    //       if (subActivities.isEmpty) {
    //         return Center(child: Assets.lotties.comingSoon.lottie());
    //       }
    //       return ListView.separated(
    //         padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
    //         itemCount: subActivities.length,
    //         separatorBuilder: (_, __) => const Gap(12),
    //         itemBuilder: (context, index) {
    //           return SubActivityCard(subActivity: subActivities[index]);
    //         },
    //       );
    //     },
    //   ),
    // );
  }
}
