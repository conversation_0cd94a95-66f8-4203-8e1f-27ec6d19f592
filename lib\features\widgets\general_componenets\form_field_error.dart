import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';

class FormFieldError<T> extends StatelessWidget {
  final String error;

  const FormFieldError({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        const Gap(10),
        Text(
          error,
          textAlign: TextAlign.center,
          style: context.textTheme.bodySmall!
              .copyWith(color: Theme.of(context).colorScheme.error),
        )
      ],
    );
  }
}
