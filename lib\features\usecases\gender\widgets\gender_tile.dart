import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/models/constants/gender.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/usecases/gender/widgets/gender_avatar.dart';

class GenderTile extends StatelessWidget {
  final Gender gender;
  final Widget? trailing;
  const GenderTile(this.gender, {super.key, this.trailing});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
      decoration: BoxDecoration(
        gradient: gradient(gender),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Builder(
        builder: (context) {
          if (gender.isBoth) {
            return Row(
              children: [
                Container(
                  width: 35,
                  height: 35,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    image: DecorationImage(
                      image: Assets.images.boy.provider(),
                      fit: BoxFit.cover,
                    ),
                    gradient: photoGradient(Gender.male),
                    boxShadow: StyleRepo.elevation_2,
                  ),
                ),
                const Gap(8),
                Text(
                  Gender.male.text,
                  style: context.textTheme.bodyMedium!.copyWith(
                    fontWeight: FontWeight.w600,
                    color: StyleRepo.grey.shade700,
                  ),
                ),
                const Spacer(),
                Text(
                  "&",
                  style: context.textTheme.bodyMedium!.copyWith(
                    fontWeight: FontWeight.w600,
                    color: StyleRepo.grey.shade700,
                  ),
                ),
                const Spacer(),
                Container(
                  width: 35,
                  height: 35,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    image: DecorationImage(
                      image: Assets.images.girl.provider(),
                      fit: BoxFit.cover,
                    ),
                    gradient: photoGradient(Gender.female),
                    boxShadow: StyleRepo.elevation_2,
                  ),
                ),
                const Gap(8),
                Text(
                  Gender.female.text,
                  style: context.textTheme.bodyMedium!.copyWith(
                    fontWeight: FontWeight.w600,
                    color: StyleRepo.grey.shade700,
                  ),
                ),
                const Spacer(),
                if (trailing != null) trailing!,
              ],
            );
          }
          return Row(
            children: [
              GenderAvatar(gender: gender),
              const Gap(12),
              Text(
                gender.text,
                style: context.textTheme.bodyMedium!.copyWith(
                  fontWeight: FontWeight.w600,
                  color: StyleRepo.grey.shade700,
                ),
              ),
              const Spacer(),
              if (trailing != null) trailing!,
            ],
          );
        },
      ),
    );
  }

  LinearGradient gradient(Gender gender) {
    if (gender.isFemale) {
      return LinearGradient(
        colors: [
          const Color(0xFFFF7DBA).withValues(alpha: .7),
          const Color(0xFFFFC8C8),
        ],
        begin: AlignmentDirectional.topStart,
        end: AlignmentDirectional.bottomEnd,
      );
    } else if (gender.isMale) {
      return const LinearGradient(
        colors: [Color(0xFF1FA9E2), Color(0xFF5CC7DF)],
        begin: AlignmentDirectional.topStart,
        end: AlignmentDirectional.bottomEnd,
      );
    } else {
      return LinearGradient(
        colors: [
          const Color(0xFF1FA9E2),
          const Color(0xFF5CC7DF),
          const Color(0xFFFF7DBA).withValues(alpha: .7),
          const Color(0xFFFFC8C8),
        ],
        begin: AlignmentDirectional.topStart,
        end: AlignmentDirectional.bottomEnd,
      );
    }
  }

  LinearGradient photoGradient(gender) {
    if (gender.isMale) {
      return const LinearGradient(
        colors: [Color(0xFF1EA9E2), Color(0xFF63CADF)],
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
      );
    } else {
      return const LinearGradient(
        colors: [Color(0xFFFF7ABA), Color(0xFFFFC8C8)],
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
      );
    }
  }
}
