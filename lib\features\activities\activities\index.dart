import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/models/activity/main_activity.dart';
import 'package:gopal/core/services/pagination/options/list_view.dart';
import 'package:gopal/features/activities/widgets/activity_card.dart';
import 'package:gopal/features/widgets/general_componenets/pages/general_page.dart';
import 'package:gopal/features/widgets/list_cards_loading.dart';

import 'controller.dart';
import 'models/nav.dart';

class ActivitiesPage extends GetView<ActivitiesPageController> {
  const ActivitiesPage({super.key});

  @override
  String? get tag {
    return (Get.arguments as ActivitiesPageNav).title;
  }

  @override
  Widget build(BuildContext context) {
    return GeneralPage(
      titleText: controller.nav.title,
      withBackground: true,
      child: ListViewPagination.separated(
        tag: "${tag}_${ControllersTags.activities_pager}",
        fetchApi: controller.loadData,
        fromJson: MainActivity.fromJson,
        padding: const EdgeInsets.fromLTRB(16, 24, 16, 16),
        initialLoading: const ListCardsLoading(),
        separatorBuilder: (_, __) => const Gap(12),
        itemBuilder: (context, index, activity) =>
            ActivityCard(activity: activity),
      ),
    );
  }
}
