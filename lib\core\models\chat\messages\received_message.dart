import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../config/app_builder.dart';
import '../../image.dart';
import 'types.dart';

import 'message.dart';

class ReceivedMessage extends Message {
  late int id;
  late ReceivedMessageContent content;
  late int senderId;

  AppBuilder get appBuilder => Get.find<AppBuilder>();

  @override
  get isMine => senderId == Get.find<AppBuilder>().user.value!.id;

  ReceivedMessage.fromJson(Map<String, dynamic> json) : super.fromJson(json) {
    id = json['id'];
    senderId = json['sender']['participantable_id'];
    switch (type) {
      case MessageType.text:
        content = ReceivedTextMessageContent(text: json['content'] ?? "");
        break;
      case MessageType.voice:
        content = ReceivedVoiceMessageContent(
          filePath: json['media']['media'].first['original_url'],
          waves: (json['metadata']?['waves'] as List<dynamic>?)
                  ?.map<double>((wave) => wave * 1.0)
                  .toList() ??
              List.generate(20, (index) => 0.0),
        );
      case MessageType.media:
        content = ReceivedMediaMessageContent(
          content: json['content'],
          mediaType: MessageMediaType.fromString(
              json['metadata']?['media_type'] ?? ""),
          files: json['media']['media']
              .map<ImageModel>((e) => ImageModel.fromJson(e))
              .toList(),
        );
      case MessageType.location:
        content = ReceivedLocationMessageContent(
          location: LatLng(
            json['metadata']['lat'] * 1.0,
            json['metadata']['lng'] * 1.0,
          ),
          map: json['media'] is! List
              ? ImageModel.fromJson(json['media']['media'].first)
              : ImageModel.empty(),
        );
      default:
    }
  }
}

abstract class ReceivedMessageContent {
  ReceivedTextMessageContent get asText => this as ReceivedTextMessageContent;
  ReceivedVoiceMessageContent get asVoice =>
      this as ReceivedVoiceMessageContent;
  ReceivedMediaMessageContent get asMedia =>
      this as ReceivedMediaMessageContent;
  ReceivedLocationMessageContent get asLocation =>
      this as ReceivedLocationMessageContent;
}

class ReceivedTextMessageContent extends ReceivedMessageContent {
  String text;

  ReceivedTextMessageContent({required this.text});
}

class ReceivedVoiceMessageContent extends ReceivedMessageContent {
  String filePath;
  List<double> waves;

  ReceivedVoiceMessageContent({required this.filePath, required this.waves});
}

class ReceivedMediaMessageContent extends ReceivedMessageContent {
  String? content;
  List<ImageModel> files;
  MessageMediaType mediaType;
  // String? fileName;

  ReceivedMediaMessageContent({
    this.content,
    required this.files,
    required this.mediaType,
  });
}

class ReceivedLocationMessageContent extends ReceivedMessageContent {
  LatLng location;
  ImageModel map;

  ReceivedLocationMessageContent({required this.location, required this.map});
}
