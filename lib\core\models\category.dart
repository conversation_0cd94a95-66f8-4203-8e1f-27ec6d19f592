class Category {
  late int id;
  late String name;
  String? svg;

  Category({
    required this.id,
    required this.name,
    this.svg,
  });

  Category.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    name = json["name"];
    try {
      svg = json["media"]?['icon'].first['original_url'] ?? "";
    } catch (_) {
      svg = "";
    }
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "svg": svg,
      };
}
