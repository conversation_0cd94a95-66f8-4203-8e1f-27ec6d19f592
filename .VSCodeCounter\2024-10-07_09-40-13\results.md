# Summary

Date : 2024-10-07 09:40:13

Directory d:\\work\\ixCoders\\current\\go_pal\\go_pal_flutter\\lib

Total : 352 files,  22320 codes, 778 comments, 2219 blanks, all 25317 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Dart | 352 | 22,320 | 778 | 2,219 | 25,317 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 352 | 22,320 | 778 | 2,219 | 25,317 |
| . (Files) | 1 | 89 | 1 | 13 | 103 |
| core | 111 | 6,303 | 595 | 926 | 7,824 |
| core (Files) | 1 | 249 | 6 | 12 | 267 |
| core\\config | 4 | 258 | 24 | 62 | 344 |
| core\\constants | 3 | 22 | 0 | 10 | 32 |
| core\\demo | 2 | 125 | 9 | 14 | 148 |
| core\\localization | 4 | 755 | 5 | 23 | 783 |
| core\\models | 26 | 973 | 9 | 171 | 1,153 |
| core\\models (Files) | 5 | 122 | 0 | 21 | 143 |
| core\\models\\activity | 4 | 184 | 0 | 29 | 213 |
| core\\models\\activity (Files) | 2 | 71 | 0 | 10 | 81 |
| core\\models\\activity\\periodic | 2 | 113 | 0 | 19 | 132 |
| core\\models\\book | 2 | 93 | 1 | 18 | 112 |
| core\\models\\chat | 6 | 295 | 1 | 64 | 360 |
| core\\models\\chat (Files) | 1 | 25 | 0 | 4 | 29 |
| core\\models\\chat\\messages | 5 | 270 | 1 | 60 | 331 |
| core\\models\\constants | 3 | 72 | 0 | 11 | 83 |
| core\\models\\notifications | 3 | 97 | 7 | 15 | 119 |
| core\\models\\notifications (Files) | 2 | 63 | 7 | 7 | 77 |
| core\\models\\notifications\\data | 1 | 34 | 0 | 8 | 42 |
| core\\models\\user | 3 | 110 | 0 | 13 | 123 |
| core\\services | 46 | 2,654 | 292 | 385 | 3,331 |
| core\\services (Files) | 5 | 58 | 172 | 33 | 263 |
| core\\services\\feedback | 3 | 122 | 0 | 19 | 141 |
| core\\services\\feedback (Files) | 1 | 49 | 0 | 11 | 60 |
| core\\services\\feedback\\widgets | 2 | 73 | 0 | 8 | 81 |
| core\\services\\firebase_messaging | 5 | 252 | 16 | 33 | 301 |
| core\\services\\firebase_messaging (Files) | 2 | 163 | 4 | 22 | 189 |
| core\\services\\firebase_messaging\\constants | 2 | 53 | 12 | 6 | 71 |
| core\\services\\firebase_messaging\\widgets | 1 | 36 | 0 | 5 | 41 |
| core\\services\\local_notifications | 1 | 94 | 2 | 11 | 107 |
| core\\services\\notifications_click | 1 | 77 | 3 | 4 | 84 |
| core\\services\\notifications_counter | 1 | 48 | 0 | 8 | 56 |
| core\\services\\pagination | 9 | 751 | 20 | 73 | 844 |
| core\\services\\pagination (Files) | 1 | 133 | 3 | 17 | 153 |
| core\\services\\pagination\\options | 3 | 464 | 17 | 35 | 516 |
| core\\services\\pagination\\widgets | 5 | 154 | 0 | 21 | 175 |
| core\\services\\player | 3 | 118 | 4 | 22 | 144 |
| core\\services\\register_fcm | 2 | 66 | 1 | 12 | 79 |
| core\\services\\rest_api | 14 | 885 | 65 | 122 | 1,072 |
| core\\services\\rest_api (Files) | 2 | 293 | 12 | 20 | 325 |
| core\\services\\rest_api\\constants | 3 | 68 | 25 | 29 | 122 |
| core\\services\\rest_api\\handlers | 2 | 114 | 2 | 8 | 124 |
| core\\services\\rest_api\\logger | 1 | 38 | 0 | 6 | 44 |
| core\\services\\rest_api\\models | 3 | 152 | 19 | 37 | 208 |
| core\\services\\rest_api\\utilitis | 2 | 121 | 6 | 11 | 138 |
| core\\services\\rest_api\\widgets | 1 | 99 | 1 | 11 | 111 |
| core\\services\\state_management | 2 | 183 | 9 | 48 | 240 |
| core\\style | 8 | 584 | 125 | 147 | 856 |
| core\\style (Files) | 3 | 152 | 19 | 19 | 190 |
| core\\style\\assets | 3 | 415 | 106 | 122 | 643 |
| core\\style\\assets (Files) | 1 | 3 | 0 | 0 | 3 |
| core\\style\\assets\\gen | 2 | 412 | 106 | 122 | 640 |
| core\\style\\utils | 2 | 17 | 0 | 6 | 23 |
| core\\utils | 11 | 345 | 124 | 67 | 536 |
| core\\widgets | 6 | 338 | 1 | 35 | 374 |
| features | 239 | 15,910 | 182 | 1,277 | 17,369 |
| features\\activities | 37 | 2,972 | 40 | 188 | 3,200 |
| features\\activities\\activities | 4 | 68 | 0 | 12 | 80 |
| features\\activities\\activities (Files) | 3 | 62 | 0 | 11 | 73 |
| features\\activities\\activities\\models | 1 | 6 | 0 | 1 | 7 |
| features\\activities\\activitiy_details | 17 | 1,769 | 18 | 107 | 1,894 |
| features\\activities\\activitiy_details (Files) | 3 | 458 | 18 | 55 | 531 |
| features\\activities\\activitiy_details\\models | 4 | 191 | 0 | 15 | 206 |
| features\\activities\\activitiy_details\\widgets | 10 | 1,120 | 0 | 37 | 1,157 |
| features\\activities\\my_activities | 10 | 480 | 5 | 48 | 533 |
| features\\activities\\my_activities (Files) | 2 | 237 | 5 | 19 | 261 |
| features\\activities\\my_activities\\models | 3 | 52 | 0 | 10 | 62 |
| features\\activities\\my_activities\\widgets | 5 | 191 | 0 | 19 | 210 |
| features\\activities\\widgets | 6 | 655 | 17 | 21 | 693 |
| features\\auth | 23 | 1,004 | 3 | 95 | 1,102 |
| features\\auth\\create_account | 8 | 405 | 1 | 36 | 442 |
| features\\auth\\create_account (Files) | 3 | 165 | 0 | 22 | 187 |
| features\\auth\\create_account\\models | 2 | 5 | 1 | 2 | 8 |
| features\\auth\\create_account\\widgets | 3 | 235 | 0 | 12 | 247 |
| features\\auth\\login | 3 | 166 | 0 | 13 | 179 |
| features\\auth\\login (Files) | 2 | 117 | 0 | 10 | 127 |
| features\\auth\\login\\widgets | 1 | 49 | 0 | 3 | 52 |
| features\\auth\\signup | 4 | 105 | 0 | 14 | 119 |
| features\\auth\\signup (Files) | 3 | 101 | 0 | 13 | 114 |
| features\\auth\\signup\\models | 1 | 4 | 0 | 1 | 5 |
| features\\auth\\verification | 6 | 268 | 2 | 26 | 296 |
| features\\auth\\verification (Files) | 3 | 175 | 2 | 16 | 193 |
| features\\auth\\verification\\models | 1 | 4 | 0 | 2 | 6 |
| features\\auth\\verification\\widgets | 2 | 89 | 0 | 8 | 97 |
| features\\auth\\widgets | 2 | 60 | 0 | 6 | 66 |
| features\\boarding | 3 | 171 | 0 | 16 | 187 |
| features\\boarding (Files) | 2 | 147 | 0 | 12 | 159 |
| features\\boarding\\models | 1 | 24 | 0 | 4 | 28 |
| features\\books | 9 | 1,009 | 11 | 55 | 1,075 |
| features\\books\\book_details | 8 | 732 | 6 | 48 | 786 |
| features\\books\\book_details (Files) | 3 | 197 | 6 | 30 | 233 |
| features\\books\\book_details\\widgets | 5 | 535 | 0 | 18 | 553 |
| features\\books\\widgets | 1 | 277 | 5 | 7 | 289 |
| features\\categories | 10 | 367 | 0 | 43 | 410 |
| features\\categories\\categories | 5 | 158 | 0 | 19 | 177 |
| features\\categories\\categories (Files) | 2 | 58 | 0 | 9 | 67 |
| features\\categories\\categories\\widgets | 3 | 100 | 0 | 10 | 110 |
| features\\categories\\category | 5 | 209 | 0 | 24 | 233 |
| features\\categories\\category (Files) | 3 | 139 | 0 | 18 | 157 |
| features\\categories\\category\\models | 1 | 5 | 0 | 2 | 7 |
| features\\categories\\category\\widgets | 1 | 65 | 0 | 4 | 69 |
| features\\center | 22 | 982 | 0 | 90 | 1,072 |
| features\\center\\centers | 4 | 86 | 0 | 16 | 102 |
| features\\center\\centers (Files) | 3 | 82 | 0 | 15 | 97 |
| features\\center\\centers\\models | 1 | 4 | 0 | 1 | 5 |
| features\\center\\models | 1 | 8 | 0 | 3 | 11 |
| features\\center\\profile | 12 | 663 | 0 | 53 | 716 |
| features\\center\\profile (Files) | 3 | 167 | 0 | 16 | 183 |
| features\\center\\profile\\models | 2 | 68 | 0 | 11 | 79 |
| features\\center\\profile\\widgets | 7 | 428 | 0 | 26 | 454 |
| features\\center\\reviews | 3 | 54 | 0 | 11 | 65 |
| features\\center\\widgets | 2 | 171 | 0 | 7 | 178 |
| features\\chat | 25 | 2,448 | 29 | 210 | 2,687 |
| features\\chat\\preview | 11 | 586 | 1 | 63 | 650 |
| features\\chat\\preview\\document | 3 | 134 | 0 | 14 | 148 |
| features\\chat\\preview\\images | 3 | 176 | 0 | 17 | 193 |
| features\\chat\\preview\\location | 2 | 120 | 1 | 13 | 134 |
| features\\chat\\preview\\video | 3 | 156 | 0 | 19 | 175 |
| features\\chat\\room | 10 | 1,689 | 28 | 132 | 1,849 |
| features\\chat\\room (Files) | 3 | 521 | 24 | 92 | 637 |
| features\\chat\\room\\widgets | 7 | 1,168 | 4 | 40 | 1,212 |
| features\\chat\\room\\widgets (Files) | 3 | 543 | 1 | 24 | 568 |
| features\\chat\\room\\widgets\\messages | 4 | 625 | 3 | 16 | 644 |
| features\\chat\\rooms | 4 | 173 | 0 | 15 | 188 |
| features\\chat\\rooms (Files) | 2 | 61 | 0 | 9 | 70 |
| features\\chat\\rooms\\widgets | 2 | 112 | 0 | 6 | 118 |
| features\\create_child | 7 | 685 | 8 | 47 | 740 |
| features\\create_child (Files) | 3 | 214 | 8 | 27 | 249 |
| features\\create_child\\models | 2 | 96 | 0 | 10 | 106 |
| features\\create_child\\widgets | 2 | 375 | 0 | 10 | 385 |
| features\\filters | 13 | 942 | 36 | 75 | 1,053 |
| features\\filters (Files) | 2 | 302 | 3 | 28 | 333 |
| features\\filters\\models | 3 | 94 | 0 | 14 | 108 |
| features\\filters\\models (Files) | 2 | 69 | 0 | 9 | 78 |
| features\\filters\\models\\categories.dart | 1 | 25 | 0 | 5 | 30 |
| features\\filters\\widgets | 8 | 546 | 33 | 33 | 612 |
| features\\home | 6 | 605 | 11 | 40 | 656 |
| features\\home (Files) | 2 | 275 | 11 | 19 | 305 |
| features\\home\\models | 1 | 68 | 0 | 9 | 77 |
| features\\home\\widgets | 3 | 262 | 0 | 12 | 274 |
| features\\main | 6 | 374 | 7 | 23 | 404 |
| features\\main (Files) | 2 | 92 | 0 | 8 | 100 |
| features\\main\\models | 1 | 34 | 0 | 4 | 38 |
| features\\main\\widgets | 3 | 248 | 7 | 11 | 266 |
| features\\notifications | 3 | 126 | 1 | 12 | 139 |
| features\\notifications (Files) | 2 | 54 | 1 | 8 | 63 |
| features\\notifications\\widgets | 1 | 72 | 0 | 4 | 76 |
| features\\profile | 24 | 1,477 | 9 | 124 | 1,610 |
| features\\profile\\change_phone | 2 | 78 | 1 | 9 | 88 |
| features\\profile\\child_profile | 11 | 685 | 3 | 59 | 747 |
| features\\profile\\child_profile (Files) | 3 | 255 | 3 | 27 | 285 |
| features\\profile\\child_profile\\models | 3 | 84 | 0 | 11 | 95 |
| features\\profile\\child_profile\\widgets | 5 | 346 | 0 | 21 | 367 |
| features\\profile\\edit_profile | 5 | 292 | 4 | 27 | 323 |
| features\\profile\\edit_profile (Files) | 3 | 115 | 4 | 21 | 140 |
| features\\profile\\edit_profile\\models | 1 | 5 | 0 | 2 | 7 |
| features\\profile\\edit_profile\\widgets | 1 | 172 | 0 | 4 | 176 |
| features\\profile\\my_profile | 6 | 422 | 1 | 29 | 452 |
| features\\profile\\my_profile (Files) | 2 | 210 | 1 | 17 | 228 |
| features\\profile\\my_profile\\models | 1 | 15 | 0 | 2 | 17 |
| features\\profile\\my_profile\\widgets | 3 | 197 | 0 | 10 | 207 |
| features\\settings | 15 | 451 | 3 | 66 | 520 |
| features\\settings\\about | 2 | 41 | 0 | 9 | 50 |
| features\\settings\\help | 2 | 41 | 0 | 9 | 50 |
| features\\settings\\privacy | 2 | 14 | 1 | 6 | 21 |
| features\\settings\\support_replies | 4 | 152 | 1 | 15 | 168 |
| features\\settings\\support_replies (Files) | 2 | 45 | 1 | 7 | 53 |
| features\\settings\\support_replies\\models | 1 | 27 | 0 | 4 | 31 |
| features\\settings\\support_replies\\widgets | 1 | 80 | 0 | 4 | 84 |
| features\\settings\\terms | 2 | 41 | 0 | 9 | 50 |
| features\\settings\\usecases | 3 | 162 | 1 | 18 | 181 |
| features\\settings\\usecases\\support | 3 | 162 | 1 | 18 | 181 |
| features\\settings\\usecases\\support (Files) | 2 | 134 | 1 | 13 | 148 |
| features\\settings\\usecases\\support\\widgets | 1 | 28 | 0 | 5 | 33 |
| features\\splash_screen | 2 | 36 | 3 | 8 | 47 |
| features\\usecases | 19 | 1,499 | 10 | 109 | 1,618 |
| features\\usecases\\book_custom_session | 3 | 231 | 8 | 18 | 257 |
| features\\usecases\\book_custom_session (Files) | 2 | 224 | 8 | 15 | 247 |
| features\\usecases\\book_custom_session\\models | 1 | 7 | 0 | 3 | 10 |
| features\\usecases\\children_selection | 2 | 261 | 1 | 23 | 285 |
| features\\usecases\\children_selection (Files) | 1 | 81 | 1 | 16 | 98 |
| features\\usecases\\children_selection\\widgets | 1 | 180 | 0 | 7 | 187 |
| features\\usecases\\confirmation | 1 | 102 | 0 | 5 | 107 |
| features\\usecases\\gender | 3 | 395 | 0 | 16 | 411 |
| features\\usecases\\gender (Files) | 1 | 133 | 0 | 6 | 139 |
| features\\usecases\\gender\\widgets | 2 | 262 | 0 | 10 | 272 |
| features\\usecases\\phone_field | 1 | 80 | 0 | 5 | 85 |
| features\\usecases\\pictures | 1 | 93 | 0 | 6 | 99 |
| features\\usecases\\players | 3 | 106 | 1 | 16 | 123 |
| features\\usecases\\players (Files) | 1 | 17 | 0 | 2 | 19 |
| features\\usecases\\players\\video | 2 | 89 | 1 | 14 | 104 |
| features\\usecases\\rating | 2 | 137 | 0 | 11 | 148 |
| features\\usecases\\toast | 3 | 94 | 0 | 9 | 103 |
| features\\usecases\\toast (Files) | 2 | 39 | 0 | 4 | 43 |
| features\\usecases\\toast\\widgets | 1 | 55 | 0 | 5 | 60 |
| features\\widgets | 15 | 762 | 11 | 76 | 849 |
| features\\widgets (Files) | 6 | 259 | 1 | 27 | 287 |
| features\\widgets\\backgrounds | 1 | 40 | 0 | 3 | 43 |
| features\\widgets\\general_componenets | 8 | 463 | 10 | 46 | 519 |
| features\\widgets\\general_componenets (Files) | 3 | 68 | 0 | 10 | 78 |
| features\\widgets\\general_componenets\\app_bars | 1 | 71 | 8 | 12 | 91 |
| features\\widgets\\general_componenets\\buttons | 2 | 179 | 2 | 14 | 195 |
| features\\widgets\\general_componenets\\pages | 2 | 145 | 0 | 10 | 155 |
| test | 1 | 18 | 0 | 3 | 21 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)