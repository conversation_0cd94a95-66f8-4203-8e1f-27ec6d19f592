import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/repo.dart';

import '../../usecases/gender/widgets/gender_tile.dart';
import '../controller.dart';

class GenderFilter extends GetView<FiltersPageController> {
  const GenderFilter({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Gap(24),
        Text(
          LocaleKeys.gender.tr(),
          style: context.textTheme.titleMedium!.copyWith(
            fontWeight: FontWeight.w700,
          ),
        ),
        const Gap(6),
        GestureDetector(
          onTap: () async => await controller.pickGender(),
          child: Obx(() {
            if (controller.gender.hasData) {
              return GenderTile(
                controller.gender.value!,
                trailing: IconButton(
                  onPressed: () => controller.gender.value = null,
                  style: ButtonStyle(
                    side: const WidgetStatePropertyAll(
                      BorderSide(color: StyleRepo.red),
                    ),
                    backgroundColor: WidgetStatePropertyAll(
                      StyleRepo.red.shade100,
                    ),
                  ),
                  icon: const Icon(Icons.close, color: StyleRepo.red),
                ),
              );
            } else {
              return Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(
                  vertical: 12,
                  horizontal: 16,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: StyleRepo.blueViolet),
                ),
                child: Text(
                  LocaleKeys.select_gender.tr(),
                  style: context.textTheme.bodyMedium!.copyWith(
                    fontWeight: FontWeight.w600,
                    color: StyleRepo.blueViolet,
                  ),
                ),
              );
            }
          }),
        ),
      ],
    );
  }
}
