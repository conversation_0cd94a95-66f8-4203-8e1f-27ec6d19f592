// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/style/repo.dart';

const Duration _kAnimationDuration = Duration(milliseconds: 300);

class AppElevatedButton extends StatelessWidget {
  final Widget child;
  final Function() onTap;
  bool withLoading;
  final double height;
  final double? width;
  final double horizontalPadding;
  final bool enabled;

  final Rx<bool> _loading = false.obs;
  bool get loading => _loading.value;
  set loading(bool value) => _loading.value = value;

  AppElevatedButton({
    super.key,
    required this.onTap,
    required this.child,
    this.withLoading = true,
    this.height = 50,
    this.horizontalPadding = 8,
    this.width = double.infinity,
    this.enabled = true,
  }) {
    if (onTap is! Future Function()) {
      withLoading = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      child: InkWell(
        borderRadius: BorderRadius.circular(height / 2),
        onTap: !enabled
            ? null
            : () async {
                if (!withLoading) {
                  onTap();
                } else {
                  if (loading) return;
                  loading = true;
                  await onTap();
                  loading = false;
                }
              },
        child: AnimatedOpacity(
          duration: 300.milliseconds,
          opacity: enabled ? 1 : .5,
          child: AnimatedContainer(
            duration: _kAnimationDuration,
            padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
            height: height,
            width: width,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(height / 2),
              gradient: const LinearGradient(
                colors: [
                  StyleRepo.amythest,
                  StyleRepo.blueViolet,
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
            alignment: Alignment.center,
            child: Obx(
              () => AnimatedSwitcher(
                duration: 300.milliseconds,
                transitionBuilder: (child, animation) {
                  return FadeTransition(opacity: animation, child: child);
                },
                child: loading
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 3,
                          color: StyleRepo.white,
                        ),
                      )
                    : IconTheme(
                        data: const IconThemeData(color: StyleRepo.white),
                        child: DefaultTextStyle(
                          style: context.textTheme.titleMedium!
                              .copyWith(color: StyleRepo.white),
                          child: child,
                        ),
                      ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
