import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/services/pagination/options/list_view.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';

import '../../widgets/favourite_activity_card.dart';
import '../controller.dart';
import '../models/favourite_activity.dart';
import 'loading.dart';

class MyFavouritesActivities extends StatelessWidget {
  const MyFavouritesActivities({super.key});

  @override
  Widget build(BuildContext context) {
    MyActivitiesPageController controller = Get.find();
    return ListViewPagination.separated(
      tag: ControllersTags.my_favourites_activities,
      fromJson: FavouriteActivity.fromJson,
      fetchApi: controller.fetchFavourites,
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      separatorBuilder: (_, __) => const Gap(12),
      initialLoading: const BooksLoading(),
      itemBuilder: (context, index, activity) {
        return FavouriteActivityCard(
          activity: activity,
          trailing: IconButton(
            onPressed: () => controller.removeFromFavourite(activity),
            icon: Assets.icons.filledHeart.svg(height: 23),
          ),
        );
      },
    );
  }
}
