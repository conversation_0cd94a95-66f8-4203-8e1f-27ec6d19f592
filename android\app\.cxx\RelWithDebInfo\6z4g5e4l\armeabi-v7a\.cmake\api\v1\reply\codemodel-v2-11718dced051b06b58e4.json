{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-RelWithDebInfo-d0094a50bb2071803777.json", "minimumCMakeVersion": {"string": "3.6.0"}, "projectIndex": 0, "source": "."}], "name": "RelWithDebInfo", "projects": [{"directoryIndexes": [0], "name": "Project"}], "targets": []}], "kind": "codemodel", "paths": {"build": "D:/work/ixCoders/current/go_pal/go_pal_flutter/android/app/.cxx/RelWithDebInfo/6z4g5e4l/armeabi-v7a", "source": "C:/src/versions/3.29.3/packages/flutter_tools/gradle/src/main/groovy"}, "version": {"major": 2, "minor": 3}}