import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/config/app_builder.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/home/<USER>/ad.dart';

import '../controller.dart';

class HomeTextSlider extends StatelessWidget {
  const HomeTextSlider({super.key});

  @override
  Widget build(BuildContext context) {
    HomePageController controller = Get.find();
    return controller.adsRequest.listBuilder(
      loader: (context) => const SizedBox(),
      errorBuilder: (_, __) => const SizedBox(),
      builder: (context, data) {
        List ads = List.from(
          data.where((element) => element.type == AdType.text),
        );
        if (ads.isEmpty) {
          return const SizedBox();
        }
        return CarouselSlider.builder(
          itemCount: ads.length,
          options: CarouselOptions(
            autoPlay: true,
            autoPlayAnimationDuration: 3.seconds,
            autoPlayInterval: 7.seconds,
            height: 70,
            viewportFraction: 1,
          ),
          itemBuilder: (context, index, _) {
            return InkWell(
              onTap: () => controller.adClick(ads[index]),
              child: Container(
                width: Get.width,
                height: 50,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                color: StyleRepo.turquoise.withValues(alpha: .35),
                alignment: Alignment.center,
                child: RichText(
                  textAlign: TextAlign.center,
                  text: TextSpan(
                    style: context.textTheme.bodyMedium!.copyWith(height: 1),
                    children: [
                      TextSpan(
                        text:
                            (ads[index].text ??
                                "Amet culpa quis qui minim esse ipsum sunt Lorem id voluptate nulla Lorem dolor eu.") +
                            " ",
                        style: const TextStyle(height: 1),
                      ),
                      WidgetSpan(
                        child: Padding(
                          //TODO - Dirty code
                          padding:
                              Get.find<AppBuilder>().currentLocale.isArabic
                                  ? const EdgeInsets.all(4.0)
                                  : EdgeInsets.zero,
                          child: const Icon(
                            Icons.arrow_forward_rounded,
                            color: StyleRepo.blueViolet,
                            size: 18,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }
}
