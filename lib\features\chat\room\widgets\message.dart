import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/models/chat/messages/messages.dart';
import 'package:gopal/core/style/repo.dart';

import '../controller.dart';
import 'messages/location_message.dart';
import 'messages/media_message_content.dart';
import 'messages/text_message_content.dart';
import 'messages/voice_message_content.dart';

class MessageBubble extends StatelessWidget {
  final Message message;
  const MessageBubble({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    ChatRoomPageController controller = Get.find();
    return Column(
      crossAxisAlignment:
          message.isMine ? CrossAxisAlignment.end : CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment:
              message.isMine ? MainAxisAlignment.end : MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            _MessageBubbleContent(message: message),
            if (message is SendMessage) const Gap(8),
            if (message is SendMessage)
              Obx(() {
                if (message.asSendMessage.status == MessageStatus.error) {
                  return IconButton(
                    style: const ButtonStyle(
                      backgroundColor: WidgetStatePropertyAll(StyleRepo.red),
                      iconColor: WidgetStatePropertyAll(StyleRepo.white),
                    ),
                    onPressed:
                        () => controller.resendMessage(message.asSendMessage),
                    icon: const Icon(Icons.refresh),
                  );
                }
                return const SizedBox();
              }),
          ],
        ),
        const Gap(6),
        if (message is ReceivedMessage)
          Text(
            TimeOfDay.fromDateTime(message.date).format(context),
            style: context.textTheme.labelMedium!.copyWith(
              color: StyleRepo.grey.shade600,
            ),
          ),
        if (message is SendMessage)
          Obx(() {
            if ((message as SendMessage).status == MessageStatus.waiting) {
              return Icon(
                Icons.watch_later_outlined,
                size: 15,
                color: StyleRepo.grey.shade700,
              );
            } else {
              return const SizedBox();
            }
          }),
      ],
    );
  }
}

class _MessageBubbleContent extends StatelessWidget {
  const _MessageBubbleContent({required this.message});

  final Message message;

  @override
  Widget build(BuildContext context) {
    return ProgressIndicatorTheme(
      data: ProgressIndicatorThemeData(
        color: message.isMine ? StyleRepo.white : null,
      ),
      child: IconTheme(
        data: IconThemeData(color: message.isMine ? StyleRepo.white : null),
        child: DefaultTextStyle(
          style: context.textTheme.bodyMedium!.copyWith(
            color: message.isMine ? StyleRepo.white : null,
          ),
          child: Container(
            constraints: BoxConstraints(
              minWidth: 50,
              minHeight: 30,
              maxWidth: MediaQuery.of(context).size.width * .7,
            ),
            decoration: BoxDecoration(
              color:
                  message.isMine
                      ? StyleRepo.blueViolet
                      : StyleRepo.grey.shade100,
              borderRadius: BorderRadiusDirectional.only(
                topStart: const Radius.circular(14),
                topEnd: const Radius.circular(14),
                bottomEnd:
                    message.isMine ? Radius.zero : const Radius.circular(14),
                bottomStart:
                    message.isMine ? const Radius.circular(14) : Radius.zero,
              ),
            ),
            child: switch (message.type) {
              MessageType.text => TextMessageContent(message: message),
              MessageType.voice => VoiceMessageContent(message: message),
              MessageType.media => MediaMessageContent(message: message),
              MessageType.location => LocationMessageContent(message: message),
              _ => const SizedBox(),
            },
          ),
        ),
      ),
    );
  }
}
