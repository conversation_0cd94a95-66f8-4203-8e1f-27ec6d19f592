import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/error_widget.dart';
import 'package:gopal/features/filters/controller.dart';

import '../controller.dart';

class SubCategoriesList extends GetView<CategoryPageController> {
  const SubCategoriesList({super.key});

  @override
  Widget build(BuildContext context) {
    FiltersPageController filtersController = Get.find();
    return Obx(
      () {
        if (filtersController.subcategories.loading) {
          return const LinearProgressIndicator();
        } else if (filtersController.subcategories.hasError) {
          return FieldErrorWidget(
              error: filtersController.subcategories.error!);
        } else {
          if (filtersController.subcategories.isEmpty!) {
            return const SizedBox();
          }
          return SizedBox(
            width: double.infinity,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: StreamBuilder(
                key: const ValueKey("Stream"),
                stream: filtersController.subcategoriesStream.stream,
                builder: (context, data) {
                  if (!data.hasData) {
                    return const SizedBox();
                  }

                  if (filtersController.subcategories.valueLength <= 4) {
                    return _ChipsWidget(
                      filtersController: filtersController,
                      subcategories: filtersController.subcategories.value!,
                      selected: data.data!,
                    );
                  }
                  return Obx(
                    () {
                      List subcategories;
                      if (!controller.isSubcategoriesExpanded &&
                          filtersController.subcategories.valueLength > 4) {
                        subcategories = filtersController.subcategories.value!
                            .sublist(0, 4);
                      } else {
                        subcategories = filtersController.subcategories.value!;
                      }
                      return _ChipsWidget(
                        filtersController: filtersController,
                        subcategories: subcategories,
                        selected: data.data!,
                        lastChips: InkWell(
                          onTap: () => controller.isSubcategoriesExpanded =
                              !controller.isSubcategoriesExpanded,
                          child: Chip(
                            label: Text(
                              controller.isSubcategoriesExpanded ? "-" : "+",
                              style: const TextStyle(fontSize: 17),
                            ),
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          );
        }
      },
    );
  }
}

class _ChipsWidget extends GetView<CategoryPageController> {
  const _ChipsWidget({
    required this.filtersController,
    required this.subcategories,
    required this.selected,
    this.lastChips,
  });

  final FiltersPageController filtersController;
  final List subcategories;
  final List selected;
  final Widget? lastChips;

  @override
  Widget build(BuildContext context) {
    return Wrap(
      alignment: WrapAlignment.start,
      spacing: 8,
      children: [
        ...subcategories.map(
          (subcategory) => FilterChip(
            label: Text(
              subcategory.name,
              style: TextStyle(
                color: filtersController.subcategoriesController.selectedOptions
                        .map((e) => e.value!.id)
                        .contains(subcategory.id)
                    ? StyleRepo.white
                    : null,
              ),
            ),
            selected: selected.contains(subcategory),
            onSelected: (bool value) =>
                controller.filterOnSubcategory(subcategory, value),
          ),
        ),
        if (lastChips != null) lastChips!,
      ],
    );
  }
}
