import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/activity/set.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';

class SetsWidget extends StatelessWidget {
  final List<ActivitySet> sets;
  final Function(int setId)? onBook;
  const SetsWidget({
    super.key,
    required this.sets,
    required this.onBook,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          tr(LocaleKeys.available_sets),
          style: context.textTheme.bodyLarge!.copyWith(
            color: StyleRepo.blueViolet,
            fontWeight: FontWeight.w600,
            decoration: TextDecoration.underline,
          ),
        ),
        const Gap(12),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: sets.length,
          padding: EdgeInsets.zero,
          separatorBuilder: (_, __) => const Gap(12),
          itemBuilder: (context, i) {
            return Container(
              decoration: BoxDecoration(
                color: StyleRepo.white,
                boxShadow: StyleRepo.elevation,
                borderRadius: BorderRadius.circular(12),
              ),
              child: ExpansionTileTheme(
                data: const ExpansionTileThemeData(
                    shape: RoundedRectangleBorder()),
                child: ExpansionTile(
                  title: Text(
                    "${tr(LocaleKeys.set)} ${i + 1}",
                    style: context.textTheme.titleMedium!
                        .copyWith(color: StyleRepo.berkeleyBlue),
                  ),
                  children: [
                    ...List.generate(
                      sets[i].sessions.length,
                      (j) => Column(
                        children: [
                          const Divider(height: 2),
                          ListTile(
                            title: Text(
                              "${tr(LocaleKeys.session)} ${j + 1}",
                              style: context.textTheme.titleSmall!
                                  .copyWith(color: StyleRepo.berkeleyBlue),
                            ),
                            trailing: Text(DateFormat.yMd()
                                .format(sets[i].sessions[j].date)),
                            subtitle: Text(
                              tr(
                                LocaleKeys.from_to,
                                args: [
                                  sets[i].sessions[j].from.format(context),
                                  sets[i].sessions[j].to.format(context),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const Divider(height: 0),
                    if (onBook != null)
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 16, vertical: 12),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            AppElevatedButton(
                              onTap: () => onBook!(sets[i].id),
                              height: 35,
                              width: 150,
                              child: Text(tr(LocaleKeys.booking)),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }
}
