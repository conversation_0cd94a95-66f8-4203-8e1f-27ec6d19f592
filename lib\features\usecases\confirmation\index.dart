import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/outlined_button.dart';

class ConfirmationDialog extends StatelessWidget {
  final void Function() onAccept;
  final void Function()? onDecline;
  final String body;
  final String? confirmText;

  const ConfirmationDialog({
    super.key,
    required this.body,
    required this.onAccept,
    this.confirmText,
    this.onDecline,
  });

  static confirm({
    required void Function() onAccept,
    required String body,
    String? confirmText,
    void Function()? onDecline,
  }) =>
      Get.dialog(ConfirmationDialog(
        onAccept: onAccept,
        body: body,
        confirmText: confirmText,
        onDecline: onDecline,
      ));

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 40),
            padding: const EdgeInsets.fromLTRB(16, 40, 16, 24),
            decoration: BoxDecoration(
              color: StyleRepo.white,
              borderRadius: BorderRadius.circular(45),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Gap(40),
                Text(
                  body,
                  textAlign: TextAlign.center,
                  style: context.textTheme.titleLarge!
                      .copyWith(color: StyleRepo.grey.shade600),
                ),
                const Gap(40),
                SizedBox(
                  height: 35,
                  child: Row(
                    children: [
                      Expanded(
                        child: AppOutlinedButton(
                          onTap: () {
                            Get.back();
                            onDecline?.call();
                          },
                          backgroundColor: StyleRepo.white,
                          borderColor: StyleRepo.grey.shade600,
                          child: Text(
                            LocaleKeys.cancel.tr(),
                            style: TextStyle(color: StyleRepo.grey.shade600),
                          ),
                        ),
                      ),
                      const Gap(16),
                      Expanded(
                        child: AppOutlinedButton(
                          onTap: () {
                            Get.back();
                            onAccept.call();
                          },
                          backgroundColor: StyleRepo.red.shade100,
                          borderColor: StyleRepo.red,
                          child: Text(
                            confirmText ?? LocaleKeys.confirm.tr(),
                            style: TextStyle(color: StyleRepo.grey.shade600),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Assets.icons.confirmationSticker.svg(),
        ],
      ),
    );
  }
}
