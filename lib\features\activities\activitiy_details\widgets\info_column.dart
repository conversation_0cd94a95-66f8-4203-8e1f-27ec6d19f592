import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/activity/constants/type.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import '../../widgets/periodic_time_info.dart';
import '../controller.dart';
import '../models/activity.dart';

class ActivityInfoColumn extends StatelessWidget {
  final ActivityDetails activity;
  const ActivityInfoColumn({super.key, required this.activity});

  @override
  Widget build(BuildContext context) {
    ActivityDetailsPageController controller = Get.find(tag: "${activity.id}");
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SvgIcon(
                Assets.icons.price.path,
                size: 20,
                color: StyleRepo.grey.shade700,
              ),
              const Gap(6),
              Expanded(
                child: RichText(
                  text: TextSpan(
                    style: context.textTheme.bodyMedium,
                    children: [
                      TextSpan(
                        text: "${LocaleKeys.activity_price.tr()}: ",
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      TextSpan(
                        text:
                            "${activity.price.toStringAsFixed(1)} ${LocaleKeys.s_a_r.tr()} ${activity.priceType.explainedText}",
                      ),
                      if (activity.priceBeforeDiscount != null)
                        const WidgetSpan(child: SizedBox(width: 12)),
                      if (activity.priceBeforeDiscount != null)
                        TextSpan(
                          text: "${activity.priceBeforeDiscount}",
                          style: const TextStyle(
                              decoration: TextDecoration.lineThrough),
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          if (activity.type == ActivityType.periodic_days) const Gap(16),
          if (activity.type == ActivityType.periodic_days)
            PeriodicTimeInfo(periodic: activity.periodic!),
          if (activity.type == ActivityType.numbered_session)
            controller.setsRequest!.listBuilder(
              loader: (_) => const SizedBox(),
              errorBuilder: (_, __) => const SizedBox(),
              builder: (context, sets) {
                if (sets.length == 1 && sets.first.sessions.length == 1) {
                  return Column(
                    children: [
                      const Gap(12),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SvgIcon(
                            Assets.icons.calendar.path,
                            size: 20,
                            color: StyleRepo.grey.shade700,
                          ),
                          const Gap(6),
                          Expanded(
                            child: RichText(
                              text: TextSpan(
                                style: context.textTheme.bodyMedium,
                                children: [
                                  TextSpan(
                                    text: "${LocaleKeys.activity_date.tr()}: ",
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  TextSpan(
                                    text: DateFormat.yMd()
                                        .format(sets.first.sessions.first.date),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                      const Gap(12),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SvgIcon(
                            Assets.icons.time.path,
                            size: 20,
                            color: StyleRepo.grey.shade700,
                          ),
                          const Gap(6),
                          Text(
                            "${LocaleKeys.time.tr()}: ",
                            style: context.textTheme.bodyMedium!
                                .copyWith(fontWeight: FontWeight.w500),
                          ),
                          Expanded(
                            child: RichText(
                              text: TextSpan(
                                style: context.textTheme.bodyMedium,
                                text: LocaleKeys.from_to.tr(
                                  args: [
                                    sets.first.sessions.first.from
                                        .format(context),
                                    sets.first.sessions.first.to
                                        .format(context),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  );
                }
                return null;
              },
            ),
          const Gap(16),
          Row(
            children: [
              SvgIcon(
                Assets.icons.targetAge.path,
                size: 20,
                color: StyleRepo.grey.shade700,
              ),
              const Gap(6),
              Expanded(
                child: RichText(
                  text: TextSpan(
                    style: context.textTheme.bodyMedium,
                    children: [
                      TextSpan(
                        text: "${LocaleKeys.target_age.tr()}: ",
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      TextSpan(
                        text: LocaleKeys.from_to.tr(
                          args: ["${activity.minAge}", "${activity.maxAge}"],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          const Gap(16),
          Row(
            children: [
              SvgIcon(
                Assets.icons.chairs.path,
                size: 20,
                color: StyleRepo.grey.shade700,
              ),
              const Gap(6),
              Expanded(
                child: RichText(
                  text: TextSpan(
                    style: context.textTheme.bodyMedium,
                    children: [
                      TextSpan(
                        text: "${LocaleKeys.capacity.tr()}: ",
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      TextSpan(
                        text: LocaleKeys.seats.plural(activity.capacity,
                            args: ["${activity.capacity}"]),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          const Gap(16),
          Row(
            children: [
              SvgIcon(
                Assets.icons.gender.path,
                size: 20,
                color: StyleRepo.grey.shade700,
              ),
              const Gap(6),
              Expanded(
                child: RichText(
                  text: TextSpan(
                    style: context.textTheme.bodyMedium,
                    children: [
                      TextSpan(
                        text: "${LocaleKeys.gender.tr()}: ",
                        style: const TextStyle(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      TextSpan(text: activity.gender.text),
                    ],
                  ),
                ),
              ),
            ],
          ),
          if (activity.isFamily) const Gap(16),
          if (activity.isFamily)
            Row(
              children: [
                SvgIcon(
                  Assets.icons.family.path,
                  size: 20,
                  color: StyleRepo.grey.shade700,
                ),
                const Gap(6),
                Expanded(
                  child: RichText(
                    text: TextSpan(
                      style: context.textTheme.bodyMedium,
                      children: [
                        TextSpan(
                          text: "${LocaleKeys.family_activity.tr()}: ",
                          style: const TextStyle(
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        TextSpan(text: LocaleKeys.targeted_for_families.tr()),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          if (activity.season != null) const Gap(16),
          if (activity.season != null)
            Row(
              children: [
                SvgIcon(
                  Assets.icons.season.path,
                  size: 20,
                  color: StyleRepo.grey.shade700,
                ),
                const Gap(6),
                Expanded(
                  child: RichText(
                    text: TextSpan(
                      style: context.textTheme.bodyMedium,
                      children: [
                        TextSpan(
                          text: "${LocaleKeys.season.tr()}: ",
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        TextSpan(text: activity.season),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          if (activity.specialCases.isNotEmpty) const Gap(16),
          if (activity.specialCases.isNotEmpty)
            Row(
              children: [
                SvgIcon(
                  Assets.icons.brain.path,
                  size: 20,
                  color: StyleRepo.grey.shade700,
                ),
                const Gap(6),
                Expanded(
                  child: RichText(
                    text: TextSpan(
                      style: context.textTheme.bodyMedium,
                      children: [
                        TextSpan(
                          text: "${LocaleKeys.special_case.tr()}: ",
                          style: const TextStyle(fontWeight: FontWeight.w500),
                        ),
                        TextSpan(
                          text: activity.specialCases
                              .map((e) => e.name)
                              .join(", "),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }
}
