import '../image.dart';

class MainUser {
  late int id;
  late String name;
  late String phone;
  late String code;
  String? email;
  ImageModel? image;

  MainUser({
    required this.id,
    required this.name,
    required this.phone,
    required this.code,
    this.email,
    this.image,
  });

  MainUser.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json["name"];
    phone = json['user']["phone"];
    email = json['user']["email"];
    code = json['refer_code'];
    image = json['user']["media"].isEmpty
        ? null
        : ImageModel.fromJson(json['user']["media"]["profile"].first);
  }
  Map<String, dynamic> toStorage() => {
        "id": id,
        "name": name,
        "phone": phone,
        "email": email,
        "refer_code": code,
        "image": image?.toJson(),
      };
  factory MainUser.fromStorage(Map<String, dynamic> json) => MainUser(
        id: json['id'] ?? 0,
        name: json["name"],
        phone: json["phone"],
        email: json["email"],
        code: json['refer_code'],
        image:
            json["image"] != null ? ImageModel.fromJson(json["image"]) : null,
      );

  @override
  String toString() => "${toStorage()}";
}
