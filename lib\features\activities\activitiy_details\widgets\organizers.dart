import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/image.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';

class ActivityOrganizers extends StatelessWidget {
  final List<ImageModel> organizers;
  const ActivityOrganizers({super.key, required this.organizers});

  @override
  Widget build(BuildContext context) {
    if (organizers.isEmpty) {
      return const SizedBox();
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            LocaleKeys.organizers.tr(),
            style: context.textTheme.bodyLarge!.copyWith(
              color: StyleRepo.blueViolet,
              fontWeight: FontWeight.w600,
              decoration: TextDecoration.underline,
            ),
          ),
        ),
        const Gap(12),
        SizedBox(
          height: Get.width * .25,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: organizers.length,
            separatorBuilder: (_, __) => const Gap(12),
            itemBuilder: (context, index) => AspectRatio(
              aspectRatio: 1,
              child: AppImage(
                path: organizers[index].medium,
                type: ImageType.CachedNetwork,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  color: StyleRepo.white,
                  boxShadow: StyleRepo.elevation_3,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
