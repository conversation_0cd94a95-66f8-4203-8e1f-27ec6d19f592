import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';

import 'status.dart';
import 'widgets/toast.dart';

class Toast {
  static FToast fToast = FToast();
  static show(
      {String? title,
      required String message,
      ToastStatus status = ToastStatus.fail}) {
    fToast.init(Get.overlayContext!);
    try {
      fToast.removeCustomToast();
    } catch (_) {}
    FocusManager.instance.primaryFocus?.unfocus();
    if (title == null) {
      if (status == ToastStatus.success) {
        title = LocaleKeys.success.tr();
      } else if (status == ToastStatus.warning) {
        title = LocaleKeys.warning.tr();
      } else {
        title = LocaleKeys.failure.tr();
      }
    }
    fToast.showToast(
      toastDuration: 3.seconds,
      gravity: ToastGravity.BOTTOM,
      child: AppToast(
        title: title,
        message: message,
        status: status,
      ),
    );
  }
}
