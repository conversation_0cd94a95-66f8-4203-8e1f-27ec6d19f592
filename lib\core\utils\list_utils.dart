import 'dart:math';

extension ListExtension<T> on List<T> {
  List<T> randomItems({int? max}) {
    if (isEmpty) {
      return [];
    }
    max = max ?? length ~/ 2;
    List<T> temp = [];
    Set<int> indexes = {};
    for (var i = 0; i < max; i++) {
      int randomIndex = Random().nextInt(max);
      if (indexes.contains(randomIndex)) {
        continue;
      }
      indexes.add(randomIndex);
      temp.add(this[randomIndex]);
    }
    return temp;
  }

  T get randomItem {
    if (isEmpty) throw "List is empty";
    if (length == 1) return first;
    return this[Random().nextInt(length - 1)];
  }

  List<T> joinItems(T separator) {
    if (length < 2) return this;

    List<T> spacedList = [];
    for (int i = 0; i < length; i++) {
      spacedList.add(this[i]);
      if (i < length - 1) {
        spacedList.add(separator);
      }
    }
    return spacedList;
  }
}
