import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/widgets/general_componenets/buttons/elevated_button.dart';

import '../widgets/general_componenets/buttons/outlined_button.dart';
import '../widgets/general_componenets/pages/rounded_page.dart';
import 'controller.dart';
import 'widgets/age.dart';
import 'widgets/category.dart';
import 'widgets/gender.dart';
import 'widgets/price_range.dart';
import 'widgets/price_type.dart';
import 'widgets/rating.dart';
import 'widgets/special_need.dart';
import 'widgets/subcategories.dart';

class FiltersPage extends GetView<FiltersPageController> {
  const FiltersPage({super.key});

  @override
  Widget build(BuildContext context) {
    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () async {
        controller.applyFilters();
        return false;
      },
      child: RoundedPage(
        title: Text(LocaleKeys.filters.tr()),
        onBack: () => controller.applyFilters(),
        actions: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8),
            child: AppOutlinedButton(
              onTap: () => controller.resetFilters(),
              width: null,
              height: 35,
              borderColor: StyleRepo.white,
              child: Text(
                LocaleKeys.reset.tr(),
                style: const TextStyle(color: StyleRepo.white),
              ),
            ),
          ),
        ],
        child: ListView(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          children: [
            const CategoriesFilter(),
            Obx(
              () => controller.selectedCategory.id == 0
                  ? const SizedBox()
                  : const SubcategoriesFilter(),
            ),
            const PriceTypeFilter(),
            Obx(
              () => controller.minMaxPriceRange.start == 0 &&
                      controller.minMaxPriceRange.end == 0
                  ? const SizedBox()
                  : const PriceRangeFilter(),
            ),
            const RatingFilter(),
            const AgeFilter(),
            const GenderFilter(),
            const SpecialNeedFilter(),
            const Gap(24),
            AppElevatedButton(
              onTap: () => controller.applyFilters(),
              child: Text(LocaleKeys.apply_filters.tr()),
            ),
            const Gap(16),
          ],
        ),
      ),
    );
  }
}
