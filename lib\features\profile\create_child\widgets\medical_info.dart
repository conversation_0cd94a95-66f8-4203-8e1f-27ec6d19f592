import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/error_widget.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';
import 'package:multi_dropdown/multiselect_dropdown.dart';

import '../../../../core/models/selection.dart';
import '../controller.dart';

class MedicalInfoFields extends GetView<CreateChildPageController> {
  const MedicalInfoFields({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.medical_information.tr(),
          style: context.textTheme.titleLarge!
              .copyWith(color: StyleRepo.berkeleyBlue),
        ),
        const Gap(20),
        Text(
          LocaleKeys.allergies.tr(),
          style: context.textTheme.bodyMedium!
              .copyWith(color: StyleRepo.berkeleyBlue),
        ),
        const Gap(12),
        TextFormField(
          controller: controller.allergies,
          decoration: InputDecoration(
            hintText: LocaleKeys.spring_allergy.tr(),
            suffixIcon: Padding(
              padding: const EdgeInsets.all(14.0),
              child: SvgIcon(Assets.icons.edit.path),
            ),
          ),
        ),
        const Gap(20),
        Text(
          LocaleKeys.special_case.tr(),
          style: context.textTheme.bodyMedium!
              .copyWith(color: StyleRepo.berkeleyBlue),
        ),
        const Gap(12),
        controller.specialCasesRequest.listBuilder(
          loader: (_) => const FieldLoadingWidget(),
          errorBuilder: (context, response) => FieldErrorWidget(
            error: response.message,
            onRefresh: () => controller.specialCasesRequest.refresh(),
          ),
          builder: (context, data) {
            return ChipTheme(
              data: const ChipThemeData(),
              child: controller.specialCasesRequest.listBuilder(
                loader: (_) => const FieldLoadingWidget(),
                errorBuilder: (context, response) => FieldErrorWidget(
                  error: response.message,
                  onRefresh: () => controller.hobbiesRequest.refresh(),
                ),
                builder: (context, data) {
                  return MultiSelectDropDown<Selection>(
                    controller: controller.specialCasesController,
                    onOptionSelected: (selections) {
                      log("Selections: ${selections.map((e) => e.value!.id).toList()}");
                    },
                    chipConfig: const ChipConfig(
                      padding: EdgeInsets.symmetric(horizontal: 12),
                    ),
                    inputDecoration: BoxDecoration(
                      border: Border.fromBorderSide(Theme.of(context)
                          .inputDecorationTheme
                          .enabledBorder!
                          .borderSide),
                      borderRadius: (Theme.of(context)
                              .inputDecorationTheme
                              .enabledBorder! as OutlineInputBorder)
                          .borderRadius,
                    ),
                    options: const [],
                    suffixIcon: const Icon(Icons.keyboard_arrow_down),
                  );
                },
              ),
            );
          },
        ),
      ],
    );
  }
}
