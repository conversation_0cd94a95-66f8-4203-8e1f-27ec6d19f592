import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/models/constants/gender.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';

class GenderCard extends StatelessWidget {
  final Gender gender;
  final bool isSelected;
  const GenderCard(this.gender, {super.key, this.isSelected = false});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        AnimatedContainer(
          duration: 300.milliseconds,
          margin: const EdgeInsets.only(top: 30),
          padding: const EdgeInsets.fromLTRB(16, 40, 16, 12),
          decoration: BoxDecoration(
            gradient: gradient(gender),
            borderRadius: BorderRadius.circular(7),
            border: Border.all(
              color: isSelected ? StyleRepo.blueViolet : Colors.transparent,
              width: 2,
            ),
          ),
          child: Center(
            child: Text(
              gender.text,
              style: context.textTheme.bodyMedium!.copyWith(
                color: StyleRepo.white,
                fontWeight: FontWeight.w700,
              ),
            ),
          ),
        ),
        Align(
          alignment: Alignment.topCenter,
          child: Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              image: DecorationImage(
                image:
                    (gender.isMale ? Assets.images.boy : Assets.images.girl)
                        .provider(),
                fit: BoxFit.cover,
              ),
              gradient: photoGradient(gender),
              boxShadow: StyleRepo.elevation_2,
            ),
          ),
        ),
      ],
    );
  }

  LinearGradient gradient(gender) {
    if (gender.isFemale) {
      return LinearGradient(
        colors: [
          const Color(0xFFFF7DBA).withValues(alpha: .7),
          const Color(0xFFFFC8C8),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );
    } else {
      return const LinearGradient(
        colors: [Color(0xFF1FA9E2), Color(0xFF5CC7DF)],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );
    }
  }

  LinearGradient photoGradient(gender) {
    if (gender.isMale) {
      return const LinearGradient(
        colors: [Color(0xFF1EA9E2), Color(0xFF63CADF)],
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
      );
    } else {
      return const LinearGradient(
        colors: [Color(0xFFFF7ABA), Color(0xFFFFC8C8)],
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
      );
    }
  }
}
