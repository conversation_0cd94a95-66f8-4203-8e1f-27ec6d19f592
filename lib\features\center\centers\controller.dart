import 'package:dio/dio.dart';
import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:gopal/core/services/location.dart';

import '../../../core/services/rest_api/rest_api.dart';
import 'models/nav.dart';

class CentersPageController extends GetxController {
  final CentersPageNav nav;
  CentersPageController(this.nav);

  Future<ResponseModel> loadData(int page, CancelToken cancel) async {
    return await Request(
      endPoint: EndPoints.centers,
      params: {
        "page": page,
        if (location != null) "lat": location!.latitude,
        if (location != null) "lng": location!.longitude,
      },
      cancelToken: cancel,
    ).perform();
  }

  final Rx<bool> _isInitialized = false.obs;
  bool get isInitialized => _isInitialized.value;
  set isInitialized(bool value) => _isInitialized.value = value;

  LatLng? location;

  init() async {
    location = await LocationUtils.getMyLocation();
    isInitialized = true;
  }

  @override
  void onInit() {
    init();
    super.onInit();
  }
}
