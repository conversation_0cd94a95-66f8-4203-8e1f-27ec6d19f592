import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/notifications/notification.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:readmore/readmore.dart';
import 'package:timeago/timeago.dart' as timeago;

import '../controller.dart';

class NotificationCard extends GetView<NotificationsPageController> {
  final NotificationModel notification;
  const NotificationCard({super.key, required this.notification});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      borderRadius: BorderRadius.circular(12),
      onTap: () => controller.notificationClick(notification),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: notification.seen ? StyleRepo.white : StyleRepo.grey.shade200,
          borderRadius: BorderRadius.circular(12),
          boxShadow: StyleRepo.elevation_2,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    notification.title,
                    style: context.textTheme.titleMedium!.copyWith(
                      color: StyleRepo.berkeleyBlue,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                Text(
                  timeago.format(
                    notification.date,
                    locale:
                        EasyLocalization.of(
                          context,
                        )!.currentLocale!.languageCode,
                  ),
                ),
              ],
            ),
            const Gap(8),
            ReadMoreText(
              notification.body,
              style: TextStyle(color: StyleRepo.grey.shade700),
              moreStyle: context.textTheme.bodyMedium!.copyWith(
                color: StyleRepo.turquoise.withValues(alpha: .5),
                fontWeight: FontWeight.w600,
              ),
              lessStyle: context.textTheme.bodyMedium!.copyWith(
                color: StyleRepo.turquoise.withValues(alpha: .5),
                fontWeight: FontWeight.w600,
              ),
              trimMode: TrimMode.Line,
              trimLines: 2,
              trimCollapsedText: LocaleKeys.read_more.tr(),
              trimExpandedText: " ${LocaleKeys.read_less.tr()}",
            ),
          ],
        ),
      ),
    );
  }
}
