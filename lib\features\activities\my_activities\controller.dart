import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/constants/controllers_tags.dart';
import 'package:gopal/core/models/book/book.dart';
import 'package:gopal/core/models/book/status.dart';
import 'package:gopal/core/models/user/main_child.dart';
import 'package:gopal/core/services/pagination/controller.dart';
import 'package:gopal/features/activities/my_activities/models/completed_book.dart';
import 'package:gopal/features/usecases/children_selection/widgets/children_selection_dialog.dart';
import 'package:gopal/features/usecases/toast/toast.dart';

import '../../../core/services/rest_api/rest_api.dart';
import '../../../core/widgets/loading.dart';
import 'models/favourite_activity.dart';
import 'models/tabs.dart';

class MyActivitiesPageController extends GetxController
    with GetTickerProviderStateMixin {
  late TabController tabController;

  @override
  void onInit() {
    int initialIndex = 0;

    if (Get.arguments != null &&
        Get.arguments is Map &&
        (Get.arguments as Map)['activities_tab'] != null) {
      initialIndex =
          MyActivitiesTabs.values.indexOf(Get.arguments['activities_tab']);
    }
    tabController = TabController(
      length: MyActivitiesTabs.values.length,
      vsync: this,
      initialIndex: initialIndex,
    );
    super.onInit();
  }

  RxList<MainChild> children = RxList.empty();

  selectChildren() async {
    List<MainChild>? result = await ChildrenSelectionDialog.open(
      initialSelected: children.map((element) => element.id!).toList(),
      withAll: true,
      seperateParent: false,
    );
    if (result == null) return;
    children.clear();
    children.addAll(result);
    try {
      Get.find<PaginationController<BookedActivity>>(
              tag: ControllersTags.my_pending_activities)
          .refreshData();
    } catch (_) {}
    try {
      Get.find<PaginationController<BookedActivity>>(
              tag: ControllersTags.my_in_progress_activities)
          .refreshData();
    } catch (_) {}
    try {
      Get.find<PaginationController<CompletedBook>>(
              tag: ControllersTags.my_completed_activities)
          .refreshData();
    } catch (_) {}
  }

  Future<ResponseModel> fetchPending(int page, CancelToken cancel) async {
    Map<String, dynamic> params = {
      "page": page,
      "statuses[]": BookStatus.pending.value,
    };
    for (int i = 0; i < children.length; i++) {
      params['children_ids[$i]'] = children[i].id;
    }
    return Request(
      endPoint: EndPoints.user_books,
      params: params,
      cancelToken: cancel,
    ).perform();
  }

  Future<ResponseModel> fetchInProgress(int page, CancelToken cancel) async {
    Map<String, dynamic> params = {
      "page": page,
      "statuses[]": BookStatus.in_progress.value,
    };
    for (int i = 0; i < children.length; i++) {
      params['children_ids[$i]'] = children[i].id;
    }
    return Request(
      endPoint: EndPoints.user_books,
      params: params,
      cancelToken: cancel,
    ).perform();
  }

  Future<ResponseModel> fetchCompleted(int page, CancelToken cancel) async {
    Map<String, dynamic> params = {
      "page": page,
      "statuses[0]": BookStatus.completed.value,
      "statuses[1]": BookStatus.rejected.value,
      // "statuses[2]": BookType.canceled.value,
    };
    for (int i = 0; i < children.length; i++) {
      params['children_ids[$i]'] = children[i].id;
    }
    return Request(
      endPoint: EndPoints.user_books,
      params: params,
      cancelToken: cancel,
    ).perform();
  }

  @override
  void onClose() {
    Get.delete<PaginationController<BookedActivity>>(
        tag: ControllersTags.my_pending_activities);
    Get.delete<PaginationController<BookedActivity>>(
        tag: ControllersTags.my_in_progress_activities);
    Get.delete<PaginationController<CompletedBook>>(
        tag: ControllersTags.my_completed_activities);
    Get.delete<PaginationController<FavouriteActivity>>(
        tag: ControllersTags.my_favourites_activities);
    super.onClose();
  }

  //SECTION - Delete Book
  deleteBook(BookedActivity bookedActivity) async {
    Loading.show();
    ResponseModel response = await Request(
      endPoint: EndPoints.delete_book(bookedActivity.id),
      method: RequestMethod.Delete,
    ).perform();
    Loading.dispose();
    if (response.success) {
      try {
        Get.find<PaginationController<BookedActivity>>(
                tag: ControllersTags.my_pending_activities)
            .data
            .remove(bookedActivity);
      } catch (_) {}
      try {
        Get.find<PaginationController<BookedActivity>>(
                tag: ControllersTags.my_in_progress_activities)
            .data
            .remove(bookedActivity);
      } catch (_) {}
    } else {
      Toast.show(message: response.message);
    }
  }
  //!SECTION

  //SECTION - Favourites

  Future<ResponseModel> fetchFavourites(int page, CancelToken cancel) async {
    return Request(
      endPoint: EndPoints.favourites,
      params: {"page": page},
      cancelToken: cancel,
    ).perform();
  }

  removeFromFavourite(FavouriteActivity activity) async {
    Loading.show();
    ResponseModel response = await Request(
      endPoint: EndPoints.remove_from_favourite(activity.favId),
      method: RequestMethod.Delete,
    ).perform();
    Loading.dispose();
    if (response.success) {
      Get.find<PaginationController<FavouriteActivity>>(
              tag: ControllersTags.my_favourites_activities)
          .data
          .remove(activity);
    } else {
      Toast.show(message: response.message);
    }
  }
  //!SECTION
}
