# Diff Summary

Date : 2024-12-31 10:39:18

Directory d:\\work\\ixCoders\\current\\go_pal\\go_pal_flutter\\lib

Total : 128 files,  2402 codes, -158 comments, 178 blanks, all 2422 lines

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Dart | 128 | 2,402 | -158 | 178 | 2,422 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 128 | 2,402 | -158 | 178 | 2,422 |
| . (Files) | 1 | -10 | -1 | -2 | -13 |
| core | 37 | 421 | -156 | 28 | 293 |
| core (Files) | 1 | 20 | 0 | 0 | 20 |
| core\\config | 3 | 5 | 0 | 1 | 6 |
| core\\constants | 1 | 2 | 0 | 1 | 3 |
| core\\localization | 2 | 102 | 0 | 0 | 102 |
| core\\models | 14 | 176 | 2 | 21 | 199 |
| core\\models (Files) | 1 | 7 | 0 | 1 | 8 |
| core\\models\\activity | 6 | 101 | 0 | 14 | 115 |
| core\\models\\activity (Files) | 4 | 45 | 0 | 9 | 54 |
| core\\models\\activity\\constants | 2 | 56 | 0 | 5 | 61 |
| core\\models\\book | 2 | 36 | 0 | 4 | 40 |
| core\\models\\constants | 1 | 3 | 0 | 1 | 4 |
| core\\models\\notifications | 3 | 24 | 2 | 1 | 27 |
| core\\models\\notifications (Files) | 2 | 8 | 2 | 0 | 10 |
| core\\models\\notifications\\data | 1 | 16 | 0 | 1 | 17 |
| core\\models\\user | 1 | 5 | 0 | 0 | 5 |
| core\\services | 14 | 79 | -167 | -8 | -96 |
| core\\services (Files) | 3 | 0 | -172 | -24 | -196 |
| core\\services\\firebase_messaging | 2 | 3 | 0 | 1 | 4 |
| core\\services\\local_notifications | 1 | 21 | 4 | 5 | 30 |
| core\\services\\notifications_click | 1 | 11 | 0 | 0 | 11 |
| core\\services\\notifications_counter | 1 | 0 | 1 | 0 | 1 |
| core\\services\\rest_api | 5 | 0 | 0 | 4 | 4 |
| core\\services\\rest_api (Files) | 1 | 1 | 0 | 0 | 1 |
| core\\services\\rest_api\\constants | 1 | 5 | 0 | 0 | 5 |
| core\\services\\rest_api\\handlers | 1 | 2 | 0 | 0 | 2 |
| core\\services\\rest_api\\models | 1 | 2 | 0 | 1 | 3 |
| core\\services\\rest_api\\utilitis | 1 | -10 | 0 | 3 | -7 |
| core\\services\\share | 1 | 44 | 0 | 6 | 50 |
| core\\style | 1 | 21 | 9 | 9 | 39 |
| core\\style\\assets | 1 | 21 | 9 | 9 | 39 |
| core\\style\\assets\\gen | 1 | 21 | 9 | 9 | 39 |
| core\\utils | 1 | 16 | 0 | 4 | 20 |
| features | 90 | 1,991 | -1 | 152 | 2,142 |
| features\\activities | 32 | 844 | -6 | 72 | 910 |
| features\\activities\\activitiy_details | 11 | -723 | -6 | -54 | -783 |
| features\\activities\\activitiy_details (Files) | 2 | -193 | -6 | -31 | -230 |
| features\\activities\\activitiy_details\\models | 4 | -125 | 0 | -10 | -135 |
| features\\activities\\activitiy_details\\widgets | 5 | -405 | 0 | -13 | -418 |
| features\\activities\\main_activity | 7 | 485 | 3 | 36 | 524 |
| features\\activities\\main_activity (Files) | 3 | 70 | 0 | 14 | 84 |
| features\\activities\\main_activity\\models | 2 | 102 | 0 | 8 | 110 |
| features\\activities\\main_activity\\widgets | 2 | 313 | 3 | 14 | 330 |
| features\\activities\\usecases | 10 | 967 | 14 | 87 | 1,068 |
| features\\activities\\usecases\\booking | 1 | 168 | 8 | 25 | 201 |
| features\\activities\\usecases\\open_sessions_calendar | 4 | 355 | 5 | 30 | 390 |
| features\\activities\\usecases\\open_sessions_calendar (Files) | 2 | 101 | 5 | 21 | 127 |
| features\\activities\\usecases\\open_sessions_calendar\\widgets | 2 | 254 | 0 | 9 | 263 |
| features\\activities\\usecases\\points_selection | 1 | 109 | 0 | 5 | 114 |
| features\\activities\\usecases\\specific_days_calendar | 4 | 335 | 1 | 27 | 363 |
| features\\activities\\usecases\\specific_days_calendar (Files) | 2 | 107 | 1 | 18 | 126 |
| features\\activities\\usecases\\specific_days_calendar\\widgets | 2 | 228 | 0 | 9 | 237 |
| features\\activities\\widgets | 4 | 115 | -17 | 3 | 101 |
| features\\auth | 9 | -71 | 1 | -12 | -82 |
| features\\auth\\create_account | 2 | 14 | 0 | 0 | 14 |
| features\\auth\\create_account (Files) | 1 | 3 | 0 | 0 | 3 |
| features\\auth\\create_account\\widgets | 1 | 11 | 0 | 0 | 11 |
| features\\auth\\login | 2 | 19 | 1 | 2 | 22 |
| features\\auth\\signup | 4 | -105 | 0 | -14 | -119 |
| features\\auth\\signup (Files) | 3 | -101 | 0 | -13 | -114 |
| features\\auth\\signup\\models | 1 | -4 | 0 | -1 | -5 |
| features\\auth\\verification | 1 | 1 | 0 | 0 | 1 |
| features\\boarding | 1 | 5 | 0 | 0 | 5 |
| features\\books | 1 | 103 | 0 | 5 | 108 |
| features\\books\\widgets | 1 | 103 | 0 | 5 | 108 |
| features\\categories | 3 | 17 | 0 | 1 | 18 |
| features\\categories\\categories | 1 | 1 | 0 | 0 | 1 |
| features\\categories\\categories\\widgets | 1 | 1 | 0 | 0 | 1 |
| features\\categories\\category | 2 | 16 | 0 | 1 | 17 |
| features\\categories\\category (Files) | 1 | 15 | 0 | 1 | 16 |
| features\\categories\\category\\widgets | 1 | 1 | 0 | 0 | 1 |
| features\\center | 6 | 118 | 1 | 14 | 133 |
| features\\center\\centers_on_map | 3 | 105 | 1 | 14 | 120 |
| features\\center\\centers_on_map (Files) | 2 | 80 | 1 | 10 | 91 |
| features\\center\\centers_on_map\\models | 1 | 25 | 0 | 4 | 29 |
| features\\center\\profile | 3 | 13 | 0 | 0 | 13 |
| features\\center\\profile (Files) | 1 | 8 | 0 | 0 | 8 |
| features\\center\\profile\\widgets | 2 | 5 | 0 | 0 | 5 |
| features\\chat | 1 | 5 | 3 | 1 | 9 |
| features\\chat\\rooms | 1 | 5 | 3 | 1 | 9 |
| features\\filters | 3 | 9 | 0 | 0 | 9 |
| features\\filters (Files) | 1 | 3 | 0 | 0 | 3 |
| features\\filters\\models | 1 | 1 | 0 | 0 | 1 |
| features\\filters\\widgets | 1 | 5 | 0 | 0 | 5 |
| features\\home | 8 | 308 | 1 | 14 | 323 |
| features\\home (Files) | 2 | 39 | 1 | 1 | 41 |
| features\\home\\models | 1 | 18 | 0 | 2 | 20 |
| features\\home\\widgets | 5 | 251 | 0 | 11 | 262 |
| features\\main | 2 | 64 | -2 | 7 | 69 |
| features\\main\\widgets | 2 | 64 | -2 | 7 | 69 |
| features\\notifications | 1 | 7 | 0 | 0 | 7 |
| features\\profile | 5 | 33 | 0 | 1 | 34 |
| features\\profile\\child_profile | 1 | 2 | 0 | 0 | 2 |
| features\\profile\\edit_profile | 1 | 3 | 0 | 0 | 3 |
| features\\profile\\edit_profile\\widgets | 1 | 3 | 0 | 0 | 3 |
| features\\profile\\my_profile | 3 | 28 | 0 | 1 | 29 |
| features\\profile\\my_profile (Files) | 1 | 24 | 0 | 0 | 24 |
| features\\profile\\my_profile\\models | 1 | 2 | 0 | 1 | 3 |
| features\\profile\\my_profile\\widgets | 1 | 2 | 0 | 0 | 2 |
| features\\search | 4 | 228 | 1 | 23 | 252 |
| features\\search (Files) | 2 | 109 | 1 | 15 | 125 |
| features\\search\\widgets | 2 | 119 | 0 | 8 | 127 |
| features\\settings | 8 | 324 | 1 | 29 | 354 |
| features\\settings\\invitation | 2 | 107 | 1 | 15 | 123 |
| features\\settings\\privacy | 2 | 28 | -1 | 2 | 29 |
| features\\settings\\usecases | 4 | 189 | 1 | 12 | 202 |
| features\\settings\\usecases\\points | 3 | 174 | 1 | 12 | 187 |
| features\\settings\\usecases\\points (Files) | 2 | 156 | 1 | 9 | 166 |
| features\\settings\\usecases\\points\\models | 1 | 18 | 0 | 3 | 21 |
| features\\settings\\usecases\\support | 1 | 15 | 0 | 0 | 15 |
| features\\usecases | 2 | 63 | 0 | 4 | 67 |
| features\\usecases\\gender | 2 | 63 | 0 | 4 | 67 |
| features\\usecases\\gender\\widgets | 2 | 63 | 0 | 4 | 67 |
| features\\widgets | 4 | -66 | -1 | -7 | -74 |
| features\\widgets (Files) | 1 | -68 | -1 | -7 | -76 |
| features\\widgets\\backgrounds | 1 | 1 | 0 | 0 | 1 |
| features\\widgets\\general_componenets | 2 | 1 | 0 | 0 | 1 |
| features\\widgets\\general_componenets\\buttons | 2 | 1 | 0 | 0 | 1 |

[Summary](results.md) / [Details](details.md) / Diff Summary / [Diff Details](diff-details.md)