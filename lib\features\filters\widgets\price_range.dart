import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';

import '../controller.dart';

class PriceRangeFilter extends GetView<FiltersPageController> {
  const PriceRangeFilter({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Gap(24),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              LocaleKeys.price_range.tr(),
              style: context.textTheme.titleMedium!.copyWith(
                fontWeight: FontWeight.w700,
              ),
            ),
            Obx(
              () => Text(
                "${controller.priceRange.start.toInt()} - ${controller.priceRange.end.toInt()}",
                style: context.textTheme.titleMedium!.copyWith(
                  fontWeight: FontWeight.w700,
                ),
              ),
            ),
          ],
        ),
        const Gap(6),
        Obx(
          () => RangeSlider(
            min: controller.minMaxPriceRange.start,
            max: controller.minMaxPriceRange.end,
            values: controller.priceRange,
            divisions: (controller.minMaxPriceRange.end -
                    controller.minMaxPriceRange.start) ~/
                10,
            onChanged: (range) => controller.priceRange = range,
          ),
        ),
        const Gap(6),
        Obx(
          () => Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(controller.minMaxPriceRange.start.toInt().toString()),
              Text(controller.minMaxPriceRange.end.toInt().toString()),
            ],
          ),
        ),
      ],
    );
  }
}
