import 'package:flutter/material.dart';
import 'package:gopal/core/style/assets/gen/fonts.gen.dart';
import 'package:gopal/core/style/repo.dart';

// part 'style.tailor.dart';

///This class must have the Themes only
abstract class AppStyle {
  static ThemeData lightTheme(bool isArabic) {
    return ThemeData(
      fontFamily: isArabic ? FontFamily.shamel : FontFamily.quicksand,
      primaryColor: StyleRepo.blueViolet,
      scaffoldBackgroundColor: StyleRepo.grey.shade50,
      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: StyleRepo.grey.shade50,
        iconTheme: WidgetStateProperty.resolveWith(
          (states) => IconThemeData(
            color:
                states.contains(WidgetState.selected)
                    ? StyleRepo.blueViolet
                    : StyleRepo.grey.shade400,
          ),
        ),
        labelTextStyle: WidgetStateProperty.resolveWith(
          (states) => TextStyle(
            color:
                states.contains(WidgetState.selected)
                    ? StyleRepo.blueViolet
                    : StyleRepo.grey,
            fontSize: 12,
            fontWeight: FontWeight.w300,
          ),
        ),
      ),
      chipTheme: ChipThemeData(
        shape: const StadiumBorder(),
        showCheckmark: false,
        color: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return StyleRepo.blueViolet;
          } else {
            return StyleRepo.grey.shade100;
          }
        }),
        iconTheme: const IconThemeData(color: StyleRepo.blueViolet),
        labelStyle: const TextStyle(
          color: StyleRepo.berkeleyBlue,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
        side: const BorderSide(color: StyleRepo.blueViolet, width: .5),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: StyleRepo.white,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 12,
        ),
        hintStyle: const TextStyle(color: StyleRepo.grey),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: StyleRepo.grey),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: StyleRepo.grey),
        ),
        disabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: StyleRepo.grey),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(color: StyleRepo.grey.shade700, width: 1.5),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: StyleRepo.red),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: const BorderSide(color: StyleRepo.red, width: 1.5),
        ),
      ),
    );
  }
}
