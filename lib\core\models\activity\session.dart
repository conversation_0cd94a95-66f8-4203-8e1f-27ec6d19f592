import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/utils/date.dart';

class ActivitySession {
  late int id;
  late DateTime date;
  late TimeOfDay from;
  late TimeOfDay to;
  int? openSessionId;
  double? price;

  ActivitySession.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    date = DateTime.parse(json['date'] ?? json['specific_date']);
    from = TimeOfDayExt.parse(json['from'] ?? json['open_at']);
    to = TimeOfDayExt.parse(json['to'] ?? json['close_at']);
    openSessionId = json['sessionable_id'];
    price = double.tryParse("${json['price']}");
  }

  String displayText(context) {
    return "${DateFormat.MMMMd(EasyLocalization.of(context)!.currentLocale!.languageCode).format(date)} ${LocaleKeys.from_to.tr(args: [
          from.format(context),
          to.format(context)
        ])}";
  }
}
