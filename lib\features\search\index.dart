import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/features/widgets/general_componenets/app_bars/general_app_bar.dart';

import 'controller.dart';
import 'widgets/actitvities.dart';
import 'widgets/centers.dart';

class SearchPage extends StatelessWidget {
  const SearchPage({super.key});

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    SearchPageController controller = Get.put(SearchPageController());
    return Scaffold(
      appBar: GeneralAppBar(title: Text(tr(LocaleKeys.search))),
      body: Column(
        children: [
          TabBar(
            controller: controller.tabController,
            tabs: [tr(LocaleKeys.centers), tr(LocaleKeys.activities)]
                .map(
                  (e) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    child: Text(
                      e,
                      style: context.textTheme.titleMedium!
                          .copyWith(fontWeight: FontWeight.bold),
                    ),
                  ),
                )
                .toList(),
          ),
          Expanded(
            child: Tab<PERSON><PERSON><PERSON><PERSON><PERSON>(
              controller: controller.tabController,
              children: const [
                CentersSearch(),
                ActivitiesSearch(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
