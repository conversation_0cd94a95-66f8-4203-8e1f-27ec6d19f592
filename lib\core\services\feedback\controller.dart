import 'dart:io';
import 'dart:typed_data';

import 'package:feedback/feedback.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';

import 'widgets/dialog.dart';

class FeedBackController extends GetxController {
  late double paddingTop;
  late Size size;

  FeedBackController(BuildContext context) {
    paddingTop = MediaQuery.of(context).viewPadding.top;
    size = MediaQuery.of(context).size;
    _offset = Offset(10, paddingTop + 10).obs;
  }

  late final Rx<Offset> _offset;
  Offset get offset => _offset.value;
  set offset(Offset value) {
    if (value.dx < 10 || value.dx > size.width - 70) return;
    if (value.dy < paddingTop + 10 || value.dy > size.height - 70) return;
    _offset.value = value;
  }

  UserFeedback? feedback;

  submit(UserFeedback feedback) {
    this.feedback = feedback;
    Get.dialog(const FeedbackDialog());
  }

  sendToWhatsapp() async {
    Get.back();
    final ByteData byteData = ByteData.view(feedback!.screenshot.buffer);
    final String tempImagePath =
        '${(await getTemporaryDirectory()).path}/image.png';
    final buffer = byteData.buffer;
    await File(tempImagePath).writeAsBytes(
      buffer.asUint8List(byteData.offsetInBytes, byteData.lengthInBytes),
    );

    final box = Get.context?.findRenderObject() as RenderBox?;

    SharePlus.instance.share(
      ShareParams(
        files: [XFile(tempImagePath)],
        text: feedback!.text,
        sharePositionOrigin: box!.localToGlobal(Offset.zero) & box.size,
      ),
    );
  }

  saveToGallery() async {
    Get.back();
              // TODO - save image

    // var res = await ImageGallerySaver.saveImage(feedback!.screenshot);
    // log(res.runtimeType.toString());
    // log(res.toString());
  }
}
