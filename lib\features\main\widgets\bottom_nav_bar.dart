import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/features/main/models/destinations.dart';
import 'package:gopal/features/widgets/general_componenets/svg_icon.dart';

import '../controller.dart';

class MainNavBar extends StatelessWidget {
  const MainNavBar({super.key});

  @override
  Widget build(BuildContext context) {
    MainPageController controller = Get.find();
    return Obx(
      () => NavigationBar(
        onDestinationSelected: (destination) => controller.currentDestination =
            MainDestinations.values[destination],
        selectedIndex:
            MainDestinations.values.indexOf(controller.currentDestination),
        destinations: MainDestinations.values
            .map((e) =>
                NavigationDestination(icon: SvgIcon(e.iconPath), label: e.text))
            .toList(),
      ),
    );
  }
}
