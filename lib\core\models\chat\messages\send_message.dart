import 'dart:async';
import 'dart:convert';
import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:get/state_manager.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:gopal/core/models/chat/messages/types.dart';

import 'message.dart';

class SendMessage extends Message {
  late SendMessageContent content;

  final Rx<MessageStatus> _status = MessageStatus.waiting.obs;
  MessageStatus get status => _status.value;
  set status(MessageStatus value) => _status.value = value;

  SendMessage({
    required super.type,
    required super.date,
    required this.content,
  });

  @override
  bool get isMine => true;

  SendMessage.fromJson(super.json) : super.fromJson();

  FutureOr<FormData> toFormData() async {
    Map<String, dynamic> data = await content.toJson();
    data['type'] = type.value;
    return FormData.fromMap(data);
  }
}

abstract class SendMessageContent {
  FutureOr<Map<String, dynamic>> toJson();

  SendTextMessageContent get asText => this as SendTextMessageContent;
  SendVoiceMessageContent get asVoice => this as SendVoiceMessageContent;
  SendImagesMessageContent get asImages => this as SendImagesMessageContent;
  SendVideoMessageContent get asVideo => this as SendVideoMessageContent;
  SendDocumentMessageContent get asDocument =>
      this as SendDocumentMessageContent;
  SendLocationMessageContent get asLocation =>
      this as SendLocationMessageContent;
}

class SendTextMessageContent extends SendMessageContent {
  String text;

  SendTextMessageContent({required this.text});

  @override
  Map<String, dynamic> toJson() => {"content": text};
}

class SendVoiceMessageContent extends SendMessageContent {
  String voiceFile;
  List<double> waves;

  SendVoiceMessageContent({required this.voiceFile, required this.waves});

  @override
  Future<Map<String, dynamic>> toJson() async {
    return {
      "media[0]": await MultipartFile.fromFile(voiceFile),
      "metadata": jsonEncode({"waves": waves}),
    };
  }
}

class SendImagesMessageContent extends SendMessageContent {
  String? content;
  List<String> images;

  SendImagesMessageContent({required this.images, this.content});

  @override
  Future<Map<String, dynamic>> toJson() async {
    Map<String, dynamic> data = {
      "metadata": jsonEncode({"media_type": MessageMediaType.images.value}),
    };
    if (content != null || content!.isNotEmpty) {
      data['content'] = content;
    }
    for (var i = 0; i < images.length; i++) {
      data["media[$i]"] = await MultipartFile.fromFile(images[i]);
    }
    return data;
  }
}

class SendVideoMessageContent extends SendMessageContent {
  String? content;
  Uint8List? thumb;
  String video;

  SendVideoMessageContent({required this.video, this.content, this.thumb});

  @override
  Future<Map<String, dynamic>> toJson() async {
    Map<String, dynamic> data = {
      "media[0]": await MultipartFile.fromFile(video),
      if (content != null && content!.isNotEmpty) "content": content,
      "metadata": jsonEncode({"media_type": MessageMediaType.video.value}),
    };
    return data;
  }
}

class SendDocumentMessageContent extends SendMessageContent {
  String documentFile;
  String fileName;
  String? content;

  SendDocumentMessageContent(
      {required this.documentFile, required this.fileName, this.content});

  @override
  Future<Map<String, dynamic>> toJson() async {
    return {
      "media[0]": await MultipartFile.fromFile(documentFile),
      if (content != null || content!.isNotEmpty) "content": content,
      "metadata": jsonEncode({
        "media_type": MessageMediaType.doc.value,
        "doc_name": fileName,
      }),
    };
  }
}

class SendLocationMessageContent extends SendMessageContent {
  LatLng location;
  Uint8List mapImage;

  SendLocationMessageContent({required this.location, required this.mapImage});

  @override
  FutureOr<Map<String, dynamic>> toJson() {
    return {
      "media[0]": MultipartFile.fromBytes(mapImage, filename: "mapImage"),
      "metadata": jsonEncode({
        "lat": location.latitude,
        "lng": location.longitude,
      })
    };
  }
}
