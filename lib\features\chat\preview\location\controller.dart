import 'dart:typed_data';

import 'package:get/get.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:gopal/core/config/defaults.dart';
import 'package:gopal/core/services/location.dart';
import 'package:gopal/core/services/state_management/observable_variable.dart';
import 'package:gopal/core/utils/action_stack.dart';
import 'package:gopal/core/utils/image_utils.dart';

class PickLocationPageController extends GetxController {
  late GoogleMapController mapController;

  ObsVar<LatLng> initLocation = ObsVar(null);
  init() async {
    cameraMovingActionStack = ActionStack<LatLng>(
      execute: (LatLng target) {
        isCameraMoving = false;
        currentLocation = target;
      },
      time: 500.milliseconds,
    );
    LatLng? result = await LocationUtils.getMyLocation(openSettings: false);
    if (result != null) {
      initLocation.value = result;
    } else {
      initLocation.value = Default.defaultLocation;
    }
    currentLocation = initLocation.value!;
  }

  @override
  void onInit() {
    init();
    super.onInit();
  }

  late LatLng currentLocation;

  final Rx<bool> _isCameraMoving = false.obs;
  bool get isCameraMoving => _isCameraMoving.value;
  set isCameraMoving(bool value) => _isCameraMoving.value = value;

  late ActionStack cameraMovingActionStack;

  confirm() async {
    Uint8List image = await ImageUtils.compressBytesImage(
        (await mapController.takeSnapshot())!);
    Get.back(
      result: (currentLocation, image),
    );
  }
}
