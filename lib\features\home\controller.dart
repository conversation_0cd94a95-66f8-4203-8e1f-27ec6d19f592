import 'dart:developer';

import 'package:easy_localization/easy_localization.dart';
import 'package:get/get.dart' hide Trans;
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/models/category.dart';
import 'package:gopal/core/models/user/center.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/services/location.dart';
import 'package:gopal/features/center/profile/models/nav.dart';
import 'package:gopal/features/home/<USER>/ad.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../core/models/activity/main_activity.dart';
import '../../core/services/rest_api/rest_api.dart';
import '../activities/activities/models/nav.dart';

class HomePageController extends GetxController {
  //SECTION - Categories
  Request<Category> categoriesRequest = Request(
    endPoint: EndPoints.categories,
    fromJson: Category.fromJson,
  );
  //!SECTION

  //SECTION - Slider

  final Rx<int> _currentImage = 0.obs;
  int get currentImage => _currentImage.value;
  set currentImage(int value) => _currentImage.value = value;

  Request<SliderAd> adsRequest = Request(
    endPoint: EndPoints.ads,
    fromJson: SliderAd.fromJson,
  );

  adClick(SliderAd ad) {
    switch (ad.actionType) {
      case AdActionType.internal:
        switch (ad.internalType!) {
          case InternalAdType.activity:
            return Nav.to(
              Pages.activity_details,
              arguments: ad.adableId,
              preventDuplicates: false,
            );
          case InternalAdType.provider:
            return Nav.to(
              Pages.center,
              arguments: CenterProfilePageNav(ad.adableId!),
              preventDuplicates: false,
            );
        }
      case AdActionType.external:
        launchUrl(Uri.parse(ad.link!), mode: LaunchMode.externalApplication);
        return;
      default:
        return;
    }
  }
  //!SECTION

  //SECTION - centers
  Request<CenterModel> centersRequest = Request(
    endPoint: EndPoints.centers,
    fromJson: CenterModel.fromJson,
  );

  centersRequestPerform() async {
    LatLng? result = await LocationUtils.getMyLocation(openSettings: true);
    if (result != null) {
      centersRequest.params = {"lat": result.latitude, "lng": result.longitude};
    }
    if (!centersRequest.isDone) {
      await centersRequest.perform();
    } else {
      await centersRequest.refresh();
    }
    if (result != null && centersRequest.response!.success) {
      (centersRequest.response!.data as List<CenterModel>).sort(
        (a, b) => a.distance!.compareTo(b.distance!),
      );
      centersRequest.status.refresh();
    }
  }
  //!SECTION

  //SECTION - Activities
  Request<MainActivity> familyActivitiesRequest = Request(
    endPoint: EndPoints.main_activities,
    params: {
      "filter": {"is_family": 1},
    },
    fromJson: MainActivity.fromJson,
  );

  Request<MainActivity> offeredActivitiesRequest = Request(
    endPoint: EndPoints.main_activities,
    params: {
      "filter": {"is_discounted": 1},
    },
    fromJson: MainActivity.fromJson,
  );

  Request<MainActivity> seasonalActivitiesRequest = Request(
    endPoint: EndPoints.main_activities,
    params: {
      "filter": {"in_season": 1},
    },
    fromJson: MainActivity.fromJson,
  );

  Request<MainActivity> recommendedActivitiesRequest = Request(
    endPoint: EndPoints.main_activities,
    params: {
      "filter": {"recommended": 1},
    },
    fromJson: MainActivity.fromJson,
  );
  //!SECTION

  //SECTION - Fetch and Refresh
  @override
  void onInit() {
    adsRequest.perform();
    centersRequestPerform();
    familyActivitiesRequest.perform();
    offeredActivitiesRequest.perform();
    seasonalActivitiesRequest.perform();
    recommendedActivitiesRequest.perform();
    categoriesRequest.perform();
    super.onInit();
  }

  refreshData() {
    // return refreshWhenError();
    categoriesRequest.refresh();
    adsRequest.refresh();
    centersRequestPerform();
    familyActivitiesRequest.refresh();
    offeredActivitiesRequest.refresh();
    seasonalActivitiesRequest.refresh();
    recommendedActivitiesRequest.refresh();
  }

  refreshWhenError() {
    if (categoriesRequest.isDone && !categoriesRequest.response!.success) {
      log("Refresh categories", name: "Refresh tracking");
      categoriesRequest.refresh();
    }
    if (adsRequest.isDone && !adsRequest.response!.success) {
      log("Refresh ads", name: "Refresh tracking");
      adsRequest.refresh();
    }
    if (centersRequest.isDone && !centersRequest.response!.success) {
      log("Refresh centers", name: "Refresh tracking");
      centersRequest.refresh();
    }
    if (familyActivitiesRequest.isDone &&
        !familyActivitiesRequest.response!.success) {
      log("Refresh families activities", name: "Refresh tracking");
      familyActivitiesRequest.refresh();
    }
    if (offeredActivitiesRequest.isDone &&
        !offeredActivitiesRequest.response!.success) {
      log("Refresh offered activities", name: "Refresh tracking");
      offeredActivitiesRequest.refresh();
    }
    if (seasonalActivitiesRequest.isDone &&
        !seasonalActivitiesRequest.response!.success) {
      log("Refresh seasonal activities", name: "Refresh tracking");
      seasonalActivitiesRequest.refresh();
    }
    if (recommendedActivitiesRequest.isDone &&
        !recommendedActivitiesRequest.response!.success) {
      log("Refresh recommended activities", name: "Refresh tracking");
      recommendedActivitiesRequest.refresh();
    }
  }
  //!SECTION

  //SECTION - Nav
  seeAllFamilyActivities() {
    Nav.to(
      Pages.activities,
      arguments: ActivitiesPageNav(
        title: LocaleKeys.family_activities.tr(),
        endPoint: EndPoints.main_activities,
        params: {
          "filter": {"is_family": 1},
        },
      ),
    );
  }

  seeAllTodaysOffersActivities() {
    Nav.to(
      Pages.activities,
      arguments: ActivitiesPageNav(
        title: LocaleKeys.todays_offers.tr(),
        endPoint: EndPoints.main_activities,
        params: {
          "filter": {"is_discounted": 1},
        },
      ),
    );
  }

  seeAllSeasonalActivities() {
    Nav.to(
      Pages.activities,
      arguments: ActivitiesPageNav(
        title: LocaleKeys.seasonal_activities.tr(),
        endPoint: EndPoints.main_activities,
        params: {
          "filter": {"in_season": 1},
        },
      ),
    );
  }

  seeAllRecommendedActivities() {
    Nav.to(
      Pages.activities,
      arguments: ActivitiesPageNav(
        title: LocaleKeys.recommended_activities.tr(),
        endPoint: EndPoints.main_activities,
        params: {
          "filter": {"recommended": 1},
        },
      ),
    );
  }

  //!SECTION
}
