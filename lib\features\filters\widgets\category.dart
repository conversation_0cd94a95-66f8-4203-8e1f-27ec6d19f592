import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/widgets/error_widget.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/widgets/general_componenets/dropdown.dart';

import '../controller.dart';
import '../models/categories.dart/main_category.dart';

class CategoriesFilter extends GetView<FiltersPageController> {
  const CategoriesFilter({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          LocaleKeys.categories.tr(),
          style: context.textTheme.titleMedium!.copyWith(
            fontWeight: FontWeight.w700,
          ),
        ),
        const Gap(6),
        Obx(
          () {
            if (controller.categories.loading) {
              return const FieldLoadingWidget();
            } else if (controller.categories.hasError) {
              return FieldErrorWidget(error: controller.categories.error!);
            } else {
              return AppDropdown<CategoryFilter>(
                value: controller.selectedCategory,
                items: List.generate(
                  controller.categories.valueLength,
                  (index) => DropdownMenuItem<CategoryFilter>(
                    value: controller.categories[index],
                    child: Text(controller.categories[index].name),
                  ),
                ),
                onChanged: (value) => controller.selectedCategory = value!,
              );
            }
          },
        ),
      ],
    );
  }
}
