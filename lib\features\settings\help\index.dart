import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/features/widgets/general_componenets/pages/rounded_page.dart';

import 'controller.dart';

class HelpPage extends StatelessWidget {
  const HelpPage({super.key});

  @override
  Widget build(BuildContext context) {
    HelpPageController controller = Get.put(HelpPageController());
    return RoundedPage(
      title: Text(LocaleKeys.help_and_support.tr()),
      child: controller.request.listBuilder(
        builder: (context, html) {
          return Html(data: html.first);
        },
      ),
    );
  }
}
