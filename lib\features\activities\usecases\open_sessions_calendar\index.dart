import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/models/activity/constants/price_type.dart';

import 'controller.dart';
import 'widgets/open_sessions.dart';

class OpenSessionsDataWidget extends StatelessWidget {
  final int id;
  final DateTime minDate, maxDate;
  final int? minPersons, maxPersons;
  final PriceType priceType;
  const OpenSessionsDataWidget({
    super.key,
    required this.id,
    required this.minDate,
    required this.maxDate,
    required this.priceType,
    this.minPersons,
    this.maxPersons,
  });

  @override
  Widget build(BuildContext context) {
    if (maxDate.isBefore(DateTime.now())) {
      return const SizedBox();
    }
    Get.put(
      OpenSessionsController(id: id, minDate: minDate, maxDate: maxDate),
      tag: "$id",
    );
    return OpenSessions(
      id: id,
      minPersons: minPersons,
      maxPersons: maxPersons,
      priceType: priceType,
    );
  }
}
