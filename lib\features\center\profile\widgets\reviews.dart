import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/features/center/widgets/review_card.dart';

import '../controller.dart';

class CenterReviews extends StatelessWidget {
  final int centerId;
  const CenterReviews({super.key, required this.centerId});

  @override
  Widget build(BuildContext context) {
    CenterProfilePageController controller = Get.find(tag: "$centerId");
    return controller.reviewsRequest.listBuilder(
      builder: (context, reviews) {
        if (reviews.isEmpty) {
          return const SizedBox();
        }
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                LocaleKeys.reviews.tr(),
                style: context.textTheme.titleLarge!
                    .copyWith(color: StyleRepo.blueViolet),
              ),
              const Gap(12),
              ListView.separated(
                itemCount: reviews.length,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                separatorBuilder: (_, __) => const Divider(),
                itemBuilder: (context, index) =>
                    ReviewCard(review: reviews[index]),
              ),
              const Gap(12),
              Center(
                child: InkWell(
                  onTap: () =>
                      Nav.to(Pages.center_reviews, arguments: centerId),
                  child: Text(
                    LocaleKeys.see_all.tr(),
                    style: context.textTheme.titleMedium!.copyWith(
                      color: StyleRepo.blueViolet,
                      fontWeight: FontWeight.w600,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
