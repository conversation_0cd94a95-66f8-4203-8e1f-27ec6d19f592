import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/utils/date.dart';

import 'models/result.dart';

class CustomSessionDialogController extends GetxController {
  //SECTION - Date Selection
  DateTime selectedDate = DateTime.now();
  TextEditingController dateController =
      TextEditingController(text: DateFormat.yMd().format(DateTime.now()));
  selectDate(DateTime date) {
    selectedDate = date;
    dateController.text = DateFormat.yMd().format(date);
    if (date.isToday) {
      selectFrom(TimeOfDay(
          hour: TimeOfDay.now().hour + 1, minute: TimeOfDay.now().minute));
      selectTo(TimeOfDay(
          hour: TimeOfDay.now().hour + 2, minute: TimeOfDay.now().minute));
    }
  }

  pickDate(context) async {
    DateTime? pickedDate = await showDatePicker(
      context: context,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDate: selectedDate,
    );
    if (pickedDate != null) {
      selectDate(pickedDate);
    }
  }
  //!SECTION

  //SECTION - From selection
  TimeOfDay selectedFrom =
      TimeOfDay(hour: TimeOfDay.now().hour + 1, minute: TimeOfDay.now().minute);
  TextEditingController fromController = TextEditingController(
      text: TimeOfDay(
              hour: TimeOfDay.now().hour + 1, minute: TimeOfDay.now().minute)
          .format(Get.context!));
  selectFrom(TimeOfDay time) {
    selectedFrom = time;
    fromController.text = time.format(Get.context!);
  }

  pickFrom(context) async {
    TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: selectedFrom,
    );
    if (pickedTime != null) {
      if (selectedDate.isToday &&
          pickedTime.isBefore(TimeOfDay.now().addDuration(50.minutes))) {
        return;
      }
      selectFrom(pickedTime);
    }
  }
  //!SECTION

  //SECTION - To selection
  TimeOfDay selectedTo =
      TimeOfDay(hour: TimeOfDay.now().hour + 2, minute: TimeOfDay.now().minute);
  TextEditingController toController = TextEditingController(
      text: TimeOfDay(
              hour: TimeOfDay.now().hour + 2, minute: TimeOfDay.now().minute)
          .format(Get.context!));
  selectTo(TimeOfDay time) {
    selectedTo = time;
    toController.text = time.format(Get.context!);
  }

  pickTo(context) async {
    TimeOfDay? pickedTime = await showTimePicker(
      context: context,
      initialTime: selectedTo,
    );
    if (pickedTime != null) {
      selectTo(pickedTime);
    }
  }
  //!SECTION

  //SECTION - Confirmation
  confirm() {
    if (selectedTo.isBefore(selectedFrom)) {
      Get.back(
        result: [
          DateTimeSelectionResult(
            date: selectedDate,
            from: selectedFrom,
            to: const TimeOfDay(hour: 23, minute: 59),
          ),
          DateTimeSelectionResult(
            date: selectedDate.add(1.days),
            from: const TimeOfDay(hour: 0, minute: 0),
            to: selectedTo,
          ),
        ],
      );
    } else {
      Get.back(
        result: [
          DateTimeSelectionResult(
            date: selectedDate,
            from: selectedFrom,
            to: selectedTo,
          )
        ],
      );
    }
  }
  //!SECTION
}
