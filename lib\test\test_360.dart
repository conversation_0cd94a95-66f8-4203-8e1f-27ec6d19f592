// import 'package:flutter/material.dart';
// import 'package:gopal/core/style/assets/gen/assets.gen.dart';
// import 'package:panorama_viewer/panorama_viewer.dart';

// class PanoramaTest extends StatelessWidget {
//   const PanoramaTest({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       body: Center(
//         child: PanoramaViewer(
//           sensorControl: SensorControl.orientation,
//           animSpeed: 1,
//           child: Image.asset(Assets.images.panorama.path),
//         ),
//       ),
//     );
//   }
// }
