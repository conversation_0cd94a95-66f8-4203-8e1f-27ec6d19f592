import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:gopal/core/localization/strings.dart';

enum AppLocalization {
  ar,
  en;

  static AppLocalization fromString(String s) {
    switch (s) {
      case "ar":
        return AppLocalization.ar;
      case "en":
        return AppLocalization.en;
      default:
        throw Exception("not valid locale");
      // return Default.defaultLocale;
    }
  }

  String get value {
    switch (this) {
      case AppLocalization.ar:
        return 'ar';
      case AppLocalization.en:
        return 'en';
    }
  }

  String get text {
    switch (this) {
      case AppLocalization.ar:
        return LocaleKeys.arabic.tr();
      case AppLocalization.en:
        return LocaleKeys.english.tr();
    }
  }

  String get flagEmoji {
    switch (this) {
      case ar:
        return '\u{1F1F8}\u{1F1E6}';
      case en:
        return '\u{1F1FA}\u{1F1F8}';
    }
  }

  bool get isArabic => this == AppLocalization.ar;

  bool get isEnglish => this == AppLocalization.en;

  Locale get locale {
    switch (this) {
      case AppLocalization.ar:
        return const Locale('ar');
      case AppLocalization.en:
        return const Locale('en');
    }
  }
}
