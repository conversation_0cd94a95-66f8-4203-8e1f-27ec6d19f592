# Summary

Date : 2024-12-31 10:39:18

Directory d:\\work\\ixCoders\\current\\go_pal\\go_pal_flutter\\lib

Total : 381 files,  24722 codes, 620 comments, 2397 blanks, all 27739 lines

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Languages
| language | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| Dart | 381 | 24,722 | 620 | 2,397 | 27,739 |

## Directories
| path | files | code | comment | blank | total |
| :--- | ---: | ---: | ---: | ---: | ---: |
| . | 381 | 24,722 | 620 | 2,397 | 27,739 |
| . (Files) | 1 | 79 | 0 | 11 | 90 |
| core | 116 | 6,724 | 439 | 954 | 8,117 |
| core (Files) | 1 | 269 | 6 | 12 | 287 |
| core\\config | 4 | 263 | 24 | 63 | 350 |
| core\\constants | 3 | 24 | 0 | 11 | 35 |
| core\\demo | 2 | 125 | 9 | 14 | 148 |
| core\\localization | 4 | 857 | 5 | 23 | 885 |
| core\\models | 32 | 1,149 | 11 | 192 | 1,352 |
| core\\models (Files) | 5 | 129 | 0 | 22 | 151 |
| core\\models\\activity | 8 | 285 | 0 | 43 | 328 |
| core\\models\\activity (Files) | 4 | 116 | 0 | 19 | 135 |
| core\\models\\activity\\constants | 2 | 56 | 0 | 5 | 61 |
| core\\models\\activity\\periodic | 2 | 113 | 0 | 19 | 132 |
| core\\models\\book | 3 | 129 | 1 | 22 | 152 |
| core\\models\\chat | 6 | 295 | 1 | 64 | 360 |
| core\\models\\chat (Files) | 1 | 25 | 0 | 4 | 29 |
| core\\models\\chat\\messages | 5 | 270 | 1 | 60 | 331 |
| core\\models\\constants | 4 | 75 | 0 | 12 | 87 |
| core\\models\\notifications | 3 | 121 | 9 | 16 | 146 |
| core\\models\\notifications (Files) | 2 | 71 | 9 | 7 | 87 |
| core\\models\\notifications\\data | 1 | 50 | 0 | 9 | 59 |
| core\\models\\user | 3 | 115 | 0 | 13 | 128 |
| core\\services | 44 | 2,733 | 125 | 377 | 3,235 |
| core\\services (Files) | 2 | 58 | 0 | 9 | 67 |
| core\\services\\feedback | 3 | 122 | 0 | 19 | 141 |
| core\\services\\feedback (Files) | 1 | 49 | 0 | 11 | 60 |
| core\\services\\feedback\\widgets | 2 | 73 | 0 | 8 | 81 |
| core\\services\\firebase_messaging | 5 | 255 | 16 | 34 | 305 |
| core\\services\\firebase_messaging (Files) | 2 | 166 | 4 | 23 | 193 |
| core\\services\\firebase_messaging\\constants | 2 | 53 | 12 | 6 | 71 |
| core\\services\\firebase_messaging\\widgets | 1 | 36 | 0 | 5 | 41 |
| core\\services\\local_notifications | 1 | 115 | 6 | 16 | 137 |
| core\\services\\notifications_click | 1 | 88 | 3 | 4 | 95 |
| core\\services\\notifications_counter | 1 | 48 | 1 | 8 | 57 |
| core\\services\\pagination | 9 | 751 | 20 | 73 | 844 |
| core\\services\\pagination (Files) | 1 | 133 | 3 | 17 | 153 |
| core\\services\\pagination\\options | 3 | 464 | 17 | 35 | 516 |
| core\\services\\pagination\\widgets | 5 | 154 | 0 | 21 | 175 |
| core\\services\\player | 3 | 118 | 4 | 22 | 144 |
| core\\services\\register_fcm | 2 | 66 | 1 | 12 | 79 |
| core\\services\\rest_api | 14 | 885 | 65 | 126 | 1,076 |
| core\\services\\rest_api (Files) | 2 | 294 | 12 | 20 | 326 |
| core\\services\\rest_api\\constants | 3 | 73 | 25 | 29 | 127 |
| core\\services\\rest_api\\handlers | 2 | 116 | 2 | 8 | 126 |
| core\\services\\rest_api\\logger | 1 | 38 | 0 | 6 | 44 |
| core\\services\\rest_api\\models | 3 | 154 | 19 | 38 | 211 |
| core\\services\\rest_api\\utilitis | 2 | 111 | 6 | 14 | 131 |
| core\\services\\rest_api\\widgets | 1 | 99 | 1 | 11 | 111 |
| core\\services\\share | 1 | 44 | 0 | 6 | 50 |
| core\\services\\state_management | 2 | 183 | 9 | 48 | 240 |
| core\\style | 8 | 605 | 134 | 156 | 895 |
| core\\style (Files) | 3 | 152 | 19 | 19 | 190 |
| core\\style\\assets | 3 | 436 | 115 | 131 | 682 |
| core\\style\\assets (Files) | 1 | 3 | 0 | 0 | 3 |
| core\\style\\assets\\gen | 2 | 433 | 115 | 131 | 679 |
| core\\style\\utils | 2 | 17 | 0 | 6 | 23 |
| core\\utils | 12 | 361 | 124 | 71 | 556 |
| core\\widgets | 6 | 338 | 1 | 35 | 374 |
| features | 263 | 17,901 | 181 | 1,429 | 19,511 |
| features\\activities | 49 | 3,816 | 34 | 260 | 4,110 |
| features\\activities\\activities | 4 | 68 | 0 | 12 | 80 |
| features\\activities\\activities (Files) | 3 | 62 | 0 | 11 | 73 |
| features\\activities\\activities\\models | 1 | 6 | 0 | 1 | 7 |
| features\\activities\\activitiy_details | 11 | 1,046 | 12 | 53 | 1,111 |
| features\\activities\\activitiy_details (Files) | 3 | 265 | 12 | 24 | 301 |
| features\\activities\\activitiy_details\\models | 1 | 66 | 0 | 5 | 71 |
| features\\activities\\activitiy_details\\widgets | 7 | 715 | 0 | 24 | 739 |
| features\\activities\\main_activity | 7 | 485 | 3 | 36 | 524 |
| features\\activities\\main_activity (Files) | 3 | 70 | 0 | 14 | 84 |
| features\\activities\\main_activity\\models | 2 | 102 | 0 | 8 | 110 |
| features\\activities\\main_activity\\widgets | 2 | 313 | 3 | 14 | 330 |
| features\\activities\\my_activities | 10 | 480 | 5 | 48 | 533 |
| features\\activities\\my_activities (Files) | 2 | 237 | 5 | 19 | 261 |
| features\\activities\\my_activities\\models | 3 | 52 | 0 | 10 | 62 |
| features\\activities\\my_activities\\widgets | 5 | 191 | 0 | 19 | 210 |
| features\\activities\\usecases | 10 | 967 | 14 | 87 | 1,068 |
| features\\activities\\usecases\\booking | 1 | 168 | 8 | 25 | 201 |
| features\\activities\\usecases\\open_sessions_calendar | 4 | 355 | 5 | 30 | 390 |
| features\\activities\\usecases\\open_sessions_calendar (Files) | 2 | 101 | 5 | 21 | 127 |
| features\\activities\\usecases\\open_sessions_calendar\\widgets | 2 | 254 | 0 | 9 | 263 |
| features\\activities\\usecases\\points_selection | 1 | 109 | 0 | 5 | 114 |
| features\\activities\\usecases\\specific_days_calendar | 4 | 335 | 1 | 27 | 363 |
| features\\activities\\usecases\\specific_days_calendar (Files) | 2 | 107 | 1 | 18 | 126 |
| features\\activities\\usecases\\specific_days_calendar\\widgets | 2 | 228 | 0 | 9 | 237 |
| features\\activities\\widgets | 7 | 770 | 0 | 24 | 794 |
| features\\auth | 19 | 933 | 4 | 83 | 1,020 |
| features\\auth\\create_account | 8 | 419 | 1 | 36 | 456 |
| features\\auth\\create_account (Files) | 3 | 168 | 0 | 22 | 190 |
| features\\auth\\create_account\\models | 2 | 5 | 1 | 2 | 8 |
| features\\auth\\create_account\\widgets | 3 | 246 | 0 | 12 | 258 |
| features\\auth\\login | 3 | 185 | 1 | 15 | 201 |
| features\\auth\\login (Files) | 2 | 136 | 1 | 12 | 149 |
| features\\auth\\login\\widgets | 1 | 49 | 0 | 3 | 52 |
| features\\auth\\verification | 6 | 269 | 2 | 26 | 297 |
| features\\auth\\verification (Files) | 3 | 176 | 2 | 16 | 194 |
| features\\auth\\verification\\models | 1 | 4 | 0 | 2 | 6 |
| features\\auth\\verification\\widgets | 2 | 89 | 0 | 8 | 97 |
| features\\auth\\widgets | 2 | 60 | 0 | 6 | 66 |
| features\\boarding | 3 | 176 | 0 | 16 | 192 |
| features\\boarding (Files) | 2 | 152 | 0 | 12 | 164 |
| features\\boarding\\models | 1 | 24 | 0 | 4 | 28 |
| features\\books | 9 | 1,112 | 11 | 60 | 1,183 |
| features\\books\\book_details | 8 | 732 | 6 | 48 | 786 |
| features\\books\\book_details (Files) | 3 | 197 | 6 | 30 | 233 |
| features\\books\\book_details\\widgets | 5 | 535 | 0 | 18 | 553 |
| features\\books\\widgets | 1 | 380 | 5 | 12 | 397 |
| features\\categories | 10 | 384 | 0 | 44 | 428 |
| features\\categories\\categories | 5 | 159 | 0 | 19 | 178 |
| features\\categories\\categories (Files) | 2 | 58 | 0 | 9 | 67 |
| features\\categories\\categories\\widgets | 3 | 101 | 0 | 10 | 111 |
| features\\categories\\category | 5 | 225 | 0 | 25 | 250 |
| features\\categories\\category (Files) | 3 | 154 | 0 | 19 | 173 |
| features\\categories\\category\\models | 1 | 5 | 0 | 2 | 7 |
| features\\categories\\category\\widgets | 1 | 66 | 0 | 4 | 70 |
| features\\center | 25 | 1,100 | 1 | 104 | 1,205 |
| features\\center\\centers | 4 | 86 | 0 | 16 | 102 |
| features\\center\\centers (Files) | 3 | 82 | 0 | 15 | 97 |
| features\\center\\centers\\models | 1 | 4 | 0 | 1 | 5 |
| features\\center\\centers_on_map | 3 | 105 | 1 | 14 | 120 |
| features\\center\\centers_on_map (Files) | 2 | 80 | 1 | 10 | 91 |
| features\\center\\centers_on_map\\models | 1 | 25 | 0 | 4 | 29 |
| features\\center\\models | 1 | 8 | 0 | 3 | 11 |
| features\\center\\profile | 12 | 676 | 0 | 53 | 729 |
| features\\center\\profile (Files) | 3 | 175 | 0 | 16 | 191 |
| features\\center\\profile\\models | 2 | 68 | 0 | 11 | 79 |
| features\\center\\profile\\widgets | 7 | 433 | 0 | 26 | 459 |
| features\\center\\reviews | 3 | 54 | 0 | 11 | 65 |
| features\\center\\widgets | 2 | 171 | 0 | 7 | 178 |
| features\\chat | 25 | 2,453 | 32 | 211 | 2,696 |
| features\\chat\\preview | 11 | 586 | 1 | 63 | 650 |
| features\\chat\\preview\\document | 3 | 134 | 0 | 14 | 148 |
| features\\chat\\preview\\images | 3 | 176 | 0 | 17 | 193 |
| features\\chat\\preview\\location | 2 | 120 | 1 | 13 | 134 |
| features\\chat\\preview\\video | 3 | 156 | 0 | 19 | 175 |
| features\\chat\\room | 10 | 1,689 | 28 | 132 | 1,849 |
| features\\chat\\room (Files) | 3 | 521 | 24 | 92 | 637 |
| features\\chat\\room\\widgets | 7 | 1,168 | 4 | 40 | 1,212 |
| features\\chat\\room\\widgets (Files) | 3 | 543 | 1 | 24 | 568 |
| features\\chat\\room\\widgets\\messages | 4 | 625 | 3 | 16 | 644 |
| features\\chat\\rooms | 4 | 178 | 3 | 16 | 197 |
| features\\chat\\rooms (Files) | 2 | 66 | 3 | 10 | 79 |
| features\\chat\\rooms\\widgets | 2 | 112 | 0 | 6 | 118 |
| features\\create_child | 7 | 685 | 8 | 47 | 740 |
| features\\create_child (Files) | 3 | 214 | 8 | 27 | 249 |
| features\\create_child\\models | 2 | 96 | 0 | 10 | 106 |
| features\\create_child\\widgets | 2 | 375 | 0 | 10 | 385 |
| features\\filters | 13 | 951 | 36 | 75 | 1,062 |
| features\\filters (Files) | 2 | 305 | 3 | 28 | 336 |
| features\\filters\\models | 3 | 95 | 0 | 14 | 109 |
| features\\filters\\models (Files) | 2 | 70 | 0 | 9 | 79 |
| features\\filters\\models\\categories.dart | 1 | 25 | 0 | 5 | 30 |
| features\\filters\\widgets | 8 | 551 | 33 | 33 | 617 |
| features\\home | 9 | 913 | 12 | 54 | 979 |
| features\\home (Files) | 2 | 314 | 12 | 20 | 346 |
| features\\home\\models | 1 | 86 | 0 | 11 | 97 |
| features\\home\\widgets | 6 | 513 | 0 | 23 | 536 |
| features\\main | 7 | 438 | 5 | 30 | 473 |
| features\\main (Files) | 2 | 92 | 0 | 8 | 100 |
| features\\main\\models | 1 | 34 | 0 | 4 | 38 |
| features\\main\\widgets | 4 | 312 | 5 | 18 | 335 |
| features\\notifications | 3 | 133 | 1 | 12 | 146 |
| features\\notifications (Files) | 2 | 61 | 1 | 8 | 70 |
| features\\notifications\\widgets | 1 | 72 | 0 | 4 | 76 |
| features\\profile | 24 | 1,510 | 9 | 125 | 1,644 |
| features\\profile\\change_phone | 2 | 78 | 1 | 9 | 88 |
| features\\profile\\child_profile | 11 | 687 | 3 | 59 | 749 |
| features\\profile\\child_profile (Files) | 3 | 257 | 3 | 27 | 287 |
| features\\profile\\child_profile\\models | 3 | 84 | 0 | 11 | 95 |
| features\\profile\\child_profile\\widgets | 5 | 346 | 0 | 21 | 367 |
| features\\profile\\edit_profile | 5 | 295 | 4 | 27 | 326 |
| features\\profile\\edit_profile (Files) | 3 | 115 | 4 | 21 | 140 |
| features\\profile\\edit_profile\\models | 1 | 5 | 0 | 2 | 7 |
| features\\profile\\edit_profile\\widgets | 1 | 175 | 0 | 4 | 179 |
| features\\profile\\my_profile | 6 | 450 | 1 | 30 | 481 |
| features\\profile\\my_profile (Files) | 2 | 234 | 1 | 17 | 252 |
| features\\profile\\my_profile\\models | 1 | 17 | 0 | 3 | 20 |
| features\\profile\\my_profile\\widgets | 3 | 199 | 0 | 10 | 209 |
| features\\search | 4 | 228 | 1 | 23 | 252 |
| features\\search (Files) | 2 | 109 | 1 | 15 | 125 |
| features\\search\\widgets | 2 | 119 | 0 | 8 | 127 |
| features\\settings | 20 | 775 | 4 | 95 | 874 |
| features\\settings\\about | 2 | 41 | 0 | 9 | 50 |
| features\\settings\\help | 2 | 41 | 0 | 9 | 50 |
| features\\settings\\invitation | 2 | 107 | 1 | 15 | 123 |
| features\\settings\\privacy | 2 | 42 | 0 | 8 | 50 |
| features\\settings\\support_replies | 4 | 152 | 1 | 15 | 168 |
| features\\settings\\support_replies (Files) | 2 | 45 | 1 | 7 | 53 |
| features\\settings\\support_replies\\models | 1 | 27 | 0 | 4 | 31 |
| features\\settings\\support_replies\\widgets | 1 | 80 | 0 | 4 | 84 |
| features\\settings\\terms | 2 | 41 | 0 | 9 | 50 |
| features\\settings\\usecases | 6 | 351 | 2 | 30 | 383 |
| features\\settings\\usecases\\points | 3 | 174 | 1 | 12 | 187 |
| features\\settings\\usecases\\points (Files) | 2 | 156 | 1 | 9 | 166 |
| features\\settings\\usecases\\points\\models | 1 | 18 | 0 | 3 | 21 |
| features\\settings\\usecases\\support | 3 | 177 | 1 | 18 | 196 |
| features\\settings\\usecases\\support (Files) | 2 | 149 | 1 | 13 | 163 |
| features\\settings\\usecases\\support\\widgets | 1 | 28 | 0 | 5 | 33 |
| features\\splash_screen | 2 | 36 | 3 | 8 | 47 |
| features\\usecases | 20 | 1,562 | 10 | 113 | 1,685 |
| features\\usecases\\book_custom_session | 3 | 231 | 8 | 18 | 257 |
| features\\usecases\\book_custom_session (Files) | 2 | 224 | 8 | 15 | 247 |
| features\\usecases\\book_custom_session\\models | 1 | 7 | 0 | 3 | 10 |
| features\\usecases\\children_selection | 2 | 261 | 1 | 23 | 285 |
| features\\usecases\\children_selection (Files) | 1 | 81 | 1 | 16 | 98 |
| features\\usecases\\children_selection\\widgets | 1 | 180 | 0 | 7 | 187 |
| features\\usecases\\confirmation | 1 | 102 | 0 | 5 | 107 |
| features\\usecases\\gender | 4 | 458 | 0 | 20 | 478 |
| features\\usecases\\gender (Files) | 1 | 133 | 0 | 6 | 139 |
| features\\usecases\\gender\\widgets | 3 | 325 | 0 | 14 | 339 |
| features\\usecases\\phone_field | 1 | 80 | 0 | 5 | 85 |
| features\\usecases\\pictures | 1 | 93 | 0 | 6 | 99 |
| features\\usecases\\players | 3 | 106 | 1 | 16 | 123 |
| features\\usecases\\players (Files) | 1 | 17 | 0 | 2 | 19 |
| features\\usecases\\players\\video | 2 | 89 | 1 | 14 | 104 |
| features\\usecases\\rating | 2 | 137 | 0 | 11 | 148 |
| features\\usecases\\toast | 3 | 94 | 0 | 9 | 103 |
| features\\usecases\\toast (Files) | 2 | 39 | 0 | 4 | 43 |
| features\\usecases\\toast\\widgets | 1 | 55 | 0 | 5 | 60 |
| features\\widgets | 14 | 696 | 10 | 69 | 775 |
| features\\widgets (Files) | 5 | 191 | 0 | 20 | 211 |
| features\\widgets\\backgrounds | 1 | 41 | 0 | 3 | 44 |
| features\\widgets\\general_componenets | 8 | 464 | 10 | 46 | 520 |
| features\\widgets\\general_componenets (Files) | 3 | 68 | 0 | 10 | 78 |
| features\\widgets\\general_componenets\\app_bars | 1 | 71 | 8 | 12 | 91 |
| features\\widgets\\general_componenets\\buttons | 2 | 180 | 2 | 14 | 196 |
| features\\widgets\\general_componenets\\pages | 2 | 145 | 0 | 10 | 155 |
| test | 1 | 18 | 0 | 3 | 21 |

Summary / [Details](details.md) / [Diff Summary](diff.md) / [Diff Details](diff-details.md)