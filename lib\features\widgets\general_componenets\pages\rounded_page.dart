import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';

class RoundedPage extends StatelessWidget {
  final Text title;
  final Widget child;
  final Widget? floatingActionButton;
  final List<Widget>? actions;
  final void Function()? onBack;
  final bool mustHasBack;

  const RoundedPage({
    super.key,
    required this.title,
    required this.child,
    this.floatingActionButton,
    this.actions,
    this.onBack,
    this.mustHasBack = false,
  });

  @override
  Widget build(BuildContext context) {
    final ModalRoute<dynamic>? parentRoute = ModalRoute.of(context);

    return Scaffold(
      floatingActionButton: floatingActionButton,
      body: Stack(
        fit: StackFit.expand,
        children: [
          Container(
            height: 90 + MediaQuery.of(context).padding.top,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Color(0xFF7640F3),
                  Color(0xFF936BBC),
                ],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
            ),
            alignment: Alignment.topCenter,
            child: Container(
              height: 90 + MediaQuery.of(context).padding.top,
              width: Get.width,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: Assets.images.roundedAppBarBackground.provider(),
                ),
              ),
              child: SafeArea(
                child: Center(
                  child: IconTheme(
                    data:
                        IconTheme.of(context).copyWith(color: StyleRepo.white),
                    child: DefaultTextStyle(
                      style: context.textTheme.headlineSmall!.copyWith(
                        color: StyleRepo.white,
                        fontWeight: FontWeight.w600,
                      ),
                      child: NavigationToolbar(
                        leading: mustHasBack ||
                                (parentRoute?.impliesAppBarDismissal ?? false)
                            ? BackButton(onPressed: onBack)
                            : null,
                        middle: title,
                        centerMiddle: true,
                        trailing: actions != null
                            ? Row(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: actions!,
                              )
                            : null,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          Positioned(
            top: 80 + MediaQuery.of(context).padding.top,
            bottom: 0,
            right: 0,
            left: 0,
            child: Container(
              decoration: const BoxDecoration(
                color: StyleRepo.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(45),
                  topRight: Radius.circular(45),
                ),
              ),
              child: ClipRRect(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(45),
                  topRight: Radius.circular(45),
                ),
                child: child,
              ),
            ),
          )
        ],
      ),
    );
  }
}
