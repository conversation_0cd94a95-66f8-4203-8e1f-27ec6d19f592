import 'package:get/get.dart';

import '../../filters/controller.dart';
import 'controller.dart';
import 'models/nav.dart';

class CategoryPageBindings extends Bindings {
  @override
  void dependencies() {
    assert(Get.arguments is CategoryPageNav);
    Get.put(CategoryPageController(Get.arguments));
    Get.put(FiltersPageController(
        initialCategoryId: (Get.arguments as CategoryPageNav).category?.id));
  }
}
