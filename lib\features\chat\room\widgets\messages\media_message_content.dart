import 'package:file_icon/file_icon.dart';
import 'package:flutter/material.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart';
import 'package:gopal/core/models/chat/messages/messages.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/image.dart';
import 'package:gopal/features/usecases/pictures/index.dart';
import 'package:gopal/features/usecases/players/video/index.dart';
import 'package:url_launcher/url_launcher.dart';

class MediaMessageContent extends StatelessWidget {
  final Message message;
  const MediaMessageContent({super.key, required this.message});

  @override
  Widget build(BuildContext context) {
    if (message is SendMessage) {
      if (message.asSendMessage.content is SendImagesMessageContent) {
        SendImagesMessageContent content =
            message.asSendMessage.content.asImages;
        if (content.images.length == 1) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AspectRatio(
                aspectRatio: 1,
                child: AppImage(
                  path: content.images.first,
                  type: ImageType.File,
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: StyleRepo.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: StyleRepo.white),
                  ),
                ),
              ),
              if (content.content != null && content.content!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 8,
                  ),
                  child: Text(content.content!),
                ),
            ],
          );
        } else {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                padding: const EdgeInsets.all(8),
                itemCount:
                    content.images.length.isEven
                        ? content.images.length
                        : content.images.length - 1,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  mainAxisSpacing: 4,
                  crossAxisSpacing: 4,
                ),
                itemBuilder: (context, index) {
                  return AppImage(
                    path: content.images[index],
                    type: ImageType.File,
                    decoration: BoxDecoration(
                      color: StyleRepo.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: StyleRepo.white),
                    ),
                  );
                },
              ),
              if (content.images.length.isOdd)
                AspectRatio(
                  aspectRatio: 1,
                  child: AppImage(
                    path: content.images.last,
                    type: ImageType.File,
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    decoration: BoxDecoration(
                      color: StyleRepo.white,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: StyleRepo.white),
                    ),
                  ),
                ),
              if (content.images.length.isOdd) const Gap(8),
              if (content.content != null && content.content!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 8,
                  ),
                  child: Text(content.content!),
                ),
            ],
          );
        }
      }
      if (message.asSendMessage.content is SendDocumentMessageContent) {
        SendDocumentMessageContent content =
            message.asSendMessage.content.asDocument;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(color: StyleRepo.white),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  FileIcon(content.documentFile, size: 50),
                  const Gap(12),
                  Expanded(child: Text(content.fileName)),
                ],
              ),
            ),
            if (content.content != null && content.content!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                child: Text(content.content!),
              ),
          ],
        );
      }
      if (message.asSendMessage.content is SendVideoMessageContent) {
        SendVideoMessageContent content = message.asSendMessage.content.asVideo;
        return Column(
          children: [
            if (content.thumb == null)
              const Padding(
                padding: EdgeInsets.all(8.0),
                child: Row(
                  children: [
                    Text("Sending video"),
                    Gap(12),
                    SizedBox(
                      height: 25,
                      width: 25,
                      child: CircularProgressIndicator(),
                    ),
                  ],
                ),
              ),
            if (content.thumb != null)
              AspectRatio(
                aspectRatio: 1,
                child: Container(
                  margin: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: StyleRepo.white,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: StyleRepo.white),
                    image: DecorationImage(
                      image: MemoryImage(content.thumb!),
                      fit: BoxFit.cover,
                    ),
                  ),
                  child: Stack(
                    children: [
                      Center(
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: StyleRepo.black.withValues(alpha: .2),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.play_arrow_outlined,
                            size: 50,
                          ),
                        ),
                      ),
                      const Center(
                        child: SizedBox(
                          height: 50,
                          width: 50,
                          child: CircularProgressIndicator(),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            if (content.content != null && content.content!.isNotEmpty)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                child: Text(content.content!),
              ),
          ],
        );
      }
    } else if (message is ReceivedMessage) {
      if (message.asReceivedMessage.content is ReceivedMediaMessageContent) {
        ReceivedMediaMessageContent content =
            message.asReceivedMessage.content.asMedia;
        if (content.files.length == 1) {
          if (content.mediaType.isImages) {
            return InkWell(
              onTap:
                  () => MediaPreview.preview(
                    media: [content.files.first.original],
                  ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AspectRatio(
                    aspectRatio: 1,
                    child: AppImage(
                      path: content.files.first.medium,
                      type: ImageType.CachedNetwork,
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: StyleRepo.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: StyleRepo.white),
                      ),
                    ),
                  ),
                  if (content.content != null && content.content!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 8,
                      ),
                      child: Text(content.content!),
                    ),
                ],
              ),
            );
          } else if (content.mediaType.isDoc) {
            return InkWell(
              onTap: () => launchUrl(Uri.parse(content.files.first.original)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    margin: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      border: Border.all(color: StyleRepo.white),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      children: [
                        FileIcon(content.files.first.original, size: 50),
                        const Gap(12),
                        Expanded(child: Text(content.files.first.name)),
                      ],
                    ),
                  ),
                  if (content.content != null && content.content!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 8,
                      ),
                      child: Text(content.content!),
                    ),
                ],
              ),
            );
          } else if (content.mediaType.isVideo) {
            return InkWell(
              onTap:
                  () => Get.to(
                    VideoPlayerWidget(video: content.files.first.original),
                  ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  AspectRatio(
                    aspectRatio: 1,
                    child: Stack(
                      children: [
                        AppImage(
                          path: content.files.first.small,
                          type: ImageType.CachedNetwork,
                          margin: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: StyleRepo.white,
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: StyleRepo.white),
                          ),
                        ),
                        Center(
                          child: Container(
                            padding: const EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              color: StyleRepo.black.withValues(alpha: .2),
                              shape: BoxShape.circle,
                            ),
                            child: const Icon(
                              Icons.play_arrow_outlined,
                              size: 50,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (content.content != null && content.content!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                        horizontal: 8,
                      ),
                      child: Text(content.content!),
                    ),
                ],
              ),
            );
          }
        } else {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                padding: const EdgeInsets.all(8),
                itemCount:
                    content.files.length.isEven
                        ? content.files.length
                        : content.files.length - 1,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  mainAxisSpacing: 4,
                  crossAxisSpacing: 4,
                ),
                itemBuilder: (context, index) {
                  return InkWell(
                    onTap:
                        () => MediaPreview.preview(
                          media: content.files.map((e) => e.original).toList(),
                          initialIndex: index,
                        ),
                    child: AppImage(
                      path: content.files[index].medium,
                      type: ImageType.CachedNetwork,
                      decoration: BoxDecoration(
                        color: StyleRepo.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: StyleRepo.white),
                      ),
                    ),
                  );
                },
              ),
              if (content.files.length.isOdd)
                AspectRatio(
                  aspectRatio: 1,
                  child: InkWell(
                    onTap:
                        () => MediaPreview.preview(
                          media: content.files.map((e) => e.original).toList(),
                          initialIndex: content.files.length - 1,
                        ),
                    child: AppImage(
                      path: content.files.last.medium,
                      type: ImageType.CachedNetwork,
                      margin: const EdgeInsets.symmetric(horizontal: 8),
                      decoration: BoxDecoration(
                        color: StyleRepo.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: StyleRepo.white),
                      ),
                    ),
                  ),
                ),
              if (content.files.length.isOdd) const Gap(8),
              if (content.content != null && content.content!.isNotEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 8,
                  ),
                  child: Text(content.content!),
                ),
            ],
          );
        }
      }
    }
    return const SizedBox();
  }
}
