import 'package:gopal/core/localization/strings.dart';

class APIErrorMessages {
  static const String noInternet = LocaleKeys.no_internet;
  static const String noData = LocaleKeys.no_data_found;
  static const String noAuth = "No Auth";
  static const String forbidden = LocaleKeys.forbidden;
  static const String validation = LocaleKeys.validation_error;
  static const String server = LocaleKeys.server_error;
  static const String unknown = "Unknown Error";
  static const String canceled = "Request has Canceled";
}
