import 'package:get/get.dart';
import 'package:gopal/core/models/activity/main_activity.dart';
import 'package:gopal/core/models/chat/chat_room.dart';
import 'package:gopal/core/routes.dart';
import 'package:gopal/features/activities/activities/models/nav.dart';

import '../../../core/services/rest_api/rest_api.dart';
import '../models/center_review.dart';
import 'models/center_details.dart';
import 'models/nav.dart';

class CenterProfilePageController extends GetxController {
  final CenterProfilePageNav nav;
  CenterProfilePageController(this.nav) {
    centerRequest = Request(
      endPoint: EndPoints.center_details(nav.id),
      fromJson: CenterDetails.fromJson,
    );
    activitiesRequest = Request(
      endPoint: EndPoints.main_activities,
      params: {"provider_id": nav.id},
      fromJson: MainActivity.fromJson,
    );
    reviewsRequest = Request(
      endPoint: EndPoints.center_reviews,
      params: {
        "page": 1,
        "page-size": 3,
        "reviewable_type": "PROVIDER",
        "reviewable_id": nav.id,
      },
      fromJson: CenterReview.fromJson,
    );
  }

  late Request<CenterDetails> centerRequest;
  late Request<MainActivity> activitiesRequest;
  late Request<CenterReview> reviewsRequest;

  @override
  void onInit() {
    centerRequest.perform();
    activitiesRequest.perform();
    reviewsRequest.perform();
    super.onInit();
  }

  Future<void> refreshData() async {
    centerRequest.refresh();
    activitiesRequest.refresh();
    reviewsRequest.refresh();
  }

  @override
  void onClose() {
    centerRequest.stop();
    activitiesRequest.stop();
    reviewsRequest.stop();
    super.onClose();
  }

  seeAllActivities() {
    Nav.to(
      Pages.activities,
      arguments: ActivitiesPageNav(
        title: (centerRequest.response!.data as CenterDetails).name,
        endPoint: EndPoints.main_activities,
        params: {"provider_id": nav.id},
      ),
    );
  }

  navToChat(ChatRoom room) async {
    Nav.to(
      Pages.chat_room,
      arguments: room,
    );
  }
}
