import 'dart:ui';

import 'package:gopal/core/models/errors/version_update_data.dart';
import 'package:gopal/core/widgets/dialogs/update_app_dialog.dart';
import 'states.dart';

export 'states.dart';

typedef VersionCheckResult = ({VersionState state, VoidCallback action});

class VersionChecker {
  static final VersionChecker _singleton = VersionChecker._internal();

  factory VersionChecker() => _singleton;

  VersionChecker._internal();

  VersionCheckingState _checkingState = VersionCheckingState.unchecked;

  VersionState? _state;

  getInvalidVersionInAPI(VersionUpdateData data, bool isSoft) {
    _state = isSoft ? VersionState.soft : VersionState.hard;
    _action(data);
  }

  void _action(VersionUpdateData data) {
    if (_state == null) return;
    if (_state!.isGood) return;
    if (_checkingState.isChecked) return;

    UpdateAppDialog.open(canSkip: _state!.isSoft, data: data);
    _checkingState = VersionCheckingState.checked;
  }
}
