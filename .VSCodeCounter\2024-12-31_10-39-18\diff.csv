"filename", "language", "Dart", "comment", "blank", "total"
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\config\app_builder.dart", "Dart", 2, 0, 0, 2
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\config\defaults.dart", "Dart", 1, 0, 1, 2
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\config\role.dart", "Dart", 2, 0, 0, 2
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\constants\controllers_tags.dart", "Dart", 2, 0, 1, 3
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\localization\codegen_loader.g.dart", "Dart", 72, 0, 0, 72
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\localization\strings.dart", "Dart", 30, 0, 0, 30
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\models\activity\activity.dart", "Dart", -47, 0, -6, -53
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\models\activity\constants\price_type.dart", "Dart", 15, 0, 2, 17
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\models\activity\constants\type.dart", "Dart", 41, 0, 3, 44
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\models\activity\main_activity.dart", "Dart", 34, 0, 6, 40
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\models\activity\set.dart", "Dart", 11, 0, 3, 14
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\models\activity\subactivity.dart", "Dart", 47, 0, 6, 53
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\models\book\book.dart", "Dart", 11, 0, 1, 12
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\models\book\payment_status.dart", "Dart", 25, 0, 3, 28
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\models\constants\support_number.dart", "Dart", 3, 0, 1, 4
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\models\image.dart", "Dart", 7, 0, 1, 8
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\models\notifications\data\main_notification_data.dart", "Dart", 16, 0, 1, 17
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\models\notifications\notification.dart", "Dart", 5, 1, 0, 6
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\models\notifications\type.dart", "Dart", 3, 1, 0, 4
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\models\user\main_user.dart", "Dart", 5, 0, 0, 5
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\routes.dart", "Dart", 20, 0, 0, 20
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\services\deep_link.dart", "Dart", 0, -83, -9, -92
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\services\firebase_messaging\firebase_messaging.dart", "Dart", 2, 0, 1, 3
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\services\firebase_messaging\notifications_actions.dart", "Dart", 1, 0, 0, 1
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\services\local_notifications\local_notification.dart", "Dart", 21, 4, 5, 30
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\services\notification_controller.dart", "Dart", 0, 0, -2, -2
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\services\notifications_click\notification_click.dart", "Dart", 11, 0, 0, 11
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\services\notifications_counter\counter.dart", "Dart", 0, 1, 0, 1
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\services\pusher.dart", "Dart", 0, -89, -13, -102
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\services\rest_api\api_service.dart", "Dart", 1, 0, 0, 1
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\services\rest_api\constants\end_points.dart", "Dart", 5, 0, 0, 5
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\services\rest_api\handlers\success_handler.dart", "Dart", 2, 0, 0, 2
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\services\rest_api\models\exceptions.dart", "Dart", 2, 0, 1, 3
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\services\rest_api\utilitis\parser.dart", "Dart", -10, 0, 3, -7
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\services\share\share.dart", "Dart", 44, 0, 6, 50
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\style\assets\gen\assets.gen.dart", "Dart", 21, 9, 9, 39
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\core\utils\social_urls.dart", "Dart", 16, 0, 4, 20
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\activitiy_details\controller.dart", "Dart", -205, -6, -31, -242
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\activitiy_details\index.dart", "Dart", 12, 0, 0, 12
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\activitiy_details\models\activity.dart", "Dart", -58, 0, -2, -60
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\activitiy_details\models\price_type.dart", "Dart", -15, 0, -2, -17
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\activitiy_details\models\set.dart", "Dart", -11, 0, -3, -14
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\activitiy_details\models\type.dart", "Dart", -41, 0, -3, -44
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\activitiy_details\widgets\calendar.dart", "Dart", -142, 0, -5, -147
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\activitiy_details\widgets\header.dart", "Dart", 11, 0, 0, 11
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\activitiy_details\widgets\open_sessions.dart", "Dart", -109, 0, -4, -113
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\activitiy_details\widgets\sets.dart", "Dart", -80, 0, 0, -80
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\activitiy_details\widgets\specific_days_sessions.dart", "Dart", -85, 0, -4, -89
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\main_activity\bindings.dart", "Dart", 10, 0, 4, 14
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\main_activity\controller.dart", "Dart", 26, 0, 6, 32
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\main_activity\index.dart", "Dart", 34, 0, 4, 38
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\main_activity\models\nav.dart", "Dart", 4, 0, 2, 6
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\main_activity\models\sub_activity_data.dart", "Dart", 98, 0, 6, 104
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\main_activity\widgets\card.dart", "Dart", 271, 3, 10, 284
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\main_activity\widgets\card_body.dart", "Dart", 42, 0, 4, 46
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\usecases\booking\booking.dart", "Dart", 168, 8, 25, 201
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\usecases\open_sessions_calendar\controller.dart", "Dart", 79, 5, 17, 101
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\usecases\open_sessions_calendar\index.dart", "Dart", 22, 0, 4, 26
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\usecases\open_sessions_calendar\widgets\calendar.dart", "Dart", 141, 0, 5, 146
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\usecases\open_sessions_calendar\widgets\open_sessions.dart", "Dart", 113, 0, 4, 117
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\usecases\points_selection\points_selection.dart", "Dart", 109, 0, 5, 114
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\usecases\specific_days_calendar\controller.dart", "Dart", 76, 1, 14, 91
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\usecases\specific_days_calendar\index.dart", "Dart", 31, 0, 4, 35
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\usecases\specific_days_calendar\widgets\calendar.dart", "Dart", 141, 0, 5, 146
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\usecases\specific_days_calendar\widgets\specific_days_sessions.dart", "Dart", 87, 0, 4, 91
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\widgets\activity_card.dart", "Dart", 1, -17, 0, -16
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\widgets\periodic_time_info.dart", "Dart", 4, 0, 0, 4
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\widgets\seasonal_activity_card.dart", "Dart", 4, 0, 0, 4
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\activities\widgets\sets.dart", "Dart", 106, 0, 3, 109
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\auth\create_account\controller.dart", "Dart", 3, 0, 0, 3
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\auth\create_account\widgets\form.dart", "Dart", 11, 0, 0, 11
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\auth\login\controller.dart", "Dart", 12, 1, 2, 15
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\auth\login\index.dart", "Dart", 7, 0, 0, 7
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\auth\signup\bindings.dart", "Dart", -10, 0, -3, -13
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\auth\signup\controller.dart", "Dart", -22, 0, -6, -28
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\auth\signup\index.dart", "Dart", -69, 0, -4, -73
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\auth\signup\models\nav.dart", "Dart", -4, 0, -1, -5
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\auth\verification\index.dart", "Dart", 1, 0, 0, 1
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\boarding\index.dart", "Dart", 5, 0, 0, 5
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\books\widgets\book_card.dart", "Dart", 103, 0, 5, 108
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\categories\categories\widgets\category_card.dart", "Dart", 1, 0, 0, 1
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\categories\category\controller.dart", "Dart", 15, 0, 1, 16
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\categories\category\widgets\sub_categories.dart", "Dart", 1, 0, 0, 1
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\center\centers_on_map\controller.dart", "Dart", 30, 0, 6, 36
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\center\centers_on_map\index.dart", "Dart", 50, 1, 4, 55
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\center\centers_on_map\models\center_location.dart", "Dart", 25, 0, 4, 29
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\center\profile\index.dart", "Dart", 8, 0, 0, 8
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\center\profile\widgets\branches.dart", "Dart", 4, 0, 0, 4
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\center\profile\widgets\header.dart", "Dart", 1, 0, 0, 1
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\chat\rooms\controller.dart", "Dart", 5, 3, 1, 9
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\filters\controller.dart", "Dart", 3, 0, 0, 3
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\filters\models\filters_data.dart", "Dart", 1, 0, 0, 1
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\filters\widgets\subcategories.dart", "Dart", 5, 0, 0, 5
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\home\controller.dart", "Dart", 22, 2, 1, 25
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\home\index.dart", "Dart", 17, -1, 0, 16
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\home\models\ad.dart", "Dart", 18, 0, 2, 20
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\home\widgets\about.dart", "Dart", 76, 0, 3, 79
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\home\widgets\categories.dart", "Dart", 95, 0, 4, 99
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\home\widgets\centers_list.dart", "Dart", 9, 0, 0, 9
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\home\widgets\seasonal_activities_list.dart", "Dart", 5, 0, 0, 5
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\home\widgets\text_slider.dart", "Dart", 66, 0, 4, 70
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\main\widgets\drawer.dart", "Dart", 21, -2, 1, 20
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\main\widgets\notification_switch.dart", "Dart", 43, 0, 6, 49
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\notifications\controller.dart", "Dart", 7, 0, 0, 7
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\profile\child_profile\index.dart", "Dart", 2, 0, 0, 2
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\profile\edit_profile\widgets\form.dart", "Dart", 3, 0, 0, 3
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\profile\my_profile\index.dart", "Dart", 24, 0, 0, 24
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\profile\my_profile\models\profile.dart", "Dart", 2, 0, 1, 3
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\profile\my_profile\widgets\children.dart", "Dart", 2, 0, 0, 2
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\search\controller.dart", "Dart", 63, 0, 11, 74
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\search\index.dart", "Dart", 46, 1, 4, 51
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\search\widgets\actitvities.dart", "Dart", 59, 0, 4, 63
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\search\widgets\centers.dart", "Dart", 60, 0, 4, 64
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\settings\invitation\controller.dart", "Dart", 40, 0, 11, 51
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\settings\invitation\index.dart", "Dart", 67, 1, 4, 72
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\settings\privacy\controller.dart", "Dart", 17, 0, 2, 19
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\settings\privacy\index.dart", "Dart", 11, -1, 0, 10
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\settings\usecases\points\controller.dart", "Dart", 30, 0, 5, 35
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\settings\usecases\points\index.dart", "Dart", 126, 1, 4, 131
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\settings\usecases\points\models\point_setting.dart", "Dart", 18, 0, 3, 21
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\settings\usecases\support\index.dart", "Dart", 15, 0, 0, 15
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\usecases\gender\widgets\gender_avatar.dart", "Dart", 76, 0, 4, 80
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\usecases\gender\widgets\gender_tile.dart", "Dart", -13, 0, 0, -13
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\widgets\available_bubble_cilpper.dart", "Dart", -68, -1, -7, -76
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\widgets\backgrounds\auth_bg.dart", "Dart", 1, 0, 0, 1
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\widgets\general_componenets\buttons\elevated_button.dart", "Dart", 3, 0, 0, 3
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\features\widgets\general_componenets\buttons\outlined_button.dart", "Dart", -2, 0, 0, -2
"d:\work\ixCoders\current\go_pal\go_pal_flutter\lib\main.dart", "Dart", -10, -1, -2, -13
"Total", "-", 2402, -158, 178, 2422