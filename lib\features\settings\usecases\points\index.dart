import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:gap/gap.dart';
import 'package:get/get.dart' hide Trans;
import 'package:gopal/core/localization/strings.dart';
import 'package:gopal/core/style/assets/gen/assets.gen.dart';
import 'package:gopal/core/style/repo.dart';
import 'package:gopal/core/widgets/error_widget.dart';
import 'package:gopal/core/widgets/loading.dart';
import 'package:gopal/features/settings/usecases/points/models/point_setting.dart';

import 'controller.dart';

class PointsGuidBottomSheet extends StatelessWidget {
  const PointsGuidBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    // ignore: unused_local_variable
    PointsGuidController controller = Get.put(PointsGuidController());
    return SizedBox(
      height: Get.height * .8,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 40),
            padding: const EdgeInsets.fromLTRB(16, 75, 16, 16),
            width: double.infinity,
            decoration: const BoxDecoration(
              color: StyleRepo.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(45),
                topRight: Radius.circular(45),
              ),
            ),
            child: Column(
              children: [
                Text(
                  tr(LocaleKeys.gifting_system),
                  style: context.textTheme.headlineMedium,
                ),
                Expanded(
                  child: Obx(
                    () {
                      if (controller.settings.loading) {
                        return const Center(child: LoadingWidget());
                      } else if (controller.settings.hasError) {
                        return Center(
                            child: AppErrorWidget(
                          error: controller.settings.error!,
                          withScrolling: false,
                        ));
                      } else {
                        List<PointsSetting> settings =
                            controller.settings.value!;
                        return Scrollbar(
                          thumbVisibility: true,
                          trackVisibility: true,
                          child: ListView.separated(
                            padding: const EdgeInsets.symmetric(vertical: 12),
                            itemCount: controller.settings.valueLength,
                            separatorBuilder: (_, __) => const Gap(12),
                            itemBuilder: (context, index) {
                              return Row(
                                children: [
                                  SizedBox(
                                    height: 35,
                                    width: 35,
                                    child: settings[index].icon.isEmpty
                                        ? Assets.icons.gift.svg()
                                        : SvgPicture.network(
                                            settings[index].icon,
                                          ),
                                  ),
                                  const Gap(8),
                                  Expanded(
                                    child: RichText(
                                      text: TextSpan(
                                        style: context.textTheme.titleLarge,
                                        children: [
                                          TextSpan(
                                            text: "${LocaleKeys.n_points.plural(
                                              settings[index].value,
                                              args: [
                                                "${settings[index].value}"
                                              ],
                                            )} ",
                                            style: const TextStyle(
                                              color: StyleRepo.blueViolet,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                          TextSpan(
                                              text: settings[index]
                                                      .text
                                                      .isNotEmpty
                                                  ? settings[index].text
                                                  : "When registering the account for the first time")
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: StyleRepo.white,
              border: Border.all(color: StyleRepo.blueViolet),
            ),
            child: Assets.icons.gift.svg(),
          ),
        ],
      ),
    );
  }
}
