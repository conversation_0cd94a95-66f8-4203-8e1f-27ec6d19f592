import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:gopal/core/config/app_builder.dart';

import '../firebase_messaging.dart';

class FcmTokenWidget extends StatelessWidget {
  FcmTokenWidget({super.key});

  final Rx<String> _token = "".obs;
  String get token => _token.value;
  set token(String value) => _token.value = value;

  @override
  Widget build(BuildContext context) {
    if (!Get.find<AppBuilder>().isDev) {
      return const SizedBox();
    }
    FirebaseMessagingService.getToken().then((value) => token = value!);
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Card(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Directionality(
            textDirection: TextDirection.ltr,
            child: Column(
              children: [
                const Text("For test pushing notification, copy that:\n"),
                Obx(
                  () => SelectableText(token),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
